const { chromium } = require('playwright');

async function debugSleepMode() {
  console.log('🔍 开始调试睡眠模式渲染问题...');
  
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  
  const page = await context.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    console.log('🖥️  浏览器控制台:', msg.text());
  });
  
  try {
    // 1. 导航到页面
    console.log('📍 步骤 1: 导航到音频页面');
    await page.goto('http://localhost:3000/zh/sounds');
    await page.waitForTimeout(2000);
    
    // 2. 检查页面上的按钮
    console.log('📍 步骤 2: 检查页面上的按钮');
    const allButtons = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      return buttons.map(btn => ({
        textContent: btn.textContent?.trim(),
        className: btn.className,
        testId: btn.getAttribute('data-testid'),
        ariaLabel: btn.getAttribute('aria-label')
      }));
    });

    console.log('🔍 页面上的所有按钮:');
    allButtons.forEach((btn, index) => {
      console.log(`  ${index + 1}. "${btn.textContent}" (testId: ${btn.testId}, class: ${btn.className})`);
    });

    // 3. 播放音频 - 查找播放按钮（图标按钮）
    console.log('📍 步骤 3: 播放音频');

    // 查找包含播放图标的按钮
    const playButton = await page.locator('button').filter({
      has: page.locator('svg path[d="M8 5v14l11-7z"]')
    }).first();

    if (await playButton.count() > 0) {
      console.log('✅ 找到播放按钮，点击中...');
      await playButton.click();
      await page.waitForTimeout(3000);
    } else {
      console.log('⚠️  播放按钮未找到，尝试点击第一个可见按钮');
      const visibleButtons = await page.locator('button:visible').all();
      if (visibleButtons.length > 0) {
        await visibleButtons[0].click();
        await page.waitForTimeout(3000);
      }
    }
    
    // 4. 切换到睡眠模式
    console.log('📍 步骤 4: 切换到睡眠模式');

    // 等待播放器出现
    await page.waitForSelector('.fixed.bottom-0', { timeout: 5000 });

    // 查找睡眠模式按钮
    const sleepButton = await page.locator('button[data-testid="sleep-mode-button"]');
    if (await sleepButton.count() > 0) {
      console.log('✅ 找到睡眠模式按钮，点击中...');
      await sleepButton.click();
      await page.waitForTimeout(3000);
    } else {
      console.log('⚠️  睡眠模式按钮未找到，查找月亮图标按钮');
      // 查找包含月亮图标的按钮
      const moonButton = await page.locator('button').filter({
        has: page.locator('svg')
      }).filter({
        hasText: /睡眠|sleep/i
      }).first();

      if (await moonButton.count() > 0) {
        await moonButton.click();
        await page.waitForTimeout(3000);
      } else {
        console.log('⚠️  睡眠模式按钮未找到，跳过此步骤');
      }
    }
    
    // 5. 检查当前DOM结构
    console.log('📍 步骤 5: 检查DOM结构');
    const bodyHTML = await page.evaluate(() => {
      return document.body.innerHTML;
    });
    
    // 检查是否有SleepModePlayer
    const hasSleepModePlayer = bodyHTML.includes('sleep-mode-player');
    console.log('🔍 是否包含 sleep-mode-player 类:', hasSleepModePlayer);
    
    // 检查是否有占位符
    const hasPlaceholder = bodyHTML.includes('睡眠模式界面将在第三阶段实现');
    console.log('🔍 是否包含占位符文本:', hasPlaceholder);
    
    // 检查AudioPlayerProvider的渲染
    const audioPlayerProvider = await page.evaluate(() => {
      const providers = document.querySelectorAll('[class*="AudioPlayerProvider"], [data-component="AudioPlayerProvider"]');
      return {
        count: providers.length,
        hasProvider: providers.length > 0
      };
    });
    console.log('🔍 AudioPlayerProvider 数量:', audioPlayerProvider);
    
    // 检查睡眠模式状态
    const sleepModeState = await page.evaluate(() => {
      // 尝试访问Zustand store
      if (window.__ZUSTAND_STORE__) {
        return window.__ZUSTAND_STORE__.getState();
      }
      return null;
    });
    console.log('🔍 睡眠模式状态:', sleepModeState);
    
    // 检查所有固定定位的元素
    const fixedElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*')).filter(el => {
        const style = window.getComputedStyle(el);
        return style.position === 'fixed';
      });
      
      return elements.map(el => ({
        tagName: el.tagName,
        className: el.className,
        textContent: el.textContent?.substring(0, 100),
        zIndex: window.getComputedStyle(el).zIndex
      }));
    });
    
    console.log('🔍 所有固定定位元素:');
    fixedElements.forEach((el, index) => {
      console.log(`  ${index + 1}. ${el.tagName}.${el.className} (z-index: ${el.zIndex})`);
      if (el.textContent) {
        console.log(`     内容: ${el.textContent}`);
      }
    });
    
    // 截图保存
    await page.screenshot({ path: 'sleep-mode-debug.png', fullPage: true });
    console.log('📸 截图已保存: sleep-mode-debug.png');
    
    // 等待用户观察
    console.log('⏳ 等待 10 秒供观察...');
    await page.waitForTimeout(10000);
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  } finally {
    await browser.close();
  }
}

debugSleepMode().catch(console.error);
