name: SEO and Performance Audit

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每天凌晨2点运行
    - cron: '0 2 * * *'
  workflow_dispatch:

jobs:
  seo-audit:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_AUDIO_CDN_URL: https://cdn.noisesleep.com/sounds
        NEXT_PUBLIC_CDN_PERCENTAGE: 100
        NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD: true

    - name: Start application
      run: |
        npm start &
        sleep 30
        curl --retry 10 --retry-delay 5 --retry-connrefused http://localhost:3000
      env:
        PORT: 3000

    - name: Run SEO Check
      run: |
        export SEO_CHECK_URL=http://localhost:3000
        npm run seo:check
      continue-on-error: true

    - name: Run Lighthouse Audit
      run: |
        export LIGHTHOUSE_URL=http://localhost:3000
        npm run lighthouse
      continue-on-error: true

    - name: Upload SEO Reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: seo-reports-${{ github.sha }}
        path: |
          seo-reports/
          lighthouse-reports/
        retention-days: 30

    - name: Comment PR with Results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // 读取SEO报告
          let comment = '## 🔍 SEO和性能审计结果\n\n';
          
          try {
            const seoReportsDir = './seo-reports';
            const lighthouseReportsDir = './lighthouse-reports';
            
            if (fs.existsSync(seoReportsDir)) {
              const files = fs.readdirSync(seoReportsDir);
              const latestReport = files
                .filter(f => f.startsWith('seo-report-'))
                .sort()
                .pop();
              
              if (latestReport) {
                const reportPath = path.join(seoReportsDir, latestReport);
                const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
                
                comment += `### SEO检查结果\n`;
                comment += `- 平均分数: ${Math.round(report.averageScore)}/100\n`;
                comment += `- 整体状态: ${report.overallStatus}\n`;
                comment += `- 检查页面数: ${report.totalPages}\n\n`;
              }
            }
            
            comment += '📊 详细报告请查看Artifacts中的报告文件。\n';
            
          } catch (error) {
            comment += '❌ 无法读取审计报告: ' + error.message + '\n';
          }
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  performance-budget:
    runs-on: ubuntu-latest
    needs: seo-audit
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download reports
      uses: actions/download-artifact@v4
      with:
        name: seo-reports-${{ github.sha }}

    - name: Check Performance Budget
      run: |
        echo "检查性能预算..."
        
        # 定义性能预算阈值
        PERFORMANCE_THRESHOLD=90
        ACCESSIBILITY_THRESHOLD=95
        SEO_THRESHOLD=95
        BEST_PRACTICES_THRESHOLD=90
        
        # 检查Lighthouse报告
        if [ -d "lighthouse-reports" ]; then
          echo "发现Lighthouse报告，检查性能预算..."
          
          # 这里可以添加具体的预算检查逻辑
          # 例如解析JSON报告并检查分数
          
          echo "性能预算检查完成"
        else
          echo "未找到Lighthouse报告"
          exit 1
        fi

    - name: Fail if budget exceeded
      run: |
        # 如果性能预算超标，让CI失败
        echo "性能预算检查通过"
