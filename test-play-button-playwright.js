const { chromium } = require('playwright');

(async () => {
  try {
    console.log('🚀 启动 Playwright 浏览器...');
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // 监听控制台消息
    const logs = [];
    page.on('console', msg => {
      const text = msg.text();
      console.log('🖥️  浏览器控制台:', text);
      logs.push(text);
    });

    // 监听页面错误
    page.on('pageerror', error => {
      console.log('❌ 页面错误:', error.message);
    });

    // 监听请求失败
    page.on('requestfailed', request => {
      console.log('🚫 请求失败:', request.url(), request.failure()?.errorText);
    });
    
    console.log('🌐 导航到音频页面...');
    await page.goto('http://localhost:3000/zh/sounds');
    await page.waitForTimeout(2000);
    
    console.log('🔍 执行播放按钮点击测试...');
    const result = await page.evaluate(() => {
      // 查找第一个播放按钮
      const playButtons = document.querySelectorAll('button[aria-label="播放"]');
      console.log('🎯 找到播放按钮数量:', playButtons.length);

      if (playButtons.length > 0) {
        const firstButton = playButtons[0];
        console.log('🎵 第一个播放按钮:', firstButton);
        console.log('🎵 按钮父元素:', firstButton.parentElement);

        // 检查按钮是否有事件监听器
        const hasClickHandler = firstButton.onclick !== null;
        console.log('🔍 按钮是否有 onclick 处理器:', hasClickHandler);

        // 检查按钮是否被禁用
        const isDisabled = firstButton.disabled;
        console.log('🔍 按钮是否被禁用:', isDisabled);

        // 检查按钮的样式
        const computedStyle = window.getComputedStyle(firstButton);
        console.log('🔍 按钮是否可见:', computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden');

        // 尝试直接调用 click() 方法
        console.log('⚡ 准备触发点击事件...');
        firstButton.click();
        console.log('✅ 点击事件已触发');

        // 等待一下，看看是否有异步效果
        setTimeout(() => {
          console.log('🔍 延迟检查：点击后的状态');
        }, 100);

        return {
          success: true,
          buttonCount: playButtons.length,
          hasClickHandler,
          isDisabled,
          isVisible: computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden'
        };
      } else {
        console.log('❌ 未找到播放按钮');

        // 列出所有按钮
        const allButtons = document.querySelectorAll('button');
        console.log('📋 页面上的所有按钮数量:', allButtons.length);
        for (let i = 0; i < Math.min(allButtons.length, 5); i++) {
          const btn = allButtons[i];
          console.log(`按钮 ${i+1}: "${btn.textContent?.trim()}" (aria-label: "${btn.getAttribute('aria-label')}")`);
        }

        return { success: false, buttonCount: 0 };
      }
    });
    
    console.log('📊 测试结果:', result);
    
    // 等待一下看看是否有音频播放器出现
    console.log('⏳ 等待音频播放器出现...');
    await page.waitForTimeout(2000);

    // 检查是否有预期的控制台消息出现
    const hasExpectedLogs = logs.some(log =>
      log.includes('PlayButton handleClick 被调用') ||
      log.includes('AudioCard handlePlay 被调用') ||
      log.includes('播放音频被调用')
    );

    console.log('📝 是否有预期的控制台消息:', hasExpectedLogs);

    const hasAudioPlayer = await page.evaluate(() => {
      // 尝试多种选择器查找音频播放器
      const selectors = [
        '.audio-player',
        '[data-testid="audio-player"]',
        '.fixed.bottom-0', // 可能的底部固定播放器
        '.player-container'
      ];

      let foundPlayer = null;
      for (const selector of selectors) {
        const element = document.querySelector(selector);
        if (element) {
          foundPlayer = element;
          console.log(`🎵 找到音频播放器 (${selector}):`, element);
          break;
        }
      }

      if (!foundPlayer) {
        console.log('❌ 未找到音频播放器');
        // 列出页面上的一些可能相关的元素
        const possibleElements = document.querySelectorAll('[class*="player"], [class*="audio"], [class*="fixed"]');
        console.log('🔍 可能相关的元素数量:', possibleElements.length);
        for (let i = 0; i < Math.min(possibleElements.length, 3); i++) {
          const el = possibleElements[i];
          console.log(`元素 ${i+1}: ${el.tagName} (class: "${el.className}")`);
        }
      }

      const isVisible = foundPlayer && foundPlayer.style.display !== 'none';
      console.log('👁️  音频播放器是否可见:', isVisible);
      return isVisible;
    });
    
    console.log('🎵 音频播放器是否出现:', hasAudioPlayer);
    
    // 收集所有控制台消息
    console.log('📝 收集到的控制台消息数量:', logs.length);
    logs.forEach((log, index) => {
      console.log(`消息 ${index + 1}: ${log}`);
    });
    
    await browser.close();
    console.log('✅ 测试完成');
  } catch (error) {
    console.error('❌ 错误:', error.message);
  }
})();
