# NoiseSleep环境变量配置示例
# 复制此文件为.env.local并填入实际值

# ===== 基础配置 =====
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://noisesleep.com
NEXT_PUBLIC_CDN_URL=https://cdn.noisesleep.com

# ===== Cloudflare配置 =====
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id_here
CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id_here

# ===== R2存储配置 =====
R2_BUCKET_NAME=noisesleep-audio
R2_ACCESS_KEY_ID=your_r2_access_key_here
R2_SECRET_ACCESS_KEY=your_r2_secret_key_here
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com

# ===== 数据库配置 =====
DATABASE_URL=your_d1_database_url_here
KV_NAMESPACE_ID=your_kv_namespace_id_here

# ===== 分析和监控 =====
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_ANALYTICS_ENABLED=true

# ===== 安全配置 =====
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=https://noisesleep.com

# ===== 音频处理配置 =====
AUDIO_QUALITY=high
AUDIO_COMPRESSION_LEVEL=6
MAX_AUDIO_FILE_SIZE=********

# ===== 多语言配置 =====
DEFAULT_LOCALE=en
SUPPORTED_LOCALES=en,zh

# ===== 缓存配置 =====
CACHE_TTL=3600
AUDIO_CACHE_TTL=2592000

# ===== 开发环境配置 =====
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_DEBUG_AUDIO=false
