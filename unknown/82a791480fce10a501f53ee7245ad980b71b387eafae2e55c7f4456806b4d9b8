/**
 * 睡眠功能测试套件
 * 测试睡眠定时器和睡眠模式的核心功能
 */

import { renderHook, act } from '@testing-library/react';
import { useSleepTimer } from '@/hooks/useSleepTimer';
import { useAudioStore } from '@/store/audioStore';

// Mock Howler.js
jest.mock('howler', () => ({
  Howl: jest.fn().mockImplementation(() => ({
    play: jest.fn(),
    pause: jest.fn(),
    stop: jest.fn(),
    volume: jest.fn(),
    fade: jest.fn(),
    unload: jest.fn(),
  })),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
}));

describe('睡眠定时器功能测试', () => {
  beforeEach(() => {
    // 启用定时器模拟
    jest.useFakeTimers();

    // 重置 store 状态
    useAudioStore.getState().setTimerActive(false);
    useAudioStore.getState().setTimerDuration(0);
    useAudioStore.getState().setTimerRemainingTime(0);
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('应该能够启动定时器', () => {
    const { result } = renderHook(() => useSleepTimer());
    
    act(() => {
      result.current.startTimer(30); // 30分钟
    });

    const timerState = useAudioStore.getState().timer;
    expect(timerState.isActive).toBe(true);
    expect(timerState.duration).toBe(30);
    expect(timerState.remainingTime).toBe(30 * 60); // 30分钟 = 1800秒
  });

  it('应该能够停止定时器', () => {
    const { result } = renderHook(() => useSleepTimer());
    
    // 先启动定时器
    act(() => {
      result.current.startTimer(15);
    });

    // 然后停止定时器
    act(() => {
      result.current.stopTimer();
    });

    const timerState = useAudioStore.getState().timer;
    expect(timerState.isActive).toBe(false);
  });

  it('应该能够设置预设定时器时长', () => {
    const { result } = renderHook(() => useSleepTimer());
    
    const presetDurations = [15, 30, 60, 120];
    
    presetDurations.forEach(duration => {
      act(() => {
        result.current.setPresetTimer(duration);
      });

      const timerState = useAudioStore.getState().timer;
      expect(timerState.duration).toBe(duration);
      expect(timerState.remainingTime).toBe(duration * 60);
    });
  });

  it('应该能够设置自定义定时器时长', () => {
    const { result } = renderHook(() => useSleepTimer());
    
    act(() => {
      result.current.setCustomTimer(45); // 45分钟
    });

    const timerState = useAudioStore.getState().timer;
    expect(timerState.duration).toBe(45);
    expect(timerState.remainingTime).toBe(45 * 60);
  });

  it('应该正确格式化剩余时间显示', () => {
    const { result } = renderHook(() => useSleepTimer());
    
    // 测试不同的时间格式
    expect(result.current.formatRemainingTime(3661)).toBe('1:01:01'); // 1小时1分1秒
    expect(result.current.formatRemainingTime(3600)).toBe('1:00:00'); // 1小时
    expect(result.current.formatRemainingTime(61)).toBe('1:01'); // 1分1秒
    expect(result.current.formatRemainingTime(60)).toBe('1:00'); // 1分钟
    expect(result.current.formatRemainingTime(30)).toBe('0:30'); // 30秒
    expect(result.current.formatRemainingTime(0)).toBe('0:00'); // 0秒
  });

  it('定时器倒计时应该正常工作', () => {
    const { result } = renderHook(() => useSleepTimer());

    // 设置播放状态为true，确保定时器能正常运行
    act(() => {
      useAudioStore.getState().updatePlayState({ isPlaying: true });
    });

    act(() => {
      result.current.startTimer(1); // 1分钟
    });

    // 验证初始状态
    expect(useAudioStore.getState().timer.remainingTime).toBe(60); // 60秒
    expect(useAudioStore.getState().timer.isActive).toBe(true);

    // 模拟时间流逝30秒
    act(() => {
      jest.advanceTimersByTime(30000); // 30秒
    });

    const timerState = useAudioStore.getState().timer;
    expect(timerState.remainingTime).toBe(30); // 剩余30秒
  });
});

describe('睡眠模式功能测试', () => {
  beforeEach(() => {
    // 重置 store 状态
    useAudioStore.getState().setPlayerMode('standard');
    useAudioStore.getState().setPlayerVisible(false);
  });

  it('应该能够切换到睡眠模式', () => {
    const store = useAudioStore.getState();
    
    act(() => {
      store.setPlayerMode('sleep');
    });

    expect(useAudioStore.getState().playerUI.mode).toBe('sleep');
  });

  it('应该能够从睡眠模式切换回标准模式', () => {
    const store = useAudioStore.getState();
    
    // 先切换到睡眠模式
    act(() => {
      store.setPlayerMode('sleep');
    });

    // 再切换回标准模式
    act(() => {
      store.setPlayerMode('standard');
    });

    expect(useAudioStore.getState().playerUI.mode).toBe('standard');
  });

  it('应该能够显示和隐藏定时器面板', () => {
    const store = useAudioStore.getState();
    
    act(() => {
      store.setTimerPanelVisible(true);
    });

    expect(useAudioStore.getState().playerUI.showTimerPanel).toBe(true);

    act(() => {
      store.setTimerPanelVisible(false);
    });

    expect(useAudioStore.getState().playerUI.showTimerPanel).toBe(false);
  });

  it('应该能够显示和隐藏混音面板', () => {
    const store = useAudioStore.getState();
    
    act(() => {
      store.setMixingPanelVisible(true);
    });

    expect(useAudioStore.getState().playerUI.showMixingPanel).toBe(true);

    act(() => {
      store.setMixingPanelVisible(false);
    });

    expect(useAudioStore.getState().playerUI.showMixingPanel).toBe(false);
  });
});

describe('混音功能集成测试', () => {
  beforeEach(() => {
    // 清空混音频道
    const store = useAudioStore.getState();
    store.mixingChannels.forEach(channel => {
      store.removeMixingChannel(channel.id);
    });
  });

  it('应该能够添加混音频道', () => {
    const store = useAudioStore.getState();
    const mockAudio = {
      id: 'test-audio',
      title: { zh: '测试音频', en: 'Test Audio' },
      filename: 'test.mp3',
      category: 'nature',
    };

    act(() => {
      store.addMixingChannel(mockAudio);
    });

    const channels = useAudioStore.getState().mixingChannels;
    expect(channels).toHaveLength(1);
    expect(channels[0].soundId).toBe('test-audio');
  });

  it('应该限制最大混音频道数量', () => {
    const store = useAudioStore.getState();
    const maxChannels = store.maxChannels;
    
    // 添加超过最大数量的频道
    for (let i = 0; i <= maxChannels; i++) {
      const mockAudio = {
        id: `test-audio-${i}`,
        title: { zh: `测试音频${i}`, en: `Test Audio ${i}` },
        filename: `test${i}.mp3`,
        category: 'nature',
      };
      
      act(() => {
        store.addMixingChannel(mockAudio);
      });
    }

    const channels = useAudioStore.getState().mixingChannels;
    expect(channels).toHaveLength(maxChannels);
  });

  it('应该能够移除混音频道', () => {
    const store = useAudioStore.getState();
    const mockAudio = {
      id: 'test-audio',
      title: { zh: '测试音频', en: 'Test Audio' },
      filename: 'test.mp3',
      category: 'nature',
    };

    // 先添加频道
    act(() => {
      store.addMixingChannel(mockAudio);
    });

    const channelId = useAudioStore.getState().mixingChannels[0].id;

    // 然后移除频道
    act(() => {
      store.removeMixingChannel(channelId);
    });

    const channels = useAudioStore.getState().mixingChannels;
    expect(channels).toHaveLength(0);
  });
});
