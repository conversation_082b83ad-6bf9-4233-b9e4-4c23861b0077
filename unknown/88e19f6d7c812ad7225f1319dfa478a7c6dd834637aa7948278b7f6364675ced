'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';

interface AmbientInfoProps {
  className?: string;
}

export function AmbientInfo({ className = '' }: AmbientInfoProps) {
  const t = useTranslations('sleepMode');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [sleepTip, setSleepTip] = useState('');

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 根据时间生成睡眠建议
  useEffect(() => {
    const hour = currentTime.getHours();
    let tip = '';

    if (hour >= 22 || hour < 6) {
      tip = t('tips.nightTime');
    } else if (hour >= 6 && hour < 12) {
      tip = t('tips.morning');
    } else if (hour >= 12 && hour < 18) {
      tip = t('tips.afternoon');
    } else {
      tip = t('tips.evening');
    }

    setSleepTip(tip);
  }, [currentTime, t]);

  // 格式化时间显示
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  // 格式化日期显示
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString([], {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });
  };

  // 获取时间段问候语
  const getGreeting = (): string => {
    const hour = currentTime.getHours();
    
    if (hour >= 5 && hour < 12) {
      return t('greetings.morning');
    } else if (hour >= 12 && hour < 17) {
      return t('greetings.afternoon');
    } else if (hour >= 17 && hour < 22) {
      return t('greetings.evening');
    } else {
      return t('greetings.night');
    }
  };

  // 获取月相信息（简化版）
  const getMoonPhase = (): string => {
    const phases = [
      t('moonPhases.newMoon') || 'New Moon',
      t('moonPhases.waxingCrescent') || 'Waxing Crescent',
      t('moonPhases.firstQuarter') || 'First Quarter',
      t('moonPhases.waxingGibbous') || 'Waxing Gibbous',
      t('moonPhases.fullMoon') || 'Full Moon',
      t('moonPhases.waningGibbous') || 'Waning Gibbous',
      t('moonPhases.lastQuarter') || 'Last Quarter',
      t('moonPhases.waningCrescent') || 'Waning Crescent'
    ];

    // 简化的月相计算（基于日期）
    const dayOfMonth = currentTime.getDate();
    const phaseIndex = Math.floor((dayOfMonth / 30) * 8) % 8;
    return phases[phaseIndex] as string;
  };

  return (
    <motion.div 
      className={`ambient-info ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
    >
      <div className="space-y-6 text-center">
        {/* 问候语 */}
        <div>
          <h3 className="text-lg font-light text-gray-700 dark:text-gray-300">
            {getGreeting()}
          </h3>
        </div>

        {/* 时间显示 */}
        <div className="space-y-2">
          <div className="text-3xl font-light text-gray-900 dark:text-white tabular-nums">
            {formatTime(currentTime)}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {formatDate(currentTime)}
          </div>
        </div>

        {/* 环境信息 */}
        <div className="grid grid-cols-2 gap-4 max-w-xs mx-auto text-sm">
          {/* 月相 */}
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400 mb-1">
              {t('moonPhase')}
            </div>
            <div className="text-gray-900 dark:text-white">
              {getMoonPhase()}
            </div>
          </div>
          
          {/* 睡眠质量预测 */}
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400 mb-1">
              {t('sleepQuality')}
            </div>
            <div className="text-green-600 dark:text-green-400">
              {t('optimal')}
            </div>
          </div>
        </div>

        {/* 睡眠建议 */}
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 max-w-md mx-auto">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-1">
              <svg className="w-5 h-5 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {t('sleepTip')}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {sleepTip}
              </div>
            </div>
          </div>
        </div>

        {/* 放松提示 */}
        <div className="text-xs text-gray-400 dark:text-gray-500 max-w-sm mx-auto">
          {t('relaxationHint')}
        </div>
      </div>
    </motion.div>
  );
}
