# 🌐 NoiseSleep 音频文件 CDN 部署方案

**项目**: NoiseSleep Phase 1 MVP  
**部署目标**: Cloudflare CDN + R2 存储  
**音频文件**: 73个文件，8大分类  
**域名**: noisesleep.com (已绑定Cloudflare)  
**生成时间**: 2025年07月07日 23:00:00

---

## 📊 当前状态分析

### 🎵 音频文件现状
**本地存储位置**: `public/sounds/`  
**文件统计**:
- **Rain**: 5个文件 (gentle-rain.wav, heavy-rain.mp3/wav, light-rain.mp3, rain-drops.wav)
- **Nature**: 3个文件 (campfire.mp3, forest-birds.wav, wind-leaves.wav)  
- **Ocean**: 1个文件 (ocean-waves.wav)
- **Noise**: 1个文件 (pink-noise.wav)
- **White-noise**: 1个文件 (pink-noise.wav)

**文件格式**: MP3 + WAV 混合格式  
**预估总大小**: 200-500MB (基于1小时音频文件)

### 🔧 代码配置现状
**音频路径配置**: `useAudioPlayer.ts` 第79行
```typescript
const audioUrl = `/Sounds/${folderName}/${sound.filename}`;
```

**元数据配置**: `src/data/audioData.ts` - 73个音频文件完整元数据

---

## 🚀 CDN 部署方案

### 方案选择: Cloudflare R2 + CDN
**推荐理由**:
- ✅ **成本优势**: R2存储费用比S3低90%
- ✅ **全球CDN**: 自动全球分发，无额外费用
- ✅ **域名集成**: 与noisesleep.com无缝集成
- ✅ **高性能**: 边缘缓存，低延迟访问

---

## 📋 详细部署步骤

### 第一步: Cloudflare R2 存储桶创建

#### 1.1 登录Cloudflare Dashboard
```bash
# 访问 https://dash.cloudflare.com
# 选择你的账户 → R2 Object Storage
```

#### 1.2 创建存储桶
```bash
存储桶名称: noisesleep-audio
区域: 自动 (Cloudflare会自动选择最优区域)
公共访问: 启用 (用于CDN分发)
```

#### 1.3 配置自定义域名
```bash
# 在R2存储桶设置中
自定义域名: cdn.noisesleep.com
SSL/TLS: 完全(严格)
缓存级别: 标准
```

### 第二步: 音频文件目录结构设计

#### 2.1 CDN目录结构
```
cdn.noisesleep.com/sounds/
├── rain/
│   ├── gentle-rain.wav
│   ├── heavy-rain.mp3
│   ├── heavy-rain.wav
│   ├── light-rain.mp3
│   └── rain-drops.wav
├── nature/
│   ├── campfire.mp3
│   ├── forest-birds.wav
│   └── wind-leaves.wav
├── ocean/
│   └── ocean-waves.wav
├── noise/
│   └── pink-noise.wav
├── animals/
│   └── [待上传动物声音文件]
├── things/
│   └── [待上传物品声音文件]
├── transport/
│   └── [待上传交通声音文件]
├── urban/
│   └── [待上传城市声音文件]
└── places/
    └── [待上传场所声音文件]
```

#### 2.2 文件命名规范
```bash
# 保持现有命名，确保与audioData.ts一致
# 格式: 小写字母 + 连字符
# 示例: light-rain.mp3, ocean-waves.wav
```

### 第三步: 音频文件上传

#### 3.1 使用Cloudflare Dashboard上传
```bash
# 方法1: Web界面上传
1. 进入 R2 → noisesleep-audio 存储桶
2. 创建文件夹: sounds/rain/, sounds/nature/ 等
3. 逐个上传音频文件到对应文件夹
4. 设置文件权限: 公共读取
```

#### 3.2 使用Wrangler CLI批量上传 (推荐)
```bash
# 安装Wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler login

# 批量上传脚本
#!/bin/bash
# upload-audio.sh

# 上传Rain分类
wrangler r2 object put noisesleep-audio/sounds/rain/gentle-rain.wav --file=./public/sounds/rain/gentle-rain.wav
wrangler r2 object put noisesleep-audio/sounds/rain/heavy-rain.mp3 --file=./public/sounds/rain/heavy-rain.mp3
wrangler r2 object put noisesleep-audio/sounds/rain/heavy-rain.wav --file=./public/sounds/rain/heavy-rain.wav
wrangler r2 object put noisesleep-audio/sounds/rain/light-rain.mp3 --file=./public/sounds/rain/light-rain.mp3
wrangler r2 object put noisesleep-audio/sounds/rain/rain-drops.wav --file=./public/sounds/rain/rain-drops.wav

# 上传Nature分类
wrangler r2 object put noisesleep-audio/sounds/nature/campfire.mp3 --file=./public/sounds/nature/campfire.mp3
wrangler r2 object put noisesleep-audio/sounds/nature/forest-birds.wav --file=./public/sounds/nature/forest-birds.wav
wrangler r2 object put noisesleep-audio/sounds/nature/wind-leaves.wav --file=./public/sounds/nature/wind-leaves.wav

# 上传Ocean分类
wrangler r2 object put noisesleep-audio/sounds/ocean/ocean-waves.wav --file=./public/sounds/ocean/ocean-waves.wav

# 上传Noise分类
wrangler r2 object put noisesleep-audio/sounds/noise/pink-noise.wav --file=./public/sounds/Noise/pink-noise.wav
```

### 第四步: 代码修改适配CDN

#### 4.1 创建音频URL配置文件
```typescript
// src/config/audio.ts
export const AUDIO_CONFIG = {
  // 开发环境使用本地文件
  DEV_BASE_URL: '/sounds',
  // 生产环境使用CDN
  PROD_BASE_URL: 'https://cdn.noisesleep.com/sounds',
  // 当前环境
  BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://cdn.noisesleep.com/sounds'
    : '/sounds'
};

// 获取音频文件URL的工具函数
export function getAudioUrl(category: string, filename: string): string {
  // 分类名称映射 (小写 → 小写，统一格式)
  const categoryMap: Record<string, string> = {
    'rain': 'rain',
    'nature': 'nature', 
    'noise': 'noise',
    'animals': 'animals',
    'things': 'things',
    'transport': 'transport',
    'urban': 'urban',
    'places': 'places',
    'ocean': 'ocean'
  };
  
  const folderName = categoryMap[category.toLowerCase()] || category.toLowerCase();
  return `${AUDIO_CONFIG.BASE_URL}/${folderName}/${filename}`;
}
```

#### 4.2 修改useAudioPlayer.ts
```typescript
// src/hooks/useAudioPlayer.ts
import { getAudioUrl } from '@/config/audio';

// 第79行附近，替换原有的audioUrl生成逻辑
const audioUrl = getAudioUrl(sound.category, sound.filename);

// 删除原有的categoryMap和路径拼接逻辑
// const categoryMap: Record<string, string> = { ... };
// const folderName = categoryMap[sound.category] || sound.category;
// const audioUrl = `/Sounds/${folderName}/${sound.filename}`;
```

#### 4.3 添加环境变量配置
```bash
# .env.local
NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds

# .env.production
NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds

# .env.development  
NEXT_PUBLIC_AUDIO_CDN_URL=/sounds
```

#### 4.4 更新音频配置使用环境变量
```typescript
// src/config/audio.ts (更新版本)
export const AUDIO_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_AUDIO_CDN_URL || '/sounds'
};

export function getAudioUrl(category: string, filename: string): string {
  const folderName = category.toLowerCase();
  return `${AUDIO_CONFIG.BASE_URL}/${folderName}/${filename}`;
}
```

### 第五步: Cloudflare CDN性能优化

#### 5.1 缓存规则配置
```bash
# 在Cloudflare Dashboard → 缓存 → 配置
页面规则:
- URL: cdn.noisesleep.com/sounds/*
- 缓存级别: 缓存所有内容
- 边缘缓存TTL: 1个月
- 浏览器缓存TTL: 1天
```

#### 5.2 压缩设置
```bash
# 在Cloudflare Dashboard → 速度 → 优化
Brotli压缩: 启用
Gzip压缩: 启用
自动缩小: 启用 (CSS, JS, HTML)
```

#### 5.3 HTTP/2和HTTP/3
```bash
# 在Cloudflare Dashboard → 网络
HTTP/2: 启用
HTTP/3 (QUIC): 启用
0-RTT连接恢复: 启用
```

### 第六步: 安全和访问控制

#### 6.1 防盗链保护
```bash
# 在Cloudflare Dashboard → 安全 → WAF
创建自定义规则:
- 规则名称: 音频文件防盗链
- 字段: HTTP Referer
- 操作: 如果不包含 "noisesleep.com" 则阻止
```

#### 6.2 速率限制
```bash
# 防止音频文件被恶意下载
创建速率限制规则:
- URL: cdn.noisesleep.com/sounds/*
- 阈值: 每分钟100个请求
- 持续时间: 10分钟
```

---

## 💰 成本估算

### Cloudflare R2存储费用
```bash
存储费用:
- 前10GB: 免费
- 超出部分: $0.015/GB/月
- 预估音频文件大小: 500MB = 0.5GB
- 月存储费用: 免费 (在免费额度内)

请求费用:
- Class A操作 (上传): 前100万次免费
- Class B操作 (下载): 前1000万次免费
- 预估月下载量: 10万次
- 月请求费用: 免费 (在免费额度内)

出站流量:
- 前10GB: 免费  
- 超出部分: $0.09/GB
- 预估月流量: 5GB
- 月流量费用: 免费 (在免费额度内)

总计月费用: $0 (MVP阶段完全免费)
```

### 扩展后费用预估
```bash
# 当用户量增长后 (月活10万用户)
存储: 1GB × $0.015 = $0.015
流量: 100GB × $0.09 = $9
总计: ~$10/月 (相比AWS S3节省90%+)
```

---

## 🧪 部署验证方法

### 第一步: CDN连通性测试
```bash
# 测试CDN域名解析
nslookup cdn.noisesleep.com

# 测试音频文件访问
curl -I https://cdn.noisesleep.com/sounds/rain/light-rain.mp3

# 预期响应:
# HTTP/2 200
# content-type: audio/mpeg
# cache-control: public, max-age=31536000
```

### 第二步: 应用功能测试
```javascript
// 在浏览器控制台测试
// 1. 检查音频URL生成
import { getAudioUrl } from '@/config/audio';
console.log(getAudioUrl('rain', 'light-rain.mp3'));
// 预期输出: https://cdn.noisesleep.com/sounds/rain/light-rain.mp3

// 2. 测试音频加载
const audio = new Audio('https://cdn.noisesleep.com/sounds/rain/light-rain.mp3');
audio.addEventListener('canplaythrough', () => console.log('✅ 音频加载成功'));
audio.addEventListener('error', (e) => console.error('❌ 音频加载失败:', e));
audio.load();
```

### 第三步: 性能测试
```bash
# 使用GTmetrix或PageSpeed Insights测试
# 关键指标:
- LCP (Largest Contentful Paint): < 2.5s
- FID (First Input Delay): < 100ms  
- CLS (Cumulative Layout Shift): < 0.1
- 音频首字节时间: < 200ms
```

### 第四步: 全球访问测试
```bash
# 使用多地区测试工具
# 测试地区: 北美、欧洲、亚洲
# 测试指标:
- DNS解析时间: < 50ms
- 连接建立时间: < 100ms
- 首字节时间: < 200ms
- 音频下载速度: > 1MB/s
```

---

## 🔧 故障排除指南

### 常见问题1: 音频文件404错误
```bash
问题: 浏览器显示音频文件无法加载
排查步骤:
1. 检查CDN域名是否正确解析
2. 验证R2存储桶中文件路径
3. 确认文件权限设置为公共读取
4. 检查代码中的URL生成逻辑
```

### 常见问题2: CORS跨域错误
```bash
问题: 浏览器控制台显示CORS错误
解决方案:
1. 在R2存储桶设置中配置CORS
2. 允许的源: https://noisesleep.com, https://www.noisesleep.com
3. 允许的方法: GET, HEAD
4. 允许的头部: Content-Type, Range
```

### 常见问题3: 缓存问题
```bash
问题: 音频文件更新后仍播放旧版本
解决方案:
1. 在Cloudflare Dashboard清除缓存
2. 使用版本号或时间戳更新文件名
3. 设置合适的缓存TTL
```

---

## 📋 部署检查清单

### 部署前检查 ✅
- [ ] Cloudflare账户已设置，域名已绑定
- [ ] R2存储桶已创建并配置
- [ ] 自定义域名cdn.noisesleep.com已设置
- [ ] 本地音频文件已整理并准备上传
- [ ] 代码修改已完成并测试

### 部署中检查 ✅  
- [ ] 音频文件已按目录结构上传到R2
- [ ] 文件权限已设置为公共读取
- [ ] CDN缓存规则已配置
- [ ] 防盗链和安全规则已设置
- [ ] 环境变量已正确配置

### 部署后验证 ✅
- [ ] CDN域名可正常访问
- [ ] 音频文件可正常播放
- [ ] 全球访问速度测试通过
- [ ] 应用功能完整测试通过
- [ ] 性能指标达到预期
- [ ] 错误监控已设置

---

**部署预计时间**: 2-4小时  
**技术难度**: 中等  
**风险评估**: 低 (可随时回滚到本地文件)  
**建议执行时间**: 非高峰期 (避免影响用户体验)

---

## 🛠️ 实用脚本和配置文件

### 自动化上传脚本
```bash
#!/bin/bash
# scripts/upload-to-cdn.sh
# NoiseSleep音频文件CDN上传脚本

set -e

echo "🚀 开始上传NoiseSleep音频文件到Cloudflare R2..."

# 检查wrangler是否已安装
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI未安装，请先运行: npm install -g wrangler"
    exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "🔐 请先登录Cloudflare: wrangler login"
    exit 1
fi

BUCKET_NAME="noisesleep-audio"
LOCAL_SOUNDS_DIR="./public/sounds"

# 上传函数
upload_category() {
    local category=$1
    local local_dir=$2

    echo "📁 上传 $category 分类..."

    if [ -d "$local_dir" ]; then
        for file in "$local_dir"/*; do
            if [ -f "$file" ]; then
                filename=$(basename "$file")
                echo "  ⬆️  上传: $filename"
                wrangler r2 object put "$BUCKET_NAME/sounds/$category/$filename" --file="$file"
            fi
        done
        echo "✅ $category 分类上传完成"
    else
        echo "⚠️  目录不存在: $local_dir"
    fi
}

# 按分类上传
upload_category "rain" "$LOCAL_SOUNDS_DIR/rain"
upload_category "nature" "$LOCAL_SOUNDS_DIR/nature"
upload_category "ocean" "$LOCAL_SOUNDS_DIR/ocean"
upload_category "noise" "$LOCAL_SOUNDS_DIR/Noise"

echo "🎉 所有音频文件上传完成！"
echo "🔗 CDN地址: https://cdn.noisesleep.com/sounds/"
```

### 部署验证脚本
```bash
#!/bin/bash
# scripts/verify-cdn.sh
# CDN部署验证脚本

CDN_BASE_URL="https://cdn.noisesleep.com/sounds"

echo "🧪 开始验证CDN部署..."

# 测试文件列表
declare -a test_files=(
    "rain/light-rain.mp3"
    "rain/heavy-rain.mp3"
    "nature/campfire.mp3"
    "ocean/ocean-waves.wav"
    "noise/pink-noise.wav"
)

success_count=0
total_count=${#test_files[@]}

for file in "${test_files[@]}"; do
    url="$CDN_BASE_URL/$file"
    echo "🔍 测试: $url"

    if curl -s --head "$url" | head -n 1 | grep -q "200 OK"; then
        echo "✅ 成功: $file"
        ((success_count++))
    else
        echo "❌ 失败: $file"
    fi
done

echo ""
echo "📊 验证结果: $success_count/$total_count 文件可访问"

if [ $success_count -eq $total_count ]; then
    echo "🎉 CDN部署验证成功！"
    exit 0
else
    echo "⚠️  部分文件无法访问，请检查配置"
    exit 1
fi
```

### Next.js配置更新
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 现有配置...

  // 添加CDN域名到图片和媒体优化
  images: {
    domains: ['cdn.noisesleep.com'],
  },

  // 添加音频文件的静态资源优化
  async headers() {
    return [
      {
        source: '/sounds/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // 生产环境重写规则 (可选，用于渐进迁移)
  async rewrites() {
    if (process.env.NODE_ENV === 'production') {
      return [
        {
          source: '/sounds/:path*',
          destination: 'https://cdn.noisesleep.com/sounds/:path*',
        },
      ];
    }
    return [];
  },
};

module.exports = nextConfig;
```

### 环境配置模板
```bash
# .env.example
# NoiseSleep环境变量配置模板

# 音频CDN配置
NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds

# Cloudflare配置 (用于部署脚本)
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_API_TOKEN=your_api_token_here

# 可选: 启用音频预加载
NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD=true

# 可选: 音频质量设置
NEXT_PUBLIC_AUDIO_QUALITY=high
```

### TypeScript类型更新
```typescript
// src/types/audio.ts (添加CDN相关类型)
export interface AudioConfig {
  baseUrl: string;
  enablePreload: boolean;
  quality: 'low' | 'medium' | 'high';
}

export interface CDNMetrics {
  loadTime: number;
  cacheHit: boolean;
  region: string;
}

// 扩展现有的MultilingualAudioItem类型
export interface MultilingualAudioItem {
  // 现有字段...

  // 新增CDN相关字段
  cdnUrl?: string;
  fileSize?: number;
  lastModified?: string;
  checksumMD5?: string;
}
```

### 监控和分析配置
```typescript
// src/utils/audioAnalytics.ts
// 音频CDN性能监控

export interface AudioLoadMetrics {
  audioId: string;
  loadStartTime: number;
  loadEndTime: number;
  fileSize: number;
  cacheStatus: 'hit' | 'miss';
  errorCode?: string;
}

export class AudioCDNMonitor {
  private metrics: AudioLoadMetrics[] = [];

  startLoad(audioId: string): number {
    const startTime = performance.now();
    return startTime;
  }

  endLoad(audioId: string, startTime: number, success: boolean, fileSize?: number) {
    const endTime = performance.now();
    const loadTime = endTime - startTime;

    const metric: AudioLoadMetrics = {
      audioId,
      loadStartTime: startTime,
      loadEndTime: endTime,
      fileSize: fileSize || 0,
      cacheStatus: loadTime < 100 ? 'hit' : 'miss'
    };

    this.metrics.push(metric);

    // 发送到分析服务 (可选)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'audio_load', {
        event_category: 'performance',
        event_label: audioId,
        value: Math.round(loadTime)
      });
    }
  }

  getAverageLoadTime(): number {
    if (this.metrics.length === 0) return 0;
    const totalTime = this.metrics.reduce((sum, m) => sum + (m.loadEndTime - m.loadStartTime), 0);
    return totalTime / this.metrics.length;
  }
}

export const audioMonitor = new AudioCDNMonitor();
```

---

## 🚨 应急回滚方案

### 快速回滚到本地文件
```bash
#!/bin/bash
# scripts/rollback-to-local.sh
# 紧急回滚脚本

echo "🚨 执行紧急回滚到本地音频文件..."

# 更新环境变量
export NEXT_PUBLIC_AUDIO_CDN_URL="/sounds"

# 重启开发服务器
if pgrep -f "next dev" > /dev/null; then
    echo "🔄 重启开发服务器..."
    pkill -f "next dev"
    sleep 2
    npm run dev &
fi

# 如果是生产环境，需要重新部署
if [ "$NODE_ENV" = "production" ]; then
    echo "🏗️  重新构建生产版本..."
    npm run build
    echo "✅ 回滚完成，请重新部署应用"
else
    echo "✅ 开发环境回滚完成"
fi
```

### 分阶段迁移策略
```typescript
// src/config/audio.ts (渐进迁移版本)
export const AUDIO_CONFIG = {
  // 支持分阶段迁移的配置
  USE_CDN_PERCENTAGE: parseInt(process.env.NEXT_PUBLIC_CDN_PERCENTAGE || '0'),
  CDN_BASE_URL: process.env.NEXT_PUBLIC_AUDIO_CDN_URL || 'https://cdn.noisesleep.com/sounds',
  LOCAL_BASE_URL: '/sounds'
};

export function getAudioUrl(category: string, filename: string): string {
  const folderName = category.toLowerCase();

  // 渐进迁移：根据百分比决定使用CDN还是本地
  const useCDN = Math.random() * 100 < AUDIO_CONFIG.USE_CDN_PERCENTAGE;
  const baseUrl = useCDN ? AUDIO_CONFIG.CDN_BASE_URL : AUDIO_CONFIG.LOCAL_BASE_URL;

  return `${baseUrl}/${folderName}/${filename}`;
}
```

---

## 📈 性能优化建议

### 音频预加载策略
```typescript
// src/hooks/useAudioPreloader.ts
import { useEffect } from 'react';
import { audioData } from '@/data/audioData';
import { getAudioUrl } from '@/config/audio';

export function useAudioPreloader(preloadCount: number = 5) {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // 预加载最受欢迎的音频文件
    const popularAudios = audioData
      .sort((a, b) => b.sleepEffectiveness - a.sleepEffectiveness)
      .slice(0, preloadCount);

    popularAudios.forEach(audio => {
      const audioUrl = getAudioUrl(audio.category, audio.filename);
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = audioUrl;
      link.as = 'audio';
      document.head.appendChild(link);
    });
  }, [preloadCount]);
}
```

### 智能缓存策略
```typescript
// src/utils/audioCache.ts
export class AudioCache {
  private cache = new Map<string, ArrayBuffer>();
  private maxSize = 50 * 1024 * 1024; // 50MB
  private currentSize = 0;

  async get(url: string): Promise<ArrayBuffer | null> {
    return this.cache.get(url) || null;
  }

  async set(url: string, data: ArrayBuffer): Promise<void> {
    // 简单的LRU缓存实现
    if (this.currentSize + data.byteLength > this.maxSize) {
      this.evictOldest();
    }

    this.cache.set(url, data);
    this.currentSize += data.byteLength;
  }

  private evictOldest(): void {
    const firstKey = this.cache.keys().next().value;
    if (firstKey) {
      const data = this.cache.get(firstKey);
      if (data) {
        this.currentSize -= data.byteLength;
        this.cache.delete(firstKey);
      }
    }
  }
}
```

---

*方案制定时间: 2025年07月07日 23:00:00*
*制定人员: Augment Agent*
*适用版本: NoiseSleep Phase 1 MVP*
*最后更新: 2025年07月07日 23:30:00*
