const { chromium } = require('playwright');

async function testSleepFunctionality() {
  console.log('🌙 开始全面测试 NoiseSleep 睡眠功能...');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // 慢速执行以便观察
  });
  
  const page = await browser.newPage();
  
  // 收集控制台消息
  const logs = [];
  page.on('console', msg => {
    const text = msg.text();
    console.log('🖥️  浏览器控制台:', text);
    logs.push(text);
  });
  
  // 监听错误
  page.on('pageerror', error => {
    console.log('❌ 页面错误:', error.message);
  });
  
  try {
    // 1. 导航到音频页面
    console.log('\n📍 步骤 1: 导航到音频页面');
    await page.goto('http://localhost:3000/zh/sounds');
    await page.waitForTimeout(3000);
    
    // 2. 测试基础音频播放功能
    console.log('\n📍 步骤 2: 测试基础音频播放');
    const playResult = await testBasicAudioPlay(page);
    console.log('✅ 基础播放测试结果:', playResult);
    
    // 3. 测试睡眠模式切换
    console.log('\n📍 步骤 3: 测试睡眠模式切换');
    const sleepModeResult = await testSleepModeToggle(page);
    console.log('✅ 睡眠模式测试结果:', sleepModeResult);
    
    // 4. 测试定时器功能
    console.log('\n📍 步骤 4: 测试定时器功能');
    const timerResult = await testTimerFunctionality(page);
    console.log('✅ 定时器测试结果:', timerResult);
    
    // 5. 测试混音功能
    console.log('\n📍 步骤 5: 测试混音功能');
    const mixingResult = await testMixingFunctionality(page);
    console.log('✅ 混音测试结果:', mixingResult);
    
    // 6. 测试睡眠模式界面
    console.log('\n📍 步骤 6: 测试睡眠模式界面');
    const sleepUIResult = await testSleepModeUI(page);
    console.log('✅ 睡眠模式界面测试结果:', sleepUIResult);
    
    // 7. 测试手势控制
    console.log('\n📍 步骤 7: 测试手势控制');
    const gestureResult = await testGestureControls(page);
    console.log('✅ 手势控制测试结果:', gestureResult);
    
    console.log('\n🎉 所有睡眠功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    console.log('\n📝 收集到的控制台消息数量:', logs.length);
    await browser.close();
  }
}

// 测试基础音频播放
async function testBasicAudioPlay(page) {
  const result = await page.evaluate(() => {
    const playButtons = document.querySelectorAll('button[aria-label="播放"]');
    if (playButtons.length > 0) {
      playButtons[0].click();
      return { success: true, buttonCount: playButtons.length };
    }
    return { success: false, buttonCount: 0 };
  });
  
  await page.waitForTimeout(2000);
  
  // 检查音频播放器是否出现
  const playerVisible = await page.evaluate(() => {
    const player = document.querySelector('.fixed.bottom-0');
    return player !== null;
  });
  
  return { ...result, playerVisible };
}

// 测试睡眠模式切换
async function testSleepModeToggle(page) {
  const result = await page.evaluate(() => {
    // 查找睡眠模式切换按钮
    const sleepButton = document.querySelector('[data-testid="sleep-mode-toggle"]') ||
                       document.querySelector('button[title*="睡眠"]') ||
                       document.querySelector('button[aria-label*="睡眠"]');
    
    if (sleepButton) {
      sleepButton.click();
      return { success: true, buttonFound: true };
    }
    
    // 如果没有找到专门的睡眠按钮，尝试查找其他可能的切换方式
    const modeButtons = document.querySelectorAll('button');
    for (let btn of modeButtons) {
      if (btn.textContent.includes('睡眠') || btn.textContent.includes('Sleep')) {
        btn.click();
        return { success: true, buttonFound: true, fallback: true };
      }
    }
    
    return { success: false, buttonFound: false };
  });
  
  await page.waitForTimeout(2000);
  
  // 检查是否切换到睡眠模式
  const sleepModeActive = await page.evaluate(() => {
    // 检查是否有全屏睡眠模式的标识
    const fullscreenMode = document.querySelector('.fixed.inset-0') ||
                          document.querySelector('[data-mode="sleep"]') ||
                          document.body.classList.contains('sleep-mode');
    return fullscreenMode !== null;
  });
  
  return { ...result, sleepModeActive };
}

// 测试定时器功能
async function testTimerFunctionality(page) {
  console.log('🔍 查找定时器按钮...');

  const result = await page.evaluate(() => {
    // 首先确保音频播放器可见
    const player = document.querySelector('.fixed.bottom-0');
    if (!player) {
      return { success: false, timerButtonFound: false, error: 'Audio player not visible' };
    }

    // 在播放器内查找定时器按钮
    const timerButton = player.querySelector('[data-testid="timer-button"]') ||
                       player.querySelector('button[title*="定时"]') ||
                       player.querySelector('button[aria-label*="定时"]') ||
                       player.querySelector('button[aria-label*="timer"]') ||
                       player.querySelector('svg[class*="clock"]')?.closest('button') ||
                       player.querySelector('svg[class*="timer"]')?.closest('button');

    if (timerButton) {
      console.log('✅ 找到定时器按钮，点击中...');
      timerButton.click();
      return { success: true, timerButtonFound: true };
    }

    // 查找所有播放器内的按钮并记录
    const buttons = player.querySelectorAll('button');
    const buttonInfo = Array.from(buttons).map(btn => ({
      text: btn.textContent?.trim(),
      title: btn.title,
      ariaLabel: btn.getAttribute('aria-label'),
      className: btn.className,
      hasSvg: btn.querySelector('svg') !== null
    }));

    return {
      success: false,
      timerButtonFound: false,
      playerFound: true,
      buttonCount: buttons.length,
      buttonInfo: buttonInfo
    };
  });

  await page.waitForTimeout(2000);

  // 检查定时器面板是否出现
  const timerPanelVisible = await page.evaluate(() => {
    const timerPanel = document.querySelector('[data-testid="timer-panel"]') ||
                      document.querySelector('.timer-panel') ||
                      document.querySelector('[class*="timer"]') ||
                      document.querySelector('.fixed.inset-0.bg-black\\/20');
    return timerPanel !== null;
  });

  console.log('🔍 定时器测试结果:', { ...result, timerPanelVisible });
  return { ...result, timerPanelVisible };
}

// 测试混音功能
async function testMixingFunctionality(page) {
  console.log('🔍 查找混音按钮...');

  const result = await page.evaluate(() => {
    // 首先确保音频播放器可见
    const player = document.querySelector('.fixed.bottom-0');
    if (!player) {
      return { success: false, mixingButtonFound: false, error: 'Audio player not visible' };
    }

    // 在播放器内查找混音按钮
    const mixingButton = player.querySelector('[data-testid="mixing-button"]') ||
                        player.querySelector('button[title*="混音"]') ||
                        player.querySelector('button[aria-label*="混音"]') ||
                        player.querySelector('button[aria-label*="mix"]') ||
                        player.querySelector('svg[class*="mix"]')?.closest('button') ||
                        player.querySelector('svg[class*="sliders"]')?.closest('button');

    if (mixingButton) {
      console.log('✅ 找到混音按钮，点击中...');
      mixingButton.click();
      return { success: true, mixingButtonFound: true };
    }

    // 查找所有播放器内的按钮并记录
    const buttons = player.querySelectorAll('button');
    const buttonInfo = Array.from(buttons).map(btn => ({
      text: btn.textContent?.trim(),
      title: btn.title,
      ariaLabel: btn.getAttribute('aria-label'),
      className: btn.className,
      hasSvg: btn.querySelector('svg') !== null
    }));

    return {
      success: false,
      mixingButtonFound: false,
      playerFound: true,
      buttonCount: buttons.length,
      buttonInfo: buttonInfo
    };
  });

  await page.waitForTimeout(2000);

  // 检查混音面板是否出现
  const mixingPanelVisible = await page.evaluate(() => {
    const mixingPanel = document.querySelector('[data-testid="mixing-panel"]') ||
                       document.querySelector('.mixing-panel') ||
                       document.querySelector('[class*="mixing"]') ||
                       document.querySelector('.fixed.inset-0.bg-black\\/20');
    return mixingPanel !== null;
  });

  console.log('🔍 混音测试结果:', { ...result, mixingPanelVisible });
  return { ...result, mixingPanelVisible };
}

// 测试睡眠模式界面
async function testSleepModeUI(page) {
  // 首先确保在睡眠模式
  await testSleepModeToggle(page);
  await page.waitForTimeout(2000);
  
  const result = await page.evaluate(() => {
    // 检查睡眠模式的关键UI元素
    const fullscreenContainer = document.querySelector('.fixed.inset-0');
    const sleepModeElements = {
      fullscreenContainer: fullscreenContainer !== null,
      backgroundGradient: false,
      centerControls: false,
      pullString: false
    };
    
    if (fullscreenContainer) {
      // 检查背景渐变
      const hasGradient = fullscreenContainer.style.background?.includes('gradient') ||
                         fullscreenContainer.className.includes('gradient');
      sleepModeElements.backgroundGradient = hasGradient;
      
      // 检查中心控制区域
      const centerControls = fullscreenContainer.querySelector('.flex.flex-col.items-center') ||
                            fullscreenContainer.querySelector('[class*="center"]');
      sleepModeElements.centerControls = centerControls !== null;
      
      // 检查拉绳控制器
      const pullString = fullscreenContainer.querySelector('[data-testid="pull-string"]') ||
                         fullscreenContainer.querySelector('[class*="pull-string"]');
      sleepModeElements.pullString = pullString !== null;
    }
    
    return sleepModeElements;
  });
  
  return result;
}

// 测试手势控制
async function testGestureControls(page) {
  const result = await page.evaluate(() => {
    // 查找可拖拽的元素
    const draggableElements = document.querySelectorAll('[draggable="true"]') ||
                             document.querySelectorAll('[data-draggable="true"]') ||
                             document.querySelectorAll('[class*="draggable"]');
    
    return {
      draggableElementsFound: draggableElements.length,
      hasDragSupport: draggableElements.length > 0
    };
  });
  
  // 如果找到可拖拽元素，尝试模拟拖拽
  if (result.hasDragSupport) {
    try {
      const dragElement = await page.locator('[draggable="true"]').first();
      if (await dragElement.count() > 0) {
        const box = await dragElement.boundingBox();
        if (box) {
          // 模拟拖拽手势
          await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
          await page.mouse.down();
          await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2 + 50);
          await page.mouse.up();
          result.dragTestPerformed = true;
        }
      }
    } catch (error) {
      result.dragTestError = error.message;
    }
  }
  
  return result;
}

// 运行测试
testSleepFunctionality().catch(console.error);
