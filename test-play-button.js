const puppeteer = require('puppeteer');

(async () => {
  try {
    console.log('启动浏览器测试...');
    const browser = await puppeteer.launch({ headless: false });
    const page = await browser.newPage();
    
    // 监听控制台消息
    const logs = [];
    page.on('console', msg => {
      const text = msg.text();
      console.log('浏览器控制台:', text);
      logs.push(text);
    });
    
    console.log('导航到音频页面...');
    await page.goto('http://localhost:3001/zh/sounds');
    await page.waitForTimeout(2000);
    
    console.log('查找播放按钮...');
    // 尝试多种选择器
    let playButton = await page.$('button[aria-label="播放"]');
    if (!playButton) {
      playButton = await page.$('button:has-text("播放")');
    }
    if (!playButton) {
      playButton = await page.$('.play-button');
    }
    if (!playButton) {
      // 获取所有按钮并查找包含播放文本的
      const buttons = await page.$$('button');
      for (const button of buttons) {
        const text = await button.textContent();
        if (text && text.includes('播放')) {
          playButton = button;
          break;
        }
      }
    }
    
    if (playButton) {
      console.log('找到播放按钮，准备点击...');
      await playButton.click();
      console.log('已点击播放按钮');
      
      // 等待一下看看控制台输出
      await page.waitForTimeout(3000);
      
      console.log('收集到的控制台消息数量:', logs.length);
      logs.forEach((log, index) => {
        console.log(`消息 ${index + 1}: ${log}`);
      });
      
      // 检查是否有音频播放器出现
      const audioPlayer = await page.$('.audio-player');
      if (audioPlayer) {
        console.log('✅ 音频播放器已出现');
      } else {
        console.log('❌ 音频播放器未出现');
      }
      
    } else {
      console.log('❌ 未找到播放按钮');
      
      // 列出页面上的所有按钮
      const buttons = await page.$$('button');
      console.log(`页面上共有 ${buttons.length} 个按钮:`);
      for (let i = 0; i < Math.min(buttons.length, 5); i++) {
        const button = buttons[i];
        const text = await button.textContent();
        const ariaLabel = await button.getAttribute('aria-label');
        console.log(`按钮 ${i + 1}: 文本="${text}", aria-label="${ariaLabel}"`);
      }
    }
    
    await browser.close();
    console.log('测试完成');
  } catch (error) {
    console.error('错误:', error.message);
  }
})();
