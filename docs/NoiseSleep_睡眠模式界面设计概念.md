# NoiseSleep 睡眠模式界面设计概念

## 概述

本设计概念展示了 NoiseSleep 睡眠模式的界面设计，灵感来源于 Muji CD 播放器的极简美学，专为睡前使用场景优化，提供沉浸式的睡眠体验。

## 睡眠模式界面设计概念图

```mermaid
graph TB
    subgraph "Sleep Mode Interface - Muji Inspired Design"
        subgraph "Top Section"
            T1[当前时间 23:45]
            T2[建议睡眠时长 8小时]
        end
        
        subgraph "Center Section"
            C1[音频名称]
            C2[大字体显示]
            C3[雨声 - 森林夜雨]
        end
        
        subgraph "Pull String Controller"
            P1[圆形拉绳区域]
            P2[物理拉动效果]
            P3[播放/暂停状态指示]
            P4[柔和呼吸动画]
        end
        
        subgraph "Timer Display"
            TD1[定时器倒计时]
            TD2[剩余 2小时30分钟]
            TD3[淡出开始提示]
        end
        
        subgraph "Bottom Section"
            B1[退出睡眠模式]
            B2[小型返回按钮]
        end
        
        subgraph "Background"
            BG1[深色背景 #0f0f0f]
            BG2[暖白色文字 #f5f5f5]
            BG3[琥珀色强调 #f59e0b]
            BG4[极简留白设计]
        end
    end
    
    T1 --> T2
    C1 --> C2
    C2 --> C3
    P1 --> P2
    P2 --> P3
    P3 --> P4
    TD1 --> TD2
    TD2 --> TD3
    B1 --> B2
    
    BG1 --> BG2
    BG2 --> BG3
    BG3 --> BG4
    
    style T1 fill:#1a1a1a,color:#f5f5f5
    style C1 fill:#1a1a1a,color:#f5f5f5
    style P1 fill:#f59e0b,color:#0f0f0f
    style TD1 fill:#1a1a1a,color:#f59e0b
    style B1 fill:#1a1a1a,color:#9ca3af
```

## 设计理念

### Muji 美学原则
- **极简主义**：去除一切不必要的视觉元素
- **功能至上**：每个元素都有明确的功能目的
- **自然材质感**：使用温暖、自然的色彩和质感
- **留白艺术**：大量留白营造宁静氛围

### 睡眠优化设计
- **护眼配色**：深色背景减少蓝光刺激
- **大字体**：在暗光环境中易于阅读
- **柔和动画**：避免刺眼的快速变化
- **直观操作**：简化交互，减少认知负担

## 界面布局详解

### 顶部区域 (Top Section)
- **当前时间显示**：大字体显示当前时间，帮助用户建立时间感知
- **睡眠建议**：基于当前时间智能推荐合适的睡眠时长
- **字体大小**：24px - 28px，确保在暗光环境中清晰可见
- **颜色**：暖白色 (#f5f5f5)，温和不刺眼

### 中央区域 (Center Section)
- **音频信息**：当前播放音频的名称和描述
- **字体层次**：
  - 主标题：32px - 36px，突出显示
  - 副标题：18px - 20px，提供补充信息
- **布局**：垂直居中，营造平衡感
- **动画**：文字淡入淡出，配合音频切换

### 拉绳控制器 (Pull String Controller)
- **设计灵感**：模拟传统 CD 播放器的拉绳开关
- **视觉设计**：
  - 圆形区域：直径 120px - 150px
  - 琥珀色背景：#f59e0b，温暖的强调色
  - 拉绳图标：简洁的线条设计
- **交互效果**：
  - 点击时：缩放动画 (scale: 1.1)
  - 播放时：柔和的呼吸动画
  - 拖拽时：跟随手指移动
- **状态指示**：
  - 播放：持续的呼吸动画
  - 暂停：静止状态
  - 加载：旋转动画

### 定时器显示 (Timer Display)
- **倒计时显示**：大字体显示剩余时间
- **格式**：2小时30分钟 / 1小时15分钟 / 45分钟
- **颜色变化**：
  - 正常：琥珀色 (#f59e0b)
  - 即将结束：温和的红色 (#ef4444)
- **淡出提示**：在淡出开始前5分钟显示提示

### 底部区域 (Bottom Section)
- **退出按钮**：小型、低调的返回按钮
- **位置**：右下角，不干扰主要内容
- **样式**：半透明，悬停时显示
- **图标**：简洁的 "×" 或箭头图标

## 色彩系统

### 主色调
- **深色背景**：#0f0f0f - 接近纯黑，减少眼部疲劳
- **暖白色文字**：#f5f5f5 - 柔和的白色，不刺眼
- **琥珀色强调**：#f59e0b - 温暖的强调色，营造舒适感

### 辅助色彩
- **灰色文字**：#9ca3af - 次要信息显示
- **半透明遮罩**：rgba(0,0,0,0.8) - 背景遮罩
- **状态指示**：
  - 成功：#10b981 (绿色)
  - 警告：#f59e0b (琥珀色)
  - 错误：#ef4444 (红色)

## 动画设计

### 进入动画
```css
.sleep-mode-enter {
  opacity: 0;
  transform: scale(0.95);
  animation: sleepModeEnter 0.8s ease-out forwards;
}

@keyframes sleepModeEnter {
  to {
    opacity: 1;
    transform: scale(1);
  }
}
```

### 拉绳动画
```css
.pull-string {
  transition: transform 0.2s ease-out;
}

.pull-string:active {
  transform: scale(1.1) translateY(4px);
}

.pull-string.playing {
  animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}
```

### 文字动画
```css
.audio-info {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 响应式适配

### 桌面端 (1024px+)
- 全屏显示，最佳视觉体验
- 拉绳控制器居中显示
- 充分利用屏幕空间

### 平板端 (768px - 1023px)
- 适当缩小字体和控件尺寸
- 保持核心功能和视觉效果
- 优化触摸交互区域

### 手机端 (< 768px)
- 垂直布局优化
- 增大触摸区域
- 简化部分视觉效果

## 无障碍访问

- **键盘导航**：支持 Tab 键导航
- **屏幕阅读器**：完整的 ARIA 标签
- **高对比度**：支持系统高对比度模式
- **字体缩放**：支持系统字体大小设置

---

**创建时间**：2025-01-07  
**版本**：v1.0  
**设计师**：基于 Muji CD 播放器美学  
**相关文档**：音频播放器设计方案_20250107_143000.md
