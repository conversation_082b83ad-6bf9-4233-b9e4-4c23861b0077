# NoiseSleep 音频播放器用户交互流程图

## 概述

本流程图展示了 NoiseSleep 音频播放器系统的完整用户交互路径，包括基础播放、睡眠定时器、音频混合和睡眠模式等核心功能的用户操作流程。

## 用户交互流程图

```mermaid
graph TD
    A[用户访问 NoiseSleep] --> B[浏览音频库]
    B --> C[点击 AudioCard 播放按钮]
    C --> D[标准播放器出现在底部]
    
    D --> E{用户选择操作}
    E -->|调节音量| F[音量控制]
    E -->|设置定时器| G[打开定时器面板]
    E -->|音频混合| H[打开混合面板]
    E -->|切换睡眠模式| I[进入全屏睡眠模式]
    
    F --> D
    
    G --> G1[选择预设时长]
    G --> G2[自定义时长]
    G1 --> G3[启动定时器]
    G2 --> G3
    G3 --> G4[显示倒计时]
    G4 --> G5[自动淡出]
    G5 --> G6[停止播放]
    
    H --> H1[选择第二个音频]
    H1 --> H2[调节各轨道音量]
    H2 --> H3[保存混合预设]
    H3 --> D
    
    I --> I1[拉绳控制器界面]
    I1 --> I2{拉绳操作}
    I2 -->|拉动| I3[播放/暂停切换]
    I2 -->|点击退出| I4[返回标准模式]
    I3 --> I1
    I4 --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style I fill:#e8f5e8
    style G6 fill:#ffebee
```

## 主要用户路径说明

### 路径1：基础播放流程
1. **音频选择**：用户从音频库中选择想要播放的音频
2. **播放启动**：点击 AudioCard 上的播放按钮
3. **播放器显示**：标准播放器从底部滑入显示
4. **播放控制**：用户可以进行音量调节、进度控制等基础操作

### 路径2：睡眠定时器流程
1. **定时器激活**：从标准播放器点击定时器按钮
2. **时长设置**：选择预设时长或自定义时长
3. **定时器启动**：开始倒计时显示
4. **自动停止**：到达设定时间后自动淡出并停止播放

### 路径3：音频混合流程
1. **混合激活**：从标准播放器点击混合按钮
2. **音频添加**：选择第二个音频进行混合
3. **音量调节**：分别调节各轨道的音量
4. **预设保存**：保存常用的混合配置

### 路径4：睡眠模式流程
1. **模式切换**：从标准播放器切换到睡眠模式
2. **全屏界面**：进入专为睡眠优化的全屏界面
3. **拉绳控制**：通过拉绳控制器进行播放控制
4. **模式退出**：可随时返回标准模式

## 设计原则

- **直观性**：每个操作都有清晰的视觉反馈
- **一致性**：所有功能模块保持统一的交互模式
- **可访问性**：支持键盘导航和屏幕阅读器
- **响应式**：适配不同设备和屏幕尺寸

## 关键交互节点

1. **音频选择触发点**：AudioCard 组件的播放按钮
2. **模式切换点**：标准模式与睡眠模式的无缝切换
3. **功能面板入口**：定时器、混合面板的快速访问
4. **状态反馈点**：播放状态、定时器状态的实时显示

---

**创建时间**：2025-01-07  
**版本**：v1.0  
**相关文档**：音频播放器设计方案_20250107_143000.md
