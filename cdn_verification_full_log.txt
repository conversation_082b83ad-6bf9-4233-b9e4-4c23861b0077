
🧪 NoiseSleep CDN部署验证脚本
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

[0;35m🔍 检查本地音频目录...[0m
[0;32m✅ 本地音频目录存在: /Users/<USER>/Documents/NoiseSleep/sounds[0m
[0;35m🔍 扫描本地音频文件...[0m
[0;32m✅ 发现 74 个音频文件[0m

[0;34mℹ️  按分类统计:[0m
  📁 urban: 7 个文件
  📁 rain: 8 个文件
  📁 transport: 6 个文件
  📁 places: 6 个文件
  📁 nature: 12 个文件
  📁 noise: 3 个文件
  📁 animals: 16 个文件
  📁 things: 16 个文件

[0;35m🔍 测试CDN域名解析...[0m
[0;32m✅ CDN域名解析成功[0m
[0;35m🔍 开始验证CDN上的音频文件...[0m

[1/74]
🔍 测试: urban/highway.mp3
  ✅ 成功 - 状态码: 200, 大小: 1671552字节, 耗时: 1.567397秒
