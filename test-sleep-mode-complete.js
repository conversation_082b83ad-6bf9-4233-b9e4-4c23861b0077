const { chromium } = require('playwright');

async function testSleepModeComplete() {
  console.log('🌙 开始完整睡眠模式功能测试...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    console.log('🖥️  浏览器控制台:', msg.text());
  });
  
  try {
    // 1. 导航到音频页面
    console.log('📍 步骤 1: 导航到音频页面');
    await page.goto('http://localhost:3000/zh/audio');
    await page.waitForTimeout(5000);

    // 等待页面完全加载
    await page.waitForSelector('button', { timeout: 10000 });
    console.log('✅ 页面加载完成');

    // 2. 播放音频
    console.log('📍 步骤 2: 播放音频');

    // 先尝试找到任何播放按钮
    const allButtons = await page.locator('button').all();
    console.log(`🔍 页面上共有 ${allButtons.length} 个按钮`);

    const playButton = await page.locator('button').filter({
      has: page.locator('svg path[d="M8 5v14l11-7z"]')
    }).first();

    if (await playButton.count() > 0) {
      await playButton.click();
      await page.waitForTimeout(3000);
      console.log('✅ 音频播放成功');
    } else {
      // 尝试点击第一个可见的按钮
      console.log('⚠️  播放图标按钮未找到，尝试点击第一个按钮');
      if (allButtons.length > 0) {
        await allButtons[0].click();
        await page.waitForTimeout(3000);
        console.log('✅ 点击了第一个按钮');
      } else {
        throw new Error('页面上没有找到任何按钮');
      }
    }
    
    // 3. 切换到睡眠模式
    console.log('📍 步骤 3: 切换到睡眠模式');
    const sleepButton = await page.locator('button[data-testid="sleep-mode-button"]');
    if (await sleepButton.count() > 0) {
      await sleepButton.click();
      await page.waitForTimeout(3000);
      console.log('✅ 睡眠模式切换成功');
    } else {
      throw new Error('睡眠模式按钮未找到');
    }
    
    // 4. 验证睡眠模式界面
    console.log('📍 步骤 4: 验证睡眠模式界面');
    const sleepModeContainer = await page.locator('.sleep-mode-player');
    const hasSleepMode = await sleepModeContainer.count() > 0;
    
    const hasGradientBg = await page.locator('.bg-gradient-to-b').count() > 0;
    const hasPullString = await page.locator('text=上下拖拽拉绳来控制播放').count() > 0;
    const hasAmbientInfo = await page.locator('text=睡眠质量').count() > 0;
    
    console.log('🔍 睡眠模式界面检查:', {
      sleepModeContainer: hasSleepMode,
      gradientBackground: hasGradientBg,
      pullStringHint: hasPullString,
      ambientInfo: hasAmbientInfo
    });
    
    // 5. 测试睡眠模式定时器
    console.log('📍 步骤 5: 测试睡眠模式定时器');
    const timerButton = await page.locator('button[data-testid="sleep-timer-button"]');
    if (await timerButton.count() > 0) {
      await timerButton.click();
      await page.waitForTimeout(2000);
      
      const timerPanel = await page.locator('.timer-panel, [data-testid="timer-panel"]');
      const timerPanelVisible = await timerPanel.count() > 0;
      console.log('✅ 睡眠模式定时器测试:', { buttonFound: true, panelVisible: timerPanelVisible });
      
      // 关闭定时器面板
      if (timerPanelVisible) {
        const closeButton = await page.locator('button').filter({ hasText: /关闭|取消|×/ }).first();
        if (await closeButton.count() > 0) {
          await closeButton.click();
          await page.waitForTimeout(1000);
        }
      }
    } else {
      console.log('❌ 睡眠模式定时器按钮未找到');
    }
    
    // 6. 测试睡眠模式混音
    console.log('📍 步骤 6: 测试睡眠模式混音');
    const mixingButton = await page.locator('button[data-testid="sleep-mixing-button"]');
    if (await mixingButton.count() > 0) {
      await mixingButton.click();
      await page.waitForTimeout(2000);
      
      const mixingPanel = await page.locator('.mixing-panel, [data-testid="mixing-panel"]');
      const mixingPanelVisible = await mixingPanel.count() > 0;
      console.log('✅ 睡眠模式混音测试:', { buttonFound: true, panelVisible: mixingPanelVisible });
      
      // 关闭混音面板
      if (mixingPanelVisible) {
        const closeButton = await page.locator('button').filter({ hasText: /关闭|取消|×/ }).first();
        if (await closeButton.count() > 0) {
          await closeButton.click();
          await page.waitForTimeout(1000);
        }
      }
    } else {
      console.log('❌ 睡眠模式混音按钮未找到');
    }
    
    // 7. 测试拉绳控制器
    console.log('📍 步骤 7: 测试拉绳控制器');
    const pullStringController = await page.locator('.pull-string-controller, [data-testid="pull-string-controller"]');
    if (await pullStringController.count() > 0) {
      // 模拟拖拽操作
      const boundingBox = await pullStringController.boundingBox();
      if (boundingBox) {
        const centerX = boundingBox.x + boundingBox.width / 2;
        const centerY = boundingBox.y + boundingBox.height / 2;
        
        // 向下拖拽
        await page.mouse.move(centerX, centerY);
        await page.mouse.down();
        await page.mouse.move(centerX, centerY + 50);
        await page.mouse.up();
        await page.waitForTimeout(1000);
        
        console.log('✅ 拉绳控制器拖拽测试完成');
      }
    } else {
      console.log('❌ 拉绳控制器未找到');
    }
    
    // 8. 测试退出睡眠模式
    console.log('📍 步骤 8: 测试退出睡眠模式');
    const exitButton = await page.locator('button').filter({
      has: page.locator('svg path[d="M6 18L18 6M6 6l12 12"]')
    }).first();
    
    if (await exitButton.count() > 0) {
      await exitButton.click();
      await page.waitForTimeout(2000);
      
      // 验证是否回到标准模式
      const standardPlayer = await page.locator('.fixed.bottom-0');
      const backToStandard = await standardPlayer.count() > 0;
      console.log('✅ 退出睡眠模式测试:', { exitButtonFound: true, backToStandard });
    } else {
      console.log('❌ 退出睡眠模式按钮未找到');
    }
    
    // 9. 截图保存
    console.log('📍 步骤 9: 保存测试截图');
    await page.screenshot({ path: 'sleep-mode-complete-test.png', fullPage: true });
    console.log('📸 截图已保存: sleep-mode-complete-test.png');
    
    console.log('🎉 睡眠模式完整功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    await page.screenshot({ path: 'sleep-mode-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

testSleepModeComplete();
