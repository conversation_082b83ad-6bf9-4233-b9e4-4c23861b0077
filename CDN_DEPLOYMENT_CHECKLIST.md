# 🚀 NoiseSleep CDN 部署检查清单

**项目**: NoiseSleep Phase 1 MVP  
**部署目标**: Cloudflare CDN + R2 存储  
**创建时间**: 2025年07月07日 23:30:00

---

## 📋 部署前准备检查

### ✅ 环境准备
- [ ] **Cloudflare账户已设置**
  - 账户已验证并可正常使用
  - 域名 noisesleep.com 已绑定到Cloudflare
  - 账户有足够的权限创建R2存储桶

- [ ] **开发环境准备**
  - Node.js 版本 >= 16.0.0
  - npm 或 yarn 包管理器已安装
  - Wrangler CLI 已安装: `npm install -g wrangler`
  - 已登录Cloudflare: `wrangler login`

- [ ] **项目文件检查**
  - 项目代码已更新到最新版本
  - 音频文件已整理在 `public/sounds/` 目录
  - 音频元数据文件 `src/data/audioData.ts` 已完善
  - 所有脚本文件已创建并设置执行权限

### ✅ 音频文件准备
- [ ] **文件结构检查**
  ```
  public/sounds/
  ├── rain/ (5个文件)
  ├── nature/ (3个文件)
  ├── ocean/ (1个文件)
  ├── noise/ (1个文件)
  └── [其他分类]
  ```

- [ ] **文件格式验证**
  - 所有音频文件格式为 MP3 或 WAV
  - 文件命名符合规范 (小写字母 + 连字符)
  - 文件大小合理 (建议单个文件 < 50MB)
  - 音频质量适合网络传输

- [ ] **元数据一致性**
  - audioData.ts 中的文件名与实际文件名一致
  - 分类名称与目录结构匹配
  - 所有音频文件都有对应的元数据条目

---

## 🛠️ 部署执行步骤

### 第一阶段: 代码准备
- [ ] **1.1 运行迁移脚本**
  ```bash
  ./scripts/migrate-to-cdn.sh
  ```
  - 检查脚本执行是否成功
  - 确认备份文件已创建
  - 验证代码修改是否正确

- [ ] **1.2 测试本地配置**
  ```bash
  node scripts/test-audio-config.js
  ```
  - 确认音频文件统计正确
  - 验证URL生成逻辑正常
  - 检查类型定义完整

- [ ] **1.3 本地开发测试**
  ```bash
  npm run dev
  ```
  - 应用启动正常
  - 音频播放功能正常
  - 无控制台错误

### 第二阶段: CDN配置
- [ ] **2.1 创建R2存储桶**
  - 存储桶名称: `noisesleep-audio`
  - 区域设置: 自动
  - 公共访问: 启用

- [ ] **2.2 配置自定义域名**
  - 自定义域名: `cdn.noisesleep.com`
  - SSL/TLS: 完全(严格)
  - DNS记录已正确配置

- [ ] **2.3 设置缓存规则**
  - URL模式: `cdn.noisesleep.com/sounds/*`
  - 缓存级别: 缓存所有内容
  - 边缘缓存TTL: 1个月
  - 浏览器缓存TTL: 1天

### 第三阶段: 文件上传
- [ ] **3.1 执行上传脚本**
  ```bash
  ./scripts/upload-to-cdn.sh
  ```
  - 所有音频文件上传成功
  - 目录结构正确创建
  - 文件权限设置为公共读取

- [ ] **3.2 验证上传结果**
  ```bash
  ./scripts/verify-cdn.sh
  ```
  - 所有测试文件可正常访问
  - HTTP状态码为200
  - 响应时间在合理范围内

### 第四阶段: 应用配置
- [ ] **4.1 更新环境变量**
  ```bash
  # 开发环境测试
  export NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds
  export NEXT_PUBLIC_CDN_PERCENTAGE=10  # 先10%测试
  ```

- [ ] **4.2 渐进式测试**
  - 10%流量使用CDN，90%使用本地
  - 监控错误率和性能指标
  - 确认CDN版本功能正常

- [ ] **4.3 全量切换**
  ```bash
  export NEXT_PUBLIC_CDN_PERCENTAGE=100
  npm run build
  ```

---

## 🧪 部署验证测试

### ✅ 功能测试
- [ ] **基础播放功能**
  - 音频文件可正常加载
  - 播放/暂停/停止功能正常
  - 音量控制正常
  - 进度条显示正确

- [ ] **睡眠模式测试**
  - 睡眠模式切换正常
  - 拉绳控制器功能正常
  - 定时器功能正常
  - 混音功能正常

- [ ] **多语言测试**
  - 中英文界面切换正常
  - 音频元数据显示正确
  - 分类和标签翻译正确

### ✅ 性能测试
- [ ] **加载性能**
  - 首次加载时间 < 3秒
  - 音频首字节时间 < 500ms
  - 页面交互响应 < 100ms

- [ ] **网络性能**
  - CDN缓存命中率 > 90%
  - 全球访问延迟 < 200ms
  - 带宽使用合理

- [ ] **移动端测试**
  - iOS Safari 播放正常
  - Android Chrome 播放正常
  - 响应式布局正确

### ✅ 兼容性测试
- [ ] **浏览器兼容性**
  - Chrome (最新版本)
  - Firefox (最新版本)
  - Safari (最新版本)
  - Edge (最新版本)

- [ ] **设备兼容性**
  - 桌面端 (Windows/Mac/Linux)
  - 平板端 (iPad/Android平板)
  - 手机端 (iPhone/Android手机)

---

## 🚨 故障排除指南

### 常见问题及解决方案

#### 问题1: 音频文件404错误
**症状**: 浏览器控制台显示音频文件无法加载
**排查步骤**:
1. 检查CDN域名DNS解析: `nslookup cdn.noisesleep.com`
2. 验证R2存储桶文件: 登录Cloudflare Dashboard检查
3. 确认文件路径: 检查URL生成逻辑
4. 测试直接访问: 在浏览器中直接访问音频URL

**解决方案**:
- 重新上传缺失的文件
- 检查文件权限设置
- 验证CDN配置

#### 问题2: CORS跨域错误
**症状**: 控制台显示CORS policy错误
**解决方案**:
1. 在R2存储桶设置中配置CORS
2. 允许的源: `https://noisesleep.com`, `https://www.noisesleep.com`
3. 允许的方法: `GET`, `HEAD`
4. 允许的头部: `Content-Type`, `Range`

#### 问题3: 缓存问题
**症状**: 音频文件更新后仍播放旧版本
**解决方案**:
1. 清除Cloudflare缓存
2. 使用版本号更新文件名
3. 检查缓存TTL设置

#### 问题4: 性能问题
**症状**: 音频加载缓慢或卡顿
**解决方案**:
1. 启用音频预加载
2. 优化音频文件大小
3. 检查CDN节点分布
4. 监控网络质量

---

## 📊 监控和维护

### ✅ 监控指标
- [ ] **性能监控**
  - 音频加载时间
  - CDN缓存命中率
  - 错误率统计
  - 用户体验指标

- [ ] **成本监控**
  - R2存储费用
  - CDN流量费用
  - 请求次数统计

- [ ] **用户反馈**
  - 播放失败报告
  - 性能问题反馈
  - 功能使用统计

### ✅ 定期维护
- [ ] **每周检查**
  - CDN服务状态
  - 音频文件完整性
  - 性能指标趋势

- [ ] **每月检查**
  - 成本使用情况
  - 缓存策略优化
  - 用户反馈分析

---

## 🔄 回滚方案

### 紧急回滚步骤
如果CDN出现严重问题，可以快速回滚到本地文件：

1. **立即回滚**
   ```bash
   export NEXT_PUBLIC_AUDIO_CDN_URL=/sounds
   npm run build
   ```

2. **验证回滚**
   - 确认应用使用本地文件
   - 测试所有功能正常
   - 监控错误日志

3. **问题修复**
   - 分析CDN问题原因
   - 修复配置或文件
   - 重新测试CDN功能

4. **重新部署**
   - 确认问题已解决
   - 渐进式重新启用CDN
   - 持续监控稳定性

---

## ✅ 部署完成确认

### 最终检查清单
- [ ] 所有音频文件可正常访问
- [ ] 应用功能完全正常
- [ ] 性能指标达到预期
- [ ] 监控系统已设置
- [ ] 文档已更新
- [ ] 团队已通知

### 部署成功标志
- ✅ CDN验证脚本100%通过
- ✅ 应用在生产环境正常运行
- ✅ 用户反馈积极
- ✅ 性能指标优于本地版本
- ✅ 成本在预算范围内

---

**检查清单版本**: v1.0  
**适用项目**: NoiseSleep Phase 1 MVP  
**最后更新**: 2025年07月07日 23:30:00
