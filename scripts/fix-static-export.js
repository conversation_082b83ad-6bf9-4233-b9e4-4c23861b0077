#!/usr/bin/env node

/**
 * NoiseSleep静态导出修复脚本
 * 解决next-intl在静态导出中的headers()问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复静态导出问题...');

// 1. 备份原始配置
const nextConfigPath = 'next.config.js';
const backupPath = 'next.config.js.backup';

if (!fs.existsSync(backupPath)) {
  fs.copyFileSync(nextConfigPath, backupPath);
  console.log('✅ 已备份原始next.config.js');
}

// 2. 创建静态导出友好的配置
const staticConfig = `const createNextIntlPlugin = require('next-intl/plugin');

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 静态导出配置
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  
  // 禁用图片优化（静态导出不支持）
  images: {
    unoptimized: true,
    domains: ['cdn.noisesleep.com'],
  },

  // 实验性功能
  experimental: {
    optimizePackageImports: ['@headlessui/react', 'framer-motion'],
  },

  // Webpack配置 - 音频文件处理
  webpack: (config, { isServer }) => {
    // 音频文件处理
    config.module.rules.push({
      test: /\\.(mp3|wav|ogg|m4a)$/,
      use: {
        loader: 'file-loader',
        options: {
          publicPath: '/_next/static/sounds/',
          outputPath: 'static/sounds/',
          name: '[name].[hash].[ext]',
        },
      },
    });

    // 优化音频文件加载
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
      };
    }

    return config;
  },
  
  // 压缩配置
  compress: true,
  
  // 性能优化
  poweredByHeader: false,
  
  // 环境变量
  env: {
    NEXT_PUBLIC_STATIC_EXPORT: 'true',
  },
};

// 集成next-intl插件
const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

module.exports = withNextIntl(nextConfig);
`;

fs.writeFileSync(nextConfigPath, staticConfig);
console.log('✅ 已更新next.config.js为静态导出模式');

// 3. 创建静态导出友好的i18n配置
const i18nRequestPath = 'src/i18n/request.ts';
const i18nBackupPath = 'src/i18n/request.ts.backup';

if (fs.existsSync(i18nRequestPath) && !fs.existsSync(i18nBackupPath)) {
  fs.copyFileSync(i18nRequestPath, i18nBackupPath);
  console.log('✅ 已备份原始i18n request.ts');
}

const staticI18nConfig = `import {getRequestConfig} from 'next-intl/server';
import {routing} from './routing';

export default getRequestConfig(async ({locale}) => {
  // 验证传入的locale参数
  if (!routing.locales.includes(locale as any)) {
    locale = routing.defaultLocale;
  }

  return {
    messages: (await import(\`../../messages/\${locale}.json\`)).default,
    timeZone: 'Asia/Shanghai',
    now: new Date(),
    formats: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        }
      }
    }
  };
});
`;

fs.writeFileSync(i18nRequestPath, staticI18nConfig);
console.log('✅ 已更新i18n配置为静态导出模式');

// 4. 创建静态导出友好的路由配置
const routingPath = 'src/i18n/routing.ts';
const routingBackupPath = 'src/i18n/routing.ts.backup';

if (fs.existsSync(routingPath) && !fs.existsSync(routingBackupPath)) {
  fs.copyFileSync(routingPath, routingBackupPath);
  console.log('✅ 已备份原始routing.ts');
}

const staticRoutingConfig = `import {defineRouting} from 'next-intl/routing';
import {createSharedPathnamesNavigation} from 'next-intl/navigation';

export const routing = defineRouting({
  // 支持的语言列表
  locales: ['en', 'zh'],
  
  // 默认语言
  defaultLocale: 'en',
  
  // 静态导出模式配置
  localePrefix: 'always',
  
  // 路径名配置
  pathnames: {
    '/': '/',
    '/sounds': '/sounds',
    '/sounds/[category]': '/sounds/[category]',
    '/favorites': '/favorites',
    '/mixing': '/mixing',
    '/landing': '/landing',
    '/audio-test': '/audio-test',
    '/test-player': '/test-player'
  }
});

// 导出导航函数
export const {Link, redirect, usePathname, useRouter} =
  createSharedPathnamesNavigation(routing);
`;

fs.writeFileSync(routingPath, staticRoutingConfig);
console.log('✅ 已更新路由配置为静态导出模式');

// 5. 添加静态导出页面配置
const pagesWithDynamic = [
  'src/app/[locale]/page.tsx',
  'src/app/[locale]/sounds/page.tsx',
  'src/app/[locale]/sounds/[category]/page.tsx',
  'src/app/[locale]/favorites/page.tsx',
  'src/app/[locale]/mixing/page.tsx',
  'src/app/[locale]/landing/page.tsx',
  'src/app/[locale]/audio-test/page.tsx',
  'src/app/[locale]/test-player/page.tsx'
];

pagesWithDynamic.forEach(pagePath => {
  if (fs.existsSync(pagePath)) {
    let content = fs.readFileSync(pagePath, 'utf8');
    
    // 检查是否已经有dynamic配置
    if (!content.includes('export const dynamic')) {
      // 在文件开头添加dynamic配置
      const lines = content.split('\n');
      const importEndIndex = lines.findIndex(line => 
        !line.startsWith('import') && 
        !line.startsWith('//') && 
        !line.startsWith('/*') && 
        !line.startsWith('*') && 
        !line.startsWith(' *') && 
        line.trim() !== ''
      );
      
      if (importEndIndex > -1) {
        lines.splice(importEndIndex, 0, '', '// 静态导出配置', 'export const dynamic = \'force-static\';');
        content = lines.join('\n');
        fs.writeFileSync(pagePath, content);
        console.log(\`✅ 已为 \${pagePath} 添加静态导出配置\`);
      }
    }
  }
});

// 6. 更新package.json脚本
const packageJsonPath = 'package.json';
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// 添加静态导出脚本
packageJson.scripts = {
  ...packageJson.scripts,
  'build:static': 'next build',
  'build:cloudflare': 'npm run build:static && npm run optimize:static',
  'restore:config': 'node scripts/restore-config.js'
};

fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log('✅ 已更新package.json脚本');

// 7. 创建配置恢复脚本
const restoreScript = \`#!/usr/bin/env node

/**
 * 恢复原始配置文件
 */

const fs = require('fs');

console.log('🔄 恢复原始配置文件...');

const filesToRestore = [
  { backup: 'next.config.js.backup', original: 'next.config.js' },
  { backup: 'src/i18n/request.ts.backup', original: 'src/i18n/request.ts' },
  { backup: 'src/i18n/routing.ts.backup', original: 'src/i18n/routing.ts' }
];

filesToRestore.forEach(({ backup, original }) => {
  if (fs.existsSync(backup)) {
    fs.copyFileSync(backup, original);
    console.log(\\\`✅ 已恢复 \\\${original}\\\`);
  }
});

console.log('🎉 配置文件恢复完成！');
\`;

fs.writeFileSync('scripts/restore-config.js', restoreScript);
fs.chmodSync('scripts/restore-config.js', '755');
console.log('✅ 已创建配置恢复脚本');

console.log('\\n🎉 静态导出修复完成！');
console.log('\\n📋 接下来的步骤：');
console.log('1. 运行 npm run build:static 测试构建');
console.log('2. 如果成功，可以部署到Cloudflare Pages');
console.log('3. 如需恢复原始配置，运行 npm run restore:config');
console.log('\\n⚠️  注意：静态导出模式下某些动态功能可能受限');
