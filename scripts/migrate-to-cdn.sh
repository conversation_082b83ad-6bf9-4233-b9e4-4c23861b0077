#!/bin/bash
# scripts/migrate-to-cdn.sh
# 自动迁移代码以支持CDN的脚本

set -e

echo "🔄 开始迁移NoiseSleep代码以支持CDN..."

# 检查是否在项目根目录
if [ ! -f "package.json" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 备份原始文件
backup_dir="./backups/cdn-migration-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"

echo "📦 创建备份目录: $backup_dir"

# 需要修改的文件列表
files_to_backup=(
    "src/hooks/useAudioPlayer.ts"
    "src/components/AudioPlayer/AudioPlayer.tsx"
    "next.config.js"
    ".env.local"
)

# 备份文件
for file in "${files_to_backup[@]}"; do
    if [ -f "$file" ]; then
        echo "  📄 备份: $file"
        cp "$file" "$backup_dir/"
    fi
done

echo "✅ 文件备份完成"

# 1. 修改 useAudioPlayer.ts
echo "🔧 修改 useAudioPlayer.ts..."
if [ -f "src/hooks/useAudioPlayer.ts" ]; then
    # 添加import语句
    if ! grep -q "import { getAudioUrl }" src/hooks/useAudioPlayer.ts; then
        sed -i '' '1i\
import { getAudioUrl } from '\''@/config/audio'\'';
' src/hooks/useAudioPlayer.ts
    fi
    
    # 替换audioUrl生成逻辑
    sed -i '' 's|const categoryMap.*|// 使用新的getAudioUrl函数|g' src/hooks/useAudioPlayer.ts
    sed -i '' 's|const folderName.*|const audioUrl = getAudioUrl(sound.category, sound.filename);|g' src/hooks/useAudioPlayer.ts
    sed -i '' 's|const audioUrl = `/Sounds.*|const audioUrl = getAudioUrl(sound.category, sound.filename);|g' src/hooks/useAudioPlayer.ts
    
    echo "  ✅ useAudioPlayer.ts 修改完成"
else
    echo "  ⚠️  useAudioPlayer.ts 文件不存在"
fi

# 2. 更新环境变量文件
echo "🔧 更新环境变量..."
if [ ! -f ".env.local" ]; then
    touch .env.local
fi

# 添加CDN配置到.env.local
if ! grep -q "NEXT_PUBLIC_AUDIO_CDN_URL" .env.local; then
    echo "" >> .env.local
    echo "# 音频CDN配置" >> .env.local
    echo "NEXT_PUBLIC_AUDIO_CDN_URL=/sounds" >> .env.local
    echo "NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD=false" >> .env.local
    echo "NEXT_PUBLIC_AUDIO_QUALITY=high" >> .env.local
    echo "NEXT_PUBLIC_CDN_PERCENTAGE=0" >> .env.local
fi

echo "  ✅ 环境变量配置完成"

# 3. 创建生产环境配置
echo "🔧 创建生产环境配置..."
cat > .env.production << EOF
# NoiseSleep生产环境配置
NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds
NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD=true
NEXT_PUBLIC_AUDIO_QUALITY=high
NEXT_PUBLIC_CDN_PERCENTAGE=100
EOF

echo "  ✅ 生产环境配置创建完成"

# 4. 更新next.config.js
echo "🔧 更新 next.config.js..."
if [ -f "next.config.js" ]; then
    # 检查是否已经有CDN相关配置
    if ! grep -q "cdn.noisesleep.com" next.config.js; then
        # 备份原始配置
        cp next.config.js next.config.js.backup
        
        # 创建新的配置文件
        cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  
  // 添加CDN域名到图片和媒体优化
  images: {
    domains: ['cdn.noisesleep.com'],
  },
  
  // 添加音频文件的静态资源优化
  async headers() {
    return [
      {
        source: '/sounds/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  
  // 生产环境重写规则 (可选，用于渐进迁移)
  async rewrites() {
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_AUDIO_CDN_URL?.startsWith('https://')) {
      return [
        {
          source: '/sounds/:path*',
          destination: `${process.env.NEXT_PUBLIC_AUDIO_CDN_URL}/:path*`,
        },
      ];
    }
    return [];
  },
};

module.exports = nextConfig;
EOF
        echo "  ✅ next.config.js 更新完成"
    else
        echo "  ℹ️  next.config.js 已包含CDN配置"
    fi
else
    echo "  ⚠️  next.config.js 文件不存在，跳过"
fi

# 5. 创建package.json脚本
echo "🔧 添加部署脚本到 package.json..."
if command -v jq &> /dev/null; then
    # 使用jq添加脚本
    jq '.scripts["upload:cdn"] = "./scripts/upload-to-cdn.sh"' package.json > package.json.tmp
    jq '.scripts["verify:cdn"] = "./scripts/verify-cdn.sh"' package.json.tmp > package.json.tmp2
    jq '.scripts["migrate:cdn"] = "./scripts/migrate-to-cdn.sh"' package.json.tmp2 > package.json.tmp3
    mv package.json.tmp3 package.json
    rm -f package.json.tmp package.json.tmp2
    echo "  ✅ package.json 脚本添加完成"
else
    echo "  ⚠️  jq未安装，请手动添加以下脚本到package.json:"
    echo '    "upload:cdn": "./scripts/upload-to-cdn.sh",'
    echo '    "verify:cdn": "./scripts/verify-cdn.sh",'
    echo '    "migrate:cdn": "./scripts/migrate-to-cdn.sh"'
fi

# 6. 设置脚本执行权限
echo "🔧 设置脚本执行权限..."
chmod +x scripts/*.sh
echo "  ✅ 脚本权限设置完成"

# 7. 创建README文档
echo "📝 创建CDN部署文档..."
cat > CDN_DEPLOYMENT_README.md << 'EOF'
# NoiseSleep CDN 部署指南

## 快速开始

### 1. 上传音频文件到CDN
```bash
npm run upload:cdn
```

### 2. 验证CDN部署
```bash
npm run verify:cdn
```

### 3. 切换到CDN模式
```bash
# 更新环境变量
export NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds

# 重新构建应用
npm run build
```

## 环境变量说明

- `NEXT_PUBLIC_AUDIO_CDN_URL`: 音频CDN基础URL
- `NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD`: 是否启用音频预加载
- `NEXT_PUBLIC_AUDIO_QUALITY`: 音频质量设置 (low/medium/high)
- `NEXT_PUBLIC_CDN_PERCENTAGE`: CDN使用百分比 (0-100)

## 故障排除

### 音频无法播放
1. 检查CDN域名是否正确配置
2. 验证音频文件是否成功上传
3. 检查浏览器控制台错误信息

### 性能问题
1. 启用音频预加载: `NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD=true`
2. 检查CDN缓存设置
3. 监控网络请求时间

## 回滚方案

如需回滚到本地文件:
```bash
export NEXT_PUBLIC_AUDIO_CDN_URL=/sounds
npm run build
```
EOF

echo "  ✅ CDN部署文档创建完成"

echo ""
echo "🎉 CDN迁移准备完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 检查修改的文件是否正确"
echo "2. 测试本地开发环境: npm run dev"
echo "3. 上传音频文件: npm run upload:cdn"
echo "4. 验证CDN部署: npm run verify:cdn"
echo "5. 切换到CDN模式并重新构建"
echo ""
echo "📁 备份文件位置: $backup_dir"
echo "📖 详细文档: CDN_DEPLOYMENT_README.md"
echo ""
echo "⚠️  注意: 请在切换到CDN前充分测试！"
