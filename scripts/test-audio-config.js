#!/usr/bin/env node
// scripts/test-audio-config.js
// 测试音频配置的Node.js脚本

const fs = require('fs');
const path = require('path');

console.log('🧪 测试NoiseSleep音频配置...\n');

// 模拟环境变量
process.env.NODE_ENV = 'development';
process.env.NEXT_PUBLIC_AUDIO_CDN_URL = '/sounds';

// 测试数据
const testAudioItems = [
  { category: 'rain', filename: 'light-rain.mp3' },
  { category: 'nature', filename: 'campfire.mp3' },
  { category: 'ocean', filename: 'ocean-waves.wav' },
  { category: 'noise', filename: 'pink-noise.wav' },
];

// 检查音频配置文件是否存在
const audioConfigPath = path.join(__dirname, '../src/config/audio.ts');
if (!fs.existsSync(audioConfigPath)) {
  console.error('❌ 音频配置文件不存在:', audioConfigPath);
  process.exit(1);
}

console.log('✅ 音频配置文件存在');

// 检查本地音频文件
console.log('\n📁 检查本地音频文件:');
const soundsDir = path.join(__dirname, '../public/sounds');

if (!fs.existsSync(soundsDir)) {
  console.error('❌ 音频目录不存在:', soundsDir);
  process.exit(1);
}

const categories = fs.readdirSync(soundsDir, { withFileTypes: true })
  .filter(dirent => dirent.isDirectory())
  .map(dirent => dirent.name);

console.log('📂 发现音频分类:', categories.join(', '));

// 统计每个分类的文件数量
let totalFiles = 0;
categories.forEach(category => {
  const categoryPath = path.join(soundsDir, category);
  const files = fs.readdirSync(categoryPath)
    .filter(file => file.endsWith('.mp3') || file.endsWith('.wav'));
  
  console.log(`  ${category}: ${files.length} 个文件`);
  totalFiles += files.length;
  
  // 显示前3个文件名
  if (files.length > 0) {
    const sampleFiles = files.slice(0, 3);
    console.log(`    示例: ${sampleFiles.join(', ')}`);
  }
});

console.log(`\n📊 总计: ${totalFiles} 个音频文件`);

// 测试URL生成逻辑
console.log('\n🔗 测试URL生成:');

// 简单的URL生成函数 (模拟audio.ts中的逻辑)
function getAudioUrl(category, filename) {
  const baseUrl = process.env.NEXT_PUBLIC_AUDIO_CDN_URL || '/sounds';
  const folderName = category.toLowerCase();
  return `${baseUrl}/${folderName}/${filename}`;
}

testAudioItems.forEach(item => {
  const url = getAudioUrl(item.category, item.filename);
  console.log(`  ${item.category}/${item.filename} → ${url}`);
});

// 检查audioData.ts文件
console.log('\n📋 检查音频元数据:');
const audioDataPath = path.join(__dirname, '../src/data/audioData.ts');

if (fs.existsSync(audioDataPath)) {
  const audioDataContent = fs.readFileSync(audioDataPath, 'utf8');
  
  // 统计元数据条目
  const exportMatches = audioDataContent.match(/export const audioData.*?\[/s);
  if (exportMatches) {
    const itemMatches = audioDataContent.match(/{\s*id:/g);
    const itemCount = itemMatches ? itemMatches.length : 0;
    console.log(`✅ 音频元数据文件存在，包含 ${itemCount} 个条目`);
    
    // 检查分类分布
    const categories = ['rain', 'nature', 'noise', 'animals', 'things', 'transport', 'urban', 'places', 'ocean'];
    categories.forEach(cat => {
      const regex = new RegExp(`category:\\s*['"]${cat}['"]`, 'g');
      const matches = audioDataContent.match(regex);
      const count = matches ? matches.length : 0;
      if (count > 0) {
        console.log(`  ${cat}: ${count} 个条目`);
      }
    });
  } else {
    console.log('⚠️  无法解析音频元数据结构');
  }
} else {
  console.log('❌ 音频元数据文件不存在:', audioDataPath);
}

// 检查TypeScript类型定义
console.log('\n🔍 检查类型定义:');
const typesPath = path.join(__dirname, '../src/types/audio.ts');

if (fs.existsSync(typesPath)) {
  const typesContent = fs.readFileSync(typesPath, 'utf8');
  
  if (typesContent.includes('MultilingualAudioItem')) {
    console.log('✅ MultilingualAudioItem 类型定义存在');
  } else {
    console.log('⚠️  MultilingualAudioItem 类型定义未找到');
  }
  
  if (typesContent.includes('category')) {
    console.log('✅ category 字段类型定义存在');
  } else {
    console.log('⚠️  category 字段类型定义未找到');
  }
} else {
  console.log('❌ 类型定义文件不存在:', typesPath);
}

// 环境变量检查
console.log('\n🌍 环境变量检查:');
const envVars = [
  'NEXT_PUBLIC_AUDIO_CDN_URL',
  'NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD',
  'NEXT_PUBLIC_AUDIO_QUALITY',
  'NEXT_PUBLIC_CDN_PERCENTAGE'
];

envVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}=${value}`);
  } else {
    console.log(`⚠️  ${varName} 未设置`);
  }
});

// 生成配置报告
console.log('\n📊 配置报告:');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log(`项目根目录: ${path.join(__dirname, '..')}`);
console.log(`音频文件目录: ${soundsDir}`);
console.log(`音频文件总数: ${totalFiles}`);
console.log(`音频分类数: ${categories.length}`);
console.log(`当前环境: ${process.env.NODE_ENV}`);
console.log(`CDN基础URL: ${process.env.NEXT_PUBLIC_AUDIO_CDN_URL || '未设置'}`);
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

console.log('\n🎉 配置测试完成！');

// 生成建议
console.log('\n💡 建议:');
if (totalFiles < 20) {
  console.log('⚠️  音频文件数量较少，建议添加更多音频文件');
}

if (!process.env.NEXT_PUBLIC_AUDIO_CDN_URL) {
  console.log('💡 建议设置 NEXT_PUBLIC_AUDIO_CDN_URL 环境变量');
}

if (categories.length < 5) {
  console.log('💡 建议增加更多音频分类以丰富用户选择');
}

console.log('\n📋 下一步操作:');
console.log('1. 运行 npm run upload:cdn 上传音频文件');
console.log('2. 运行 npm run verify:cdn 验证CDN部署');
console.log('3. 更新环境变量切换到CDN模式');
console.log('4. 重新构建和部署应用');
