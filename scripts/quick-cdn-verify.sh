#!/bin/bash
# 快速CDN验证脚本

set -e

CDN_BASE_URL="https://cdn.noisesleep.com/sounds"
LOCAL_SOUNDS_DIR="/Users/<USER>/Documents/NoiseSleep/sounds"

echo "🧪 快速CDN验证开始..."
echo "📁 本地目录: $LOCAL_SOUNDS_DIR"
echo "🌐 CDN基础URL: $CDN_BASE_URL"
echo ""

# 统计变量
total_files=0
success_count=0
failed_files=()

# 扫描所有音频文件
echo "🔍 扫描本地音频文件..."
while IFS= read -r -d '' file; do
    relative_path="${file#$LOCAL_SOUNDS_DIR/}"
    ((total_files++))
    
    # 测试CDN访问
    url="$CDN_BASE_URL/$relative_path"
    
    echo -n "[$total_files] 测试: $relative_path ... "
    
    # 使用curl测试
    if curl -s --head --max-time 5 "$url" | head -n 1 | grep -q "200"; then
        echo "✅"
        ((success_count++))
    else
        echo "❌"
        failed_files+=("$relative_path")
    fi
    
done < <(find "$LOCAL_SOUNDS_DIR" -type f \( -name "*.mp3" -o -name "*.wav" \) -print0)

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📊 验证结果汇总"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎵 总文件数: $total_files"
echo "✅ 成功验证: $success_count"
echo "❌ 验证失败: $((total_files - success_count))"

if [ $total_files -gt 0 ]; then
    success_rate=$(echo "scale=1; $success_count * 100 / $total_files" | bc -l 2>/dev/null || echo "0")
    echo "📈 成功率: ${success_rate}%"
fi

if [ ${#failed_files[@]} -gt 0 ]; then
    echo ""
    echo "❌ 失败的文件列表:"
    for failed_file in "${failed_files[@]}"; do
        echo "  - $failed_file"
    done
fi

echo ""
if [ $success_count -eq $total_files ]; then
    echo "🎉 所有文件验证成功！可以安全切换到CDN模式。"
    exit 0
else
    echo "⚠️  部分文件验证失败，需要修复后再切换到CDN模式。"
    exit 1
fi
