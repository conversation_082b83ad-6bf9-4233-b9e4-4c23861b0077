#!/bin/bash

# NoiseSleep CDN功能验证脚本
# 测试CDN模式下的应用功能

set -e

echo "🎵 NoiseSleep CDN功能验证开始"
echo "================================"

# 配置
BASE_URL="http://localhost:3000"
CDN_URL="https://cdn.noisesleep.com/sounds"
TEST_RESULTS_FILE="cdn_functionality_test_$(date +%Y%m%d_%H%M%S).md"

# 创建测试结果文件
cat > "$TEST_RESULTS_FILE" << 'EOF'
# NoiseSleep CDN功能验证报告

## 测试概览
- **测试时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **CDN地址**: https://cdn.noisesleep.com/sounds
- **应用地址**: http://localhost:3000
- **测试模式**: CDN 100%

## 测试结果

EOF

echo "📋 测试结果将保存到: $TEST_RESULTS_FILE"

# 1. 检查应用是否正常启动
echo ""
echo "1️⃣ 检查应用启动状态..."
if curl -s "$BASE_URL" > /dev/null; then
    echo "✅ 应用正常运行"
    echo "### 1. 应用启动状态: ✅ 正常" >> "$TEST_RESULTS_FILE"
else
    echo "❌ 应用无法访问"
    echo "### 1. 应用启动状态: ❌ 失败" >> "$TEST_RESULTS_FILE"
    exit 1
fi

# 2. 检查主要页面可访问性
echo ""
echo "2️⃣ 检查主要页面..."
pages=("/" "/zh" "/en" "/zh/sounds" "/en/sounds" "/zh/mixing" "/en/mixing")
page_results=""

for page in "${pages[@]}"; do
    echo "   检查页面: $page"
    if curl -s "$BASE_URL$page" > /dev/null; then
        echo "   ✅ $page - 正常"
        page_results="$page_results\n- $page: ✅ 正常"
    else
        echo "   ❌ $page - 失败"
        page_results="$page_results\n- $page: ❌ 失败"
    fi
done

echo -e "### 2. 页面可访问性:$page_results" >> "$TEST_RESULTS_FILE"

# 3. 检查CDN音频文件可访问性（抽样测试）
echo ""
echo "3️⃣ 检查CDN音频文件..."
sample_files=(
    "nature/waterfall.mp3"
    "rain/heavy-rain.mp3"
    "urban/highway.mp3"
    "animals/birds.mp3"
    "animals/cat-purring.mp3"
    "things/wind-chimes.mp3"
    "noise/white-noise.wav"
    "places/airport.mp3"
)

cdn_results=""
cdn_success=0
cdn_total=${#sample_files[@]}

for file in "${sample_files[@]}"; do
    echo "   检查文件: $file"
    if curl -s --head "$CDN_URL/$file" | grep -q "200"; then
        echo "   ✅ $file - 可访问"
        cdn_results="$cdn_results\n- $file: ✅ 可访问"
        ((cdn_success++))
    else
        echo "   ❌ $file - 无法访问"
        cdn_results="$cdn_results\n- $file: ❌ 无法访问"
    fi
done

echo -e "### 3. CDN音频文件测试 ($cdn_success/$cdn_total):$cdn_results" >> "$TEST_RESULTS_FILE"

# 4. 检查环境变量配置
echo ""
echo "4️⃣ 检查环境变量配置..."
echo "### 4. 环境变量配置:" >> "$TEST_RESULTS_FILE"

if [ "$NEXT_PUBLIC_AUDIO_CDN_URL" = "https://cdn.noisesleep.com/sounds" ]; then
    echo "✅ NEXT_PUBLIC_AUDIO_CDN_URL: $NEXT_PUBLIC_AUDIO_CDN_URL"
    echo "- NEXT_PUBLIC_AUDIO_CDN_URL: ✅ $NEXT_PUBLIC_AUDIO_CDN_URL" >> "$TEST_RESULTS_FILE"
else
    echo "❌ NEXT_PUBLIC_AUDIO_CDN_URL 配置错误"
    echo "- NEXT_PUBLIC_AUDIO_CDN_URL: ❌ 配置错误" >> "$TEST_RESULTS_FILE"
fi

if [ "$NEXT_PUBLIC_CDN_PERCENTAGE" = "100" ]; then
    echo "✅ NEXT_PUBLIC_CDN_PERCENTAGE: $NEXT_PUBLIC_CDN_PERCENTAGE"
    echo "- NEXT_PUBLIC_CDN_PERCENTAGE: ✅ $NEXT_PUBLIC_CDN_PERCENTAGE" >> "$TEST_RESULTS_FILE"
else
    echo "❌ NEXT_PUBLIC_CDN_PERCENTAGE 配置错误"
    echo "- NEXT_PUBLIC_CDN_PERCENTAGE: ❌ 配置错误" >> "$TEST_RESULTS_FILE"
fi

if [ "$NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD" = "true" ]; then
    echo "✅ NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD: $NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD"
    echo "- NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD: ✅ $NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD" >> "$TEST_RESULTS_FILE"
else
    echo "❌ NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD 配置错误"
    echo "- NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD: ❌ 配置错误" >> "$TEST_RESULTS_FILE"
fi

# 5. 性能测试（简单的响应时间测试）
echo ""
echo "5️⃣ 性能测试..."
echo "### 5. 性能测试:" >> "$TEST_RESULTS_FILE"

# 测试主页加载时间
echo "   测试主页加载时间..."
home_time=$(curl -o /dev/null -s -w '%{time_total}' "$BASE_URL")
echo "   主页加载时间: ${home_time}s"
echo "- 主页加载时间: ${home_time}s" >> "$TEST_RESULTS_FILE"

# 测试CDN音频文件下载时间
echo "   测试CDN音频文件下载时间..."
audio_time=$(curl -o /dev/null -s -w '%{time_total}' "$CDN_URL/nature/waterfall.mp3")
echo "   音频文件下载时间: ${audio_time}s"
echo "- 音频文件下载时间: ${audio_time}s" >> "$TEST_RESULTS_FILE"

# 6. 总结
echo ""
echo "6️⃣ 测试总结..."
total_score=$((cdn_success * 100 / cdn_total))

echo "### 6. 测试总结:" >> "$TEST_RESULTS_FILE"
echo "- CDN文件可访问率: $total_score%" >> "$TEST_RESULTS_FILE"
echo "- 应用状态: 正常运行" >> "$TEST_RESULTS_FILE"
echo "- CDN集成: 已启用" >> "$TEST_RESULTS_FILE"

if [ $total_score -ge 80 ]; then
    echo "✅ CDN功能验证通过 (得分: $total_score%)"
    echo "- **整体评估**: ✅ 通过 (得分: $total_score%)" >> "$TEST_RESULTS_FILE"
else
    echo "⚠️  CDN功能验证部分通过 (得分: $total_score%)"
    echo "- **整体评估**: ⚠️ 部分通过 (得分: $total_score%)" >> "$TEST_RESULTS_FILE"
fi

echo ""
echo "📊 详细测试报告已保存到: $TEST_RESULTS_FILE"
echo ""
echo "🎉 CDN功能验证完成！"

# 显示下一步建议
echo ""
echo "📋 下一步建议:"
echo "1. 查看详细测试报告: cat $TEST_RESULTS_FILE"
echo "2. 在浏览器中测试: open $BASE_URL"
echo "3. 测试音频播放功能"
echo "4. 测试音频混合功能"
echo "5. 测试睡眠定时器功能"
