#!/usr/bin/env node

/**
 * 生成测试用的音频文件
 * 这个脚本会创建一些短的静音音频文件用于测试播放器功能
 */

const fs = require('fs');
const path = require('path');

// 创建一个简单的 WAV 文件头（44字节）
function createWavHeader(sampleRate, numChannels, bitsPerSample, dataSize) {
  const buffer = Buffer.alloc(44);
  
  // RIFF header
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + dataSize, 4);
  buffer.write('WAVE', 8);
  
  // fmt chunk
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16); // chunk size
  buffer.writeUInt16LE(1, 20);  // audio format (PCM)
  buffer.writeUInt16LE(numChannels, 22);
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * numChannels * bitsPerSample / 8, 28); // byte rate
  buffer.writeUInt16LE(numChannels * bitsPerSample / 8, 32); // block align
  buffer.writeUInt16LE(bitsPerSample, 34);
  
  // data chunk
  buffer.write('data', 36);
  buffer.writeUInt32LE(dataSize, 40);
  
  return buffer;
}

// 创建静音音频数据
function createSilentAudio(durationSeconds, sampleRate = 44100, numChannels = 2, bitsPerSample = 16) {
  const samplesPerChannel = Math.floor(durationSeconds * sampleRate);
  const dataSize = samplesPerChannel * numChannels * bitsPerSample / 8;
  const header = createWavHeader(sampleRate, numChannels, bitsPerSample, dataSize);
  const data = Buffer.alloc(dataSize, 0); // 静音数据（全为0）
  
  return Buffer.concat([header, data]);
}

// 测试音频文件配置
const testAudioFiles = [
  {
    path: 'public/sounds/rain/gentle-rain.wav',
    duration: 30, // 30秒
    description: '轻柔雨声测试文件'
  },
  {
    path: 'public/sounds/rain/heavy-rain.wav',
    duration: 30,
    description: '暴雨声测试文件'
  },
  {
    path: 'public/sounds/rain/rain-drops.wav',
    duration: 30,
    description: '雨滴声测试文件'
  },
  {
    path: 'public/sounds/nature/forest-birds.wav',
    duration: 30,
    description: '森林鸟声测试文件'
  },
  {
    path: 'public/sounds/nature/wind-leaves.wav',
    duration: 30,
    description: '风吹树叶测试文件'
  },
  {
    path: 'public/sounds/ocean/ocean-waves.wav',
    duration: 30,
    description: '海浪声测试文件'
  },
  {
    path: 'public/sounds/white-noise/pink-noise.wav',
    duration: 30,
    description: '粉红噪音测试文件'
  }
];

// 生成测试音频文件
console.log('开始生成测试音频文件...');

testAudioFiles.forEach(({ path: filePath, duration, description }) => {
  try {
    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 生成音频数据
    const audioData = createSilentAudio(duration);
    
    // 写入文件
    fs.writeFileSync(filePath, audioData);
    
    console.log(`✓ 已生成: ${filePath} (${duration}秒) - ${description}`);
  } catch (error) {
    console.error(`✗ 生成失败: ${filePath} - ${error.message}`);
  }
});

console.log('\n测试音频文件生成完成！');
console.log('注意：这些是静音测试文件，用于验证播放器功能。');
console.log('在生产环境中，请替换为真实的音频文件。');
