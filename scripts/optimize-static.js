#!/usr/bin/env node

/**
 * NoiseSleep静态文件优化脚本
 * 为Cloudflare Pages部署优化静态导出文件
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const OUTPUT_DIR = 'out';
const DIST_DIR = 'dist';

console.log('🔧 开始优化静态文件...');

// 1. 检查输出目录
if (!fs.existsSync(OUTPUT_DIR)) {
  console.error('❌ 静态导出目录不存在，请先运行 npm run build:static');
  process.exit(1);
}

// 2. 创建分发目录
if (fs.existsSync(DIST_DIR)) {
  fs.rmSync(DIST_DIR, { recursive: true, force: true });
}
fs.mkdirSync(DIST_DIR, { recursive: true });

// 3. 复制静态文件
console.log('📁 复制静态文件...');
execSync(`cp -r ${OUTPUT_DIR}/* ${DIST_DIR}/`, { stdio: 'inherit' });

// 4. 创建_headers文件（Cloudflare Pages配置）
console.log('📝 创建_headers文件...');
const headersContent = `
# 全局安全头部
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: microphone=(), camera=(), geolocation=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.clarity.ms; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' blob: https://cdn.noisesleep.com; connect-src 'self' https://cdn.noisesleep.com https://www.google-analytics.com https://www.googletagmanager.com https://www.clarity.ms;

# 静态资源缓存
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable

# 音频文件缓存
*.mp3
  Content-Type: audio/mpeg
  Cache-Control: public, max-age=2592000
  Accept-Ranges: bytes

*.wav
  Content-Type: audio/wav
  Cache-Control: public, max-age=2592000
  Accept-Ranges: bytes

# HTML文件
*.html
  Cache-Control: public, max-age=3600

# 多语言支持
/zh/*
  Content-Language: zh-CN
  Vary: Accept-Language

/
  Content-Language: en
  Vary: Accept-Language
`;

fs.writeFileSync(path.join(DIST_DIR, '_headers'), headersContent.trim());

// 5. 创建_redirects文件
console.log('🔄 创建_redirects文件...');
const redirectsContent = `
# WWW重定向
https://www.noisesleep.com/* https://noisesleep.com/:splat 301!

# 默认语言重定向（可选）
# /sounds /sounds/ 200
# /zh/sounds /zh/sounds/ 200

# SPA回退
/* /index.html 200
`;

fs.writeFileSync(path.join(DIST_DIR, '_redirects'), redirectsContent.trim());

// 6. 优化HTML文件
console.log('🎯 优化HTML文件...');
function optimizeHtmlFiles(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      optimizeHtmlFiles(filePath);
    } else if (file.endsWith('.html')) {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 移除不必要的空白
      content = content.replace(/>\s+</g, '><');
      
      // 添加预加载提示
      if (content.includes('<head>')) {
        const preloadHints = `
  <link rel="dns-prefetch" href="https://cdn.noisesleep.com">
  <link rel="dns-prefetch" href="https://www.googletagmanager.com">
  <link rel="dns-prefetch" href="https://fonts.googleapis.com">
  <link rel="dns-prefetch" href="https://fonts.gstatic.com">
        `;
        content = content.replace('<head>', '<head>' + preloadHints);
      }
      
      fs.writeFileSync(filePath, content);
    }
  });
}

optimizeHtmlFiles(DIST_DIR);

// 7. 生成部署信息
console.log('📊 生成部署信息...');
const deployInfo = {
  buildTime: new Date().toISOString(),
  version: require('../package.json').version,
  environment: 'production',
  platform: 'cloudflare-pages',
  features: {
    internationalization: true,
    analytics: true,
    audioStreaming: true,
    staticExport: true
  },
  urls: {
    main: 'https://noisesleep.com',
    chinese: 'https://noisesleep.com/zh',
    cdn: 'https://cdn.noisesleep.com'
  }
};

fs.writeFileSync(
  path.join(DIST_DIR, 'deploy-info.json'), 
  JSON.stringify(deployInfo, null, 2)
);

// 8. 统计文件信息
console.log('📈 统计部署文件...');
function getDirectorySize(dir) {
  let size = 0;
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      size += getDirectorySize(filePath);
    } else {
      size += stat.size;
    }
  });
  
  return size;
}

const totalSize = getDirectorySize(DIST_DIR);
const sizeInMB = (totalSize / 1024 / 1024).toFixed(2);

console.log('✅ 静态文件优化完成！');
console.log(`📦 总大小: ${sizeInMB} MB`);
console.log(`📁 输出目录: ${DIST_DIR}/`);
console.log('🚀 准备部署到Cloudflare Pages');

// 9. 验证关键文件
const requiredFiles = ['index.html', '_headers', '_redirects'];
const missingFiles = requiredFiles.filter(file => !fs.existsSync(path.join(DIST_DIR, file)));

if (missingFiles.length > 0) {
  console.error('❌ 缺少关键文件:', missingFiles.join(', '));
  process.exit(1);
}

console.log('✅ 所有关键文件已生成');
console.log('🎉 优化完成，可以部署了！');
