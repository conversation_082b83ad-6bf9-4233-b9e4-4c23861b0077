#!/bin/bash
# scripts/upload-to-cdn.sh
# NoiseSleep音频文件CDN上传脚本

set -e

echo "🚀 开始上传NoiseSleep音频文件到Cloudflare R2..."

# 检查wrangler是否已安装
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI未安装，请先运行: npm install -g wrangler"
    exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "🔐 请先登录Cloudflare: wrangler login"
    exit 1
fi

BUCKET_NAME="noisesleep-audio"
LOCAL_SOUNDS_DIR="./public/sounds"

# 上传函数
upload_category() {
    local category=$1
    local local_dir=$2
    
    echo "📁 上传 $category 分类..."
    
    if [ -d "$local_dir" ]; then
        for file in "$local_dir"/*; do
            if [ -f "$file" ]; then
                filename=$(basename "$file")
                echo "  ⬆️  上传: $filename"
                wrangler r2 object put "$BUCKET_NAME/sounds/$category/$filename" --file="$file"
                
                # 验证上传是否成功
                if wrangler r2 object get "$BUCKET_NAME/sounds/$category/$filename" --file=/dev/null 2>/dev/null; then
                    echo "  ✅ 验证成功: $filename"
                else
                    echo "  ❌ 验证失败: $filename"
                fi
            fi
        done
        echo "✅ $category 分类上传完成"
    else
        echo "⚠️  目录不存在: $local_dir"
    fi
}

# 创建存储桶 (如果不存在)
echo "🪣 检查存储桶..."
if ! wrangler r2 bucket list | grep -q "$BUCKET_NAME"; then
    echo "📦 创建存储桶: $BUCKET_NAME"
    wrangler r2 bucket create "$BUCKET_NAME"
else
    echo "✅ 存储桶已存在: $BUCKET_NAME"
fi

# 按分类上传
upload_category "rain" "$LOCAL_SOUNDS_DIR/rain"
upload_category "nature" "$LOCAL_SOUNDS_DIR/nature"  
upload_category "ocean" "$LOCAL_SOUNDS_DIR/ocean"
upload_category "noise" "$LOCAL_SOUNDS_DIR/Noise"

# 上传白噪音文件 (处理重复目录)
if [ -d "$LOCAL_SOUNDS_DIR/white-noise" ]; then
    upload_category "noise" "$LOCAL_SOUNDS_DIR/white-noise"
fi

echo ""
echo "🎉 所有音频文件上传完成！"
echo "🔗 CDN地址: https://cdn.noisesleep.com/sounds/"
echo ""
echo "📋 下一步操作:"
echo "1. 运行验证脚本: ./scripts/verify-cdn.sh"
echo "2. 更新环境变量: NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds"
echo "3. 重新构建应用: npm run build"
