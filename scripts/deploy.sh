#!/bin/bash

# NoiseSleep项目Cloudflare部署脚本
# 用于自动化部署到Cloudflare Pages

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    if ! command -v wrangler &> /dev/null; then
        log_warning "Wrangler CLI 未安装，正在安装..."
        npm install -g wrangler
    fi
    
    log_success "依赖检查完成"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    if [ -z "$CLOUDFLARE_API_TOKEN" ]; then
        log_error "CLOUDFLARE_API_TOKEN 环境变量未设置"
        exit 1
    fi
    
    if [ -z "$CLOUDFLARE_ACCOUNT_ID" ]; then
        log_error "CLOUDFLARE_ACCOUNT_ID 环境变量未设置"
        exit 1
    fi
    
    log_success "环境变量检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    npm ci
    log_success "依赖安装完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # TypeScript类型检查
    npm run type-check
    
    # ESLint检查
    npm run lint
    
    # 单元测试（如果有）
    if npm run test --silent 2>/dev/null; then
        npm run test
    else
        log_warning "未找到测试脚本，跳过测试"
    fi
    
    log_success "测试完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."

    # 设置生产环境变量
    export NODE_ENV=production
    export NEXT_PUBLIC_SITE_URL=${SITE_URL:-"https://noisesleep.com"}
    export NEXT_PUBLIC_CDN_URL=${CDN_URL:-"https://cdn.noisesleep.com"}

    # 构建Next.js项目（静态导出）
    npm run build:cloudflare

    # 检查构建输出
    if [ ! -d "dist" ]; then
        log_error "构建失败：dist目录不存在"
        exit 1
    fi

    log_success "项目构建完成"
}

# 上传音频文件到R2
upload_audio_files() {
    log_info "上传音频文件到Cloudflare R2..."
    
    if [ -f "scripts/upload-audio.js" ]; then
        node scripts/upload-audio.js
        log_success "音频文件上传完成"
    else
        log_warning "音频上传脚本不存在，跳过音频文件上传"
    fi
}

# 部署到Cloudflare Pages
deploy_to_cloudflare() {
    local environment=${1:-"production"}
    
    log_info "部署到Cloudflare Pages (环境: $environment)..."
    
    # 认证Wrangler
    echo "$CLOUDFLARE_API_TOKEN" | wrangler auth login --api-token
    
    # 部署到指定环境
    if [ "$environment" = "production" ]; then
        wrangler pages deploy dist --project-name=noisesleep --compatibility-date=2024-01-01
    else
        wrangler pages deploy dist --project-name=noisesleep-staging --compatibility-date=2024-01-01
    fi
    
    log_success "部署完成"
}

# 验证部署
verify_deployment() {
    local url=${1:-"https://noisesleep.com"}
    
    log_info "验证部署..."
    
    # 等待部署生效
    sleep 30
    
    # 检查主页
    if curl -f -s "$url" > /dev/null; then
        log_success "主页访问正常"
    else
        log_error "主页访问失败"
        exit 1
    fi
    
    # 检查中文页面
    if curl -f -s "$url/zh" > /dev/null; then
        log_success "中文页面访问正常"
    else
        log_warning "中文页面访问可能有问题"
    fi
    
    # 检查音频API
    if curl -f -s "$url/api/health" > /dev/null; then
        log_success "API健康检查正常"
    else
        log_warning "API可能未正常工作"
    fi
    
    log_success "部署验证完成"
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    # 清理构建缓存
    rm -rf .next/cache
    
    # 清理优化后的音频文件
    if [ -d "optimized-audio" ]; then
        rm -rf optimized-audio
    fi
    
    log_success "清理完成"
}

# 发送部署通知
send_notification() {
    local status=$1
    local environment=${2:-"production"}
    
    if [ -n "$WEBHOOK_URL" ]; then
        log_info "发送部署通知..."
        
        local message
        if [ "$status" = "success" ]; then
            message="✅ NoiseSleep部署成功 (环境: $environment)"
        else
            message="❌ NoiseSleep部署失败 (环境: $environment)"
        fi
        
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\": \"$message\"}" \
            > /dev/null 2>&1 || log_warning "通知发送失败"
    fi
}

# 主函数
main() {
    local environment=${1:-"production"}
    local skip_tests=${2:-false}
    
    log_info "开始NoiseSleep项目部署流程..."
    log_info "目标环境: $environment"
    
    # 检查依赖和环境
    check_dependencies
    check_environment
    
    # 安装依赖
    install_dependencies
    
    # 运行测试（可选）
    if [ "$skip_tests" != "true" ]; then
        run_tests
    else
        log_warning "跳过测试阶段"
    fi
    
    # 构建项目
    build_project
    
    # 上传音频文件
    upload_audio_files
    
    # 部署到Cloudflare
    deploy_to_cloudflare "$environment"
    
    # 验证部署
    if [ "$environment" = "production" ]; then
        verify_deployment "https://noisesleep.com"
    else
        verify_deployment "https://staging.noisesleep.com"
    fi
    
    # 清理
    cleanup
    
    # 发送成功通知
    send_notification "success" "$environment"
    
    log_success "🎉 部署流程完成！"
    
    if [ "$environment" = "production" ]; then
        log_info "生产环境地址: https://noisesleep.com"
        log_info "中文版本: https://noisesleep.com/zh"
    else
        log_info "测试环境地址: https://staging.noisesleep.com"
    fi
}

# 错误处理
trap 'log_error "部署过程中发生错误"; send_notification "failure" "$1"; exit 1' ERR

# 脚本使用说明
usage() {
    echo "用法: $0 [environment] [skip_tests]"
    echo ""
    echo "参数:"
    echo "  environment  部署环境 (production|staging) [默认: production]"
    echo "  skip_tests   跳过测试 (true|false) [默认: false]"
    echo ""
    echo "示例:"
    echo "  $0                    # 部署到生产环境，运行测试"
    echo "  $0 staging            # 部署到测试环境"
    echo "  $0 production true    # 部署到生产环境，跳过测试"
    echo ""
    echo "环境变量:"
    echo "  CLOUDFLARE_API_TOKEN  Cloudflare API令牌 (必需)"
    echo "  CLOUDFLARE_ACCOUNT_ID Cloudflare账户ID (必需)"
    echo "  SITE_URL             网站URL (可选)"
    echo "  CDN_URL              CDN URL (可选)"
    echo "  WEBHOOK_URL          通知Webhook URL (可选)"
}

# 处理命令行参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    usage
    exit 0
fi

# 执行主函数
main "$@"
