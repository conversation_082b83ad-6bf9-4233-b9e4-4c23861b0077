const { chromium } = require('playwright');

// 睡眠模式深度测试脚本
async function runSleepModeDeepTest() {
  console.log('🌙 开始睡眠模式深度功能测试...\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // 减慢操作速度以便观察
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // 监听控制台消息
  const consoleMessages = [];
  page.on('console', msg => {
    const message = `🖥️  浏览器控制台: ${msg.text()}`;
    console.log(message);
    consoleMessages.push(message);
  });
  
  try {
    // 步骤1: 导航并激活音频播放
    console.log('📍 步骤 1: 导航到音频页面并激活播放');
    await page.goto('http://localhost:3000/zh/sounds');
    await page.waitForTimeout(3000);
    
    // 点击第一个音频开始播放
    const firstPlayButton = await page.locator('button[aria-label*="播放"], button[title*="播放"]').first();
    if (await firstPlayButton.count() > 0) {
      await firstPlayButton.click();
      await page.waitForTimeout(2000);
      console.log('✅ 音频播放已激活');
    } else {
      console.log('❌ 未找到播放按钮');
      return;
    }
    
    // 步骤2: 进入睡眠模式
    console.log('\n📍 步骤 2: 进入睡眠模式');
    const sleepModeResult = await enterSleepMode(page);
    console.log('✅ 睡眠模式切换结果:', sleepModeResult);
    
    if (!sleepModeResult.success) {
      console.log('❌ 无法进入睡眠模式，终止测试');
      return;
    }
    
    // 等待睡眠模式完全加载
    await page.waitForTimeout(3000);
    
    // 步骤3: 测试定时器功能
    console.log('\n📍 步骤 3: 测试睡眠模式下的定时器功能');
    const timerResult = await testTimerInSleepMode(page);
    console.log('✅ 定时器功能测试结果:', timerResult);
    
    // 步骤4: 测试混音功能
    console.log('\n📍 步骤 4: 测试睡眠模式下的混音功能');
    const mixingResult = await testMixingInSleepMode(page);
    console.log('✅ 混音功能测试结果:', mixingResult);
    
    // 步骤5: 测试UI交互功能
    console.log('\n📍 步骤 5: 测试睡眠模式UI交互功能');
    const uiResult = await testSleepModeUI(page);
    console.log('✅ UI交互功能测试结果:', uiResult);
    
    // 步骤6: 测试手势控制
    console.log('\n📍 步骤 6: 测试拖拽手势控制');
    const gestureResult = await testGestureControls(page);
    console.log('✅ 手势控制测试结果:', gestureResult);
    
    // 步骤7: 测试ESC退出功能
    console.log('\n📍 步骤 7: 测试ESC键退出睡眠模式');
    const escapeResult = await testEscapeExit(page);
    console.log('✅ ESC退出功能测试结果:', escapeResult);
    
    // 步骤8: 集成测试
    console.log('\n📍 步骤 8: 集成功能测试');
    const integrationResult = await testIntegration(page);
    console.log('✅ 集成测试结果:', integrationResult);
    
    console.log('\n🎉 睡眠模式深度测试完成！');
    console.log(`📝 收集到的控制台消息数量: ${consoleMessages.length}`);
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await browser.close();
  }
}

// 进入睡眠模式
async function enterSleepMode(page) {
  try {
    // 多种方式查找睡眠模式按钮
    const sleepButton = await page.locator('button').filter({ 
      hasText: /睡眠模式|Sleep Mode|进入睡眠|Enter Sleep/i 
    }).first();
    
    if (await sleepButton.count() === 0) {
      // 尝试查找图标按钮
      const iconButton = await page.locator('button[title*="睡眠"], button[aria-label*="睡眠"]').first();
      if (await iconButton.count() > 0) {
        await iconButton.click();
      } else {
        return { success: false, error: 'Sleep mode button not found' };
      }
    } else {
      await sleepButton.click();
    }
    
    await page.waitForTimeout(2000);
    
    // 验证是否成功进入睡眠模式
    const sleepModeActive = await page.evaluate(() => {
      // 检查多种睡眠模式标识
      const indicators = [
        document.querySelector('.sleep-mode-player'),
        document.querySelector('[data-testid="sleep-mode"]'),
        document.querySelector('.fixed.inset-0'),
        document.querySelector('.fullscreen'),
        document.body.classList.contains('sleep-mode')
      ];
      return indicators.some(indicator => indicator);
    });
    
    return { success: sleepModeActive, sleepModeActive };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 测试睡眠模式下的定时器功能
async function testTimerInSleepMode(page) {
  try {
    console.log('🔍 查找睡眠模式下的定时器按钮...');
    
    // 在睡眠模式下查找定时器按钮
    const timerButton = await page.locator('button').filter({ 
      hasText: /定时器|Timer|睡眠定时|Sleep Timer/i 
    }).first();
    
    const timerButtonFound = await timerButton.count() > 0;
    
    if (!timerButtonFound) {
      // 尝试查找图标按钮
      const iconTimer = await page.locator('button[title*="定时"], button[aria-label*="定时"], svg[data-icon*="clock"]').first();
      if (await iconTimer.count() > 0) {
        console.log('🔍 找到定时器图标按钮');
        await iconTimer.click();
      } else {
        return { success: false, timerButtonFound: false, error: 'Timer button not found in sleep mode' };
      }
    } else {
      console.log('🔍 找到定时器文本按钮');
      await timerButton.click();
    }
    
    await page.waitForTimeout(1500);
    
    // 检查定时器面板是否显示
    const timerPanelVisible = await page.evaluate(() => {
      const panel = document.querySelector('.timer-panel') ||
                   document.querySelector('[data-testid="timer-panel"]') ||
                   document.querySelector('.modal') ||
                   document.querySelector('.overlay');
      return panel !== null && panel.offsetParent !== null;
    });
    
    let timerSetResult = { success: false };
    
    if (timerPanelVisible) {
      console.log('🔍 定时器面板已显示，测试设置功能...');
      timerSetResult = await testTimerSettings(page);
    }
    
    return {
      success: timerButtonFound && timerPanelVisible,
      timerButtonFound,
      timerPanelVisible,
      timerSetResult
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 测试定时器设置功能
async function testTimerSettings(page) {
  try {
    // 查找预设时间按钮（30分钟、60分钟等）
    const presetButtons = await page.locator('button').filter({ 
      hasText: /30|60|90|分钟|min/i 
    });
    
    const presetCount = await presetButtons.count();
    console.log(`🔍 找到 ${presetCount} 个预设时间按钮`);
    
    if (presetCount > 0) {
      // 点击第一个预设按钮
      await presetButtons.first().click();
      await page.waitForTimeout(1000);
      
      // 检查定时器是否开始
      const timerActive = await page.evaluate(() => {
        const display = document.querySelector('.timer-display') ||
                       document.querySelector('[data-testid="timer-display"]');
        return display !== null && display.textContent.includes(':');
      });
      
      return { success: true, presetCount, timerActive };
    }
    
    return { success: false, presetCount: 0 };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 测试睡眠模式下的混音功能
async function testMixingInSleepMode(page) {
  try {
    console.log('🔍 查找睡眠模式下的混音按钮...');

    // 在睡眠模式下查找混音按钮
    const mixingButton = await page.locator('button').filter({
      hasText: /混音|Mixing|调音|Audio Mix/i
    }).first();

    const mixingButtonFound = await mixingButton.count() > 0;

    if (!mixingButtonFound) {
      // 尝试查找图标按钮
      const iconMixing = await page.locator('button[title*="混音"], button[aria-label*="混音"], svg[data-icon*="sliders"]').first();
      if (await iconMixing.count() > 0) {
        console.log('🔍 找到混音图标按钮');
        await iconMixing.click();
      } else {
        return { success: false, mixingButtonFound: false, error: 'Mixing button not found in sleep mode' };
      }
    } else {
      console.log('🔍 找到混音文本按钮');
      await mixingButton.click();
    }

    await page.waitForTimeout(1500);

    // 检查混音面板是否显示
    const mixingPanelVisible = await page.evaluate(() => {
      const panel = document.querySelector('.mixing-panel') ||
                   document.querySelector('[data-testid="mixing-panel"]') ||
                   document.querySelector('.audio-mixer');
      return panel !== null && panel.offsetParent !== null;
    });

    let mixingControlResult = { success: false };

    if (mixingPanelVisible) {
      console.log('🔍 混音面板已显示，测试控制功能...');
      mixingControlResult = await testMixingControls(page);
    }

    return {
      success: mixingButtonFound && mixingPanelVisible,
      mixingButtonFound,
      mixingPanelVisible,
      mixingControlResult
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 测试混音控制功能
async function testMixingControls(page) {
  try {
    // 查找音量滑块
    const volumeSliders = await page.locator('input[type="range"], .slider').count();
    console.log(`🔍 找到 ${volumeSliders} 个音量控制滑块`);

    // 查找音频通道
    const audioChannels = await page.evaluate(() => {
      const channels = document.querySelectorAll('.audio-channel, .mixing-channel, [data-testid*="channel"]');
      return channels.length;
    });
    console.log(`🔍 找到 ${audioChannels} 个音频通道`);

    // 测试添加音频到混音
    const addButtons = await page.locator('button').filter({
      hasText: /添加|Add|加入|Join/i
    }).count();

    return {
      success: volumeSliders > 0 || audioChannels > 0,
      volumeSliders,
      audioChannels,
      addButtons
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 测试睡眠模式UI交互功能
async function testSleepModeUI(page) {
  try {
    console.log('🔍 检查睡眠模式UI元素...');

    // 检查全屏容器
    const fullscreenContainer = await page.evaluate(() => {
      const container = document.querySelector('.fixed.inset-0') ||
                       document.querySelector('.fullscreen') ||
                       document.querySelector('.sleep-mode-player');
      return container !== null;
    });

    // 检查背景渐变
    const backgroundGradient = await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      for (let el of elements) {
        const style = window.getComputedStyle(el);
        if (style.background.includes('gradient') || style.backgroundImage.includes('gradient')) {
          return true;
        }
      }
      return false;
    });

    // 检查中心控制区域
    const centerControls = await page.evaluate(() => {
      const center = document.querySelector('.center, .flex.items-center.justify-center') ||
                    document.querySelector('[class*="center"]');
      return center !== null;
    });

    // 检查拉绳控制器
    const pullString = await page.evaluate(() => {
      const pullString = document.querySelector('.pull-string') ||
                         document.querySelector('[data-testid="pull-string"]') ||
                         document.querySelector('.drag-control');
      return pullString !== null;
    });

    // 检查环境信息显示
    const ambientInfo = await page.evaluate(() => {
      const ambient = document.querySelector('.ambient-info') ||
                     document.querySelector('[data-testid="ambient-info"]');
      return ambient !== null;
    });

    // 检查音频信息显示
    const audioInfo = await page.evaluate(() => {
      const audio = document.querySelector('.audio-info') ||
                   document.querySelector('[data-testid="audio-info"]');
      return audio !== null;
    });

    return {
      success: fullscreenContainer || centerControls || pullString,
      fullscreenContainer,
      backgroundGradient,
      centerControls,
      pullString,
      ambientInfo,
      audioInfo
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 测试手势控制功能
async function testGestureControls(page) {
  try {
    console.log('🔍 测试拖拽手势控制...');

    // 查找可拖拽元素
    const draggableElements = await page.evaluate(() => {
      const elements = document.querySelectorAll('[draggable="true"], .draggable, .pull-string, [data-drag="true"]');
      return elements.length;
    });

    console.log(`🔍 找到 ${draggableElements} 个可拖拽元素`);

    // 尝试模拟拖拽操作
    let dragTestResult = { success: false };

    if (draggableElements > 0) {
      dragTestResult = await simulateDragGesture(page);
    }

    // 检查触摸事件支持
    const touchSupport = await page.evaluate(() => {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    });

    return {
      success: draggableElements > 0,
      draggableElements,
      touchSupport,
      dragTestResult
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 模拟拖拽手势
async function simulateDragGesture(page) {
  try {
    // 查找拉绳控制器或其他可拖拽元素
    const dragElement = await page.locator('.pull-string, .draggable, [data-drag="true"]').first();

    if (await dragElement.count() > 0) {
      const box = await dragElement.boundingBox();
      if (box) {
        // 模拟向下拖拽
        await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
        await page.mouse.down();
        await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2 + 50);
        await page.waitForTimeout(500);
        await page.mouse.up();

        console.log('🔍 执行了拖拽操作');
        return { success: true, dragExecuted: true };
      }
    }

    return { success: false, dragExecuted: false };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 测试ESC键退出功能
async function testEscapeExit(page) {
  try {
    console.log('🔍 测试ESC键退出睡眠模式...');

    // 按ESC键
    await page.keyboard.press('Escape');
    await page.waitForTimeout(2000);

    // 检查是否退出睡眠模式
    const sleepModeExited = await page.evaluate(() => {
      // 检查睡眠模式标识是否消失
      const sleepIndicators = [
        document.querySelector('.sleep-mode-player'),
        document.querySelector('[data-testid="sleep-mode"]'),
        document.querySelector('.fixed.inset-0'),
        document.body.classList.contains('sleep-mode')
      ];
      return !sleepIndicators.some(indicator => indicator);
    });

    // 检查是否回到标准播放器
    const standardPlayerVisible = await page.evaluate(() => {
      const standardPlayer = document.querySelector('.fixed.bottom-0') ||
                            document.querySelector('.standard-player');
      return standardPlayer !== null;
    });

    return {
      success: sleepModeExited || standardPlayerVisible,
      sleepModeExited,
      standardPlayerVisible
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 集成测试
async function testIntegration(page) {
  try {
    console.log('🔍 执行集成功能测试...');

    // 重新进入睡眠模式进行集成测试
    const reenterResult = await enterSleepMode(page);

    if (!reenterResult.success) {
      return { success: false, error: 'Cannot re-enter sleep mode for integration test' };
    }

    await page.waitForTimeout(2000);

    // 检查状态同步
    const stateSync = await page.evaluate(() => {
      // 检查音频播放状态是否同步
      const audioElements = document.querySelectorAll('audio');
      const playingStates = Array.from(audioElements).map(audio => !audio.paused);

      // 检查UI状态是否一致
      const playButtons = document.querySelectorAll('button[aria-label*="播放"], button[title*="播放"]');
      const pauseButtons = document.querySelectorAll('button[aria-label*="暂停"], button[title*="暂停"]');

      return {
        audioElementsCount: audioElements.length,
        playingCount: playingStates.filter(Boolean).length,
        playButtonsCount: playButtons.length,
        pauseButtonsCount: pauseButtons.length
      };
    });

    // 检查翻译文本显示
    const translationCheck = await page.evaluate(() => {
      const textElements = document.querySelectorAll('*');
      let hasChineseText = false;
      let hasTranslationErrors = false;

      for (let el of textElements) {
        const text = el.textContent || '';
        if (/[\u4e00-\u9fff]/.test(text)) {
          hasChineseText = true;
        }
        if (text.includes('MISSING_MESSAGE') || text.includes('IntlError')) {
          hasTranslationErrors = true;
        }
      }

      return { hasChineseText, hasTranslationErrors };
    });

    return {
      success: reenterResult.success && !translationCheck.hasTranslationErrors,
      reenterResult,
      stateSync,
      translationCheck
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 运行测试
if (require.main === module) {
  runSleepModeDeepTest().catch(console.error);
}
