'use client';

import { useTranslations, useLocale } from 'next-intl';
import { useCallback } from 'react';

/**
 * 安全翻译Hook
 * 提供带有回退机制的翻译功能
 */
export function useSafeTranslation(namespace?: string) {
  const t = useTranslations(namespace);
  const locale = useLocale();

  /**
   * 安全获取翻译文本
   * @param key - 翻译键
   * @param fallback - 回退文本
   * @param params - 翻译参数
   * @returns 翻译文本或回退文本
   */
  const safeT = useCallback((
    key: string,
    fallback?: string,
    params?: Record<string, any>
  ): string => {
    try {
      const result = t(key, params);
      
      // 检查是否返回了缺失消息标识
      if (result.includes('MISSING_MESSAGE') || result === key) {
        console.warn(`Translation missing for key: ${namespace ? `${namespace}.${key}` : key}`);
        return fallback || key;
      }
      
      return result;
    } catch (error) {
      console.error(`Translation error for key: ${namespace ? `${namespace}.${key}` : key}`, error);
      return fallback || key;
    }
  }, [t, namespace]);

  /**
   * 检查翻译键是否存在
   * @param key - 翻译键
   * @returns 是否存在
   */
  const hasKey = useCallback((key: string): boolean => {
    try {
      const result = t(key);
      return !result.includes('MISSING_MESSAGE') && result !== key;
    } catch {
      return false;
    }
  }, [t]);

  /**
   * 获取本地化的错误消息
   * @param errorType - 错误类型
   * @param fallback - 回退文本
   * @returns 本地化的错误消息
   */
  const getErrorMessage = useCallback((
    errorType: string,
    fallback?: string
  ): string => {
    const errorKey = `errors.${errorType}`;
    return safeT(errorKey, fallback);
  }, [safeT]);

  /**
   * 获取复数形式的翻译
   * @param key - 翻译键
   * @param count - 数量
   * @param fallback - 回退文本
   * @returns 复数形式的翻译
   */
  const plural = useCallback((
    key: string,
    count: number,
    fallback?: string
  ): string => {
    try {
      const result = t(key, { count });
      
      if (result.includes('MISSING_MESSAGE') || result === key) {
        console.warn(`Plural translation missing for key: ${namespace ? `${namespace}.${key}` : key}`);
        return fallback || `${key} (${count})`;
      }
      
      return result;
    } catch (error) {
      console.error(`Plural translation error for key: ${namespace ? `${namespace}.${key}` : key}`, error);
      return fallback || `${key} (${count})`;
    }
  }, [t, namespace]);

  /**
   * 获取富文本翻译（支持HTML标签）
   * @param key - 翻译键
   * @param fallback - 回退文本
   * @param params - 翻译参数
   * @returns 富文本翻译
   */
  const rich = useCallback((
    key: string,
    fallback?: string,
    params?: Record<string, any>
  ): string => {
    try {
      const result = t.rich(key, params);
      
      if (typeof result === 'string' && (result.includes('MISSING_MESSAGE') || result === key)) {
        console.warn(`Rich translation missing for key: ${namespace ? `${namespace}.${key}` : key}`);
        return fallback || key;
      }
      
      return result as string;
    } catch (error) {
      console.error(`Rich translation error for key: ${namespace ? `${namespace}.${key}` : key}`, error);
      return fallback || key;
    }
  }, [t, namespace]);

  return {
    t: safeT,
    hasKey,
    getErrorMessage,
    plural,
    rich,
    locale,
    namespace
  };
}

/**
 * 错误翻译Hook
 * 专门用于错误消息的翻译
 */
export function useErrorTranslation() {
  const { t, getErrorMessage, locale } = useSafeTranslation('errors');

  /**
   * 获取音频错误消息
   * @param error - 错误对象
   * @param audioId - 音频ID
   * @returns 本地化的错误消息
   */
  const getAudioErrorMessage = useCallback((error: Error, audioId?: string): string => {
    // 根据错误类型返回相应消息
    if (error.message.includes('network') || error.message.includes('fetch')) {
      return getErrorMessage('networkError', 'Network connection failed');
    }
    
    if (error.message.includes('404') || error.message.includes('not found')) {
      return getErrorMessage('audioLoadFailed', 'Audio file not found');
    }
    
    if (error.message.includes('browser') || error.message.includes('support')) {
      return getErrorMessage('browserNotSupported', 'Browser not supported');
    }
    
    if (error.message.includes('AudioContext') || error.message.includes('audio context')) {
      return getErrorMessage('audioContextFailed', 'Audio system failed');
    }
    
    return getErrorMessage('unknownError', 'An unexpected error occurred');
  }, [getErrorMessage]);

  /**
   * 获取网络状态文本
   * @param isOnline - 是否在线
   * @returns 网络状态文本
   */
  const getNetworkStatusText = useCallback((isOnline: boolean): string => {
    const statusKey = isOnline ? 'online' : 'offline';
    return getErrorMessage(statusKey, isOnline ? 'Online' : 'Offline');
  }, [getErrorMessage]);

  /**
   * 获取重试次数文本
   * @param current - 当前重试次数
   * @param max - 最大重试次数
   * @returns 重试次数文本
   */
  const getRetryText = useCallback((current: number, max: number = 3): string => {
    return t('retryAttempts', `Retry attempts: ${current}/${max}`, { count: current, max });
  }, [t]);

  return {
    t,
    getErrorMessage,
    getAudioErrorMessage,
    getNetworkStatusText,
    getRetryText,
    locale
  };
}

/**
 * 页面翻译Hook
 * 用于页面级别的翻译
 */
export function usePageTranslation(page: string) {
  const { t, hasKey, locale } = useSafeTranslation(page);

  /**
   * 获取页面标题
   * @param fallback - 回退标题
   * @returns 页面标题
   */
  const getTitle = useCallback((fallback?: string): string => {
    return t('title', fallback || 'NoiseSleep');
  }, [t]);

  /**
   * 获取页面描述
   * @param fallback - 回退描述
   * @returns 页面描述
   */
  const getDescription = useCallback((fallback?: string): string => {
    return t('description', fallback || 'Sleep better with nature sounds');
  }, [t]);

  /**
   * 获取页面关键词
   * @param fallback - 回退关键词
   * @returns 页面关键词
   */
  const getKeywords = useCallback((fallback?: string): string => {
    return t('keywords', fallback || 'sleep, sounds, nature, relaxation');
  }, [t]);

  return {
    t,
    hasKey,
    getTitle,
    getDescription,
    getKeywords,
    locale,
    page
  };
}

export default useSafeTranslation;
