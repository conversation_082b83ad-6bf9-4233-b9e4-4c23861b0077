'use client';

import { useEffect, useRef, useCallback } from 'react';
import { useAudioStore } from '@/store/audioStore';
import { TimerPreset } from '@/types/audio';

export function useSleepTimer() {
  const {
    timer,
    playState,
    setTimerActive,
    setTimerDuration,
    setTimerRemainingTime,
    updatePlayState,
  } = useAudioStore();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const fadeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const originalVolumeRef = useRef<number>(0.7);

  // 启动定时器
  const startTimer = useCallback((durationMinutes: number) => {
    console.log('🕐 启动睡眠定时器:', durationMinutes, '分钟');
    
    // 保存当前音量
    originalVolumeRef.current = playState.volume;
    
    // 设置定时器状态
    const durationSeconds = durationMinutes * 60;
    setTimerDuration(durationMinutes);
    setTimerRemainingTime(durationSeconds);
    setTimerActive(true);

    // 清除现有定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // 启动倒计时
    intervalRef.current = setInterval(() => {
      useAudioStore.getState().setTimerRemainingTime(
        Math.max(0, useAudioStore.getState().timer.remainingTime - 1)
      );
    }, 1000);
  }, [playState.volume, setTimerDuration, setTimerRemainingTime, setTimerActive]);

  // 停止定时器
  const stopTimer = useCallback(() => {
    console.log('⏹️ 停止睡眠定时器');
    
    setTimerActive(false);
    setTimerRemainingTime(0);
    
    // 清除所有定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (fadeIntervalRef.current) {
      clearInterval(fadeIntervalRef.current);
      fadeIntervalRef.current = null;
    }
  }, [setTimerActive, setTimerRemainingTime]);

  // 暂停/恢复定时器
  const pauseTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const resumeTimer = useCallback(() => {
    if (timer.isActive && timer.remainingTime > 0 && !intervalRef.current) {
      intervalRef.current = setInterval(() => {
        useAudioStore.getState().setTimerRemainingTime(
          Math.max(0, useAudioStore.getState().timer.remainingTime - 1)
        );
      }, 1000);
    }
  }, [timer.isActive, timer.remainingTime]);

  // 快速设置预设时长
  const setPresetTimer = useCallback((preset: TimerPreset) => {
    startTimer(preset);
  }, [startTimer]);

  // 设置自定义时长
  const setCustomTimer = useCallback((minutes: number) => {
    if (minutes > 0 && minutes <= 480) { // 最大8小时
      startTimer(minutes);
    }
  }, [startTimer]);

  // 开始淡出效果
  const startFadeOut = useCallback(() => {
    console.log('🔉 开始淡出效果');
    
    const fadeSteps = timer.fadeOutDuration; // 淡出步数（秒）
    const volumeStep = originalVolumeRef.current / fadeSteps;
    let currentStep = 0;

    fadeIntervalRef.current = setInterval(() => {
      currentStep++;
      const newVolume = Math.max(0, originalVolumeRef.current - (volumeStep * currentStep));
      
      updatePlayState({ volume: newVolume });
      
      if (currentStep >= fadeSteps || newVolume <= 0) {
        // 淡出完成，停止播放
        if (fadeIntervalRef.current) {
          clearInterval(fadeIntervalRef.current);
          fadeIntervalRef.current = null;
        }
        
        if (timer.autoStop) {
          updatePlayState({ 
            isPlaying: false, 
            isPaused: false,
            volume: originalVolumeRef.current // 恢复原始音量
          });
        }
        
        stopTimer();
      }
    }, 1000);
  }, [timer.fadeOutDuration, timer.autoStop, updatePlayState, stopTimer]);

  // 格式化剩余时间显示
  const formatRemainingTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  }, []);

  // 监听定时器到期
  useEffect(() => {
    if (timer.isActive && timer.remainingTime <= 0) {
      console.log('⏰ 定时器到期，开始淡出');
      startFadeOut();
    }
  }, [timer.isActive, timer.remainingTime, startFadeOut]);

  // 监听播放状态变化，同步定时器
  useEffect(() => {
    if (timer.isActive) {
      if (playState.isPlaying && !intervalRef.current) {
        resumeTimer();
      } else if (!playState.isPlaying && intervalRef.current) {
        pauseTimer();
      }
    }
  }, [playState.isPlaying, timer.isActive, resumeTimer, pauseTimer]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (fadeIntervalRef.current) {
        clearInterval(fadeIntervalRef.current);
      }
    };
  }, []);

  return {
    // 状态
    isActive: timer.isActive,
    duration: timer.duration,
    remainingTime: timer.remainingTime,
    fadeOutDuration: timer.fadeOutDuration,
    autoStop: timer.autoStop,
    
    // 方法
    startTimer,
    stopTimer,
    pauseTimer,
    resumeTimer,
    setPresetTimer,
    setCustomTimer,
    formatRemainingTime,
    
    // 计算属性
    progress: timer.duration > 0 ? (timer.duration * 60 - timer.remainingTime) / (timer.duration * 60) : 0,
    isNearEnd: timer.remainingTime <= timer.fadeOutDuration,
  };
}
