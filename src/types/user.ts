import { SoundCategory } from './audio';

// 用户年龄组
export type AgeGroup = 'child' | 'teen' | 'adult' | 'elderly';

// 睡眠问题类型
export type SleepIssue = 
  | 'insomnia'
  | 'light_sleeper'
  | 'stress'
  | 'anxiety'
  | 'snoring_partner'
  | 'environmental_noise'
  | 'shift_work'
  | 'jet_lag';

// 用户偏好设置
export interface UserPreferences {
  // 语言设置
  language: 'en' | 'zh';
  
  // 主题设置
  theme: 'light' | 'dark' | 'night' | 'auto';
  brightness: number; // 0-100
  
  // 音频偏好
  defaultVolume: number; // 0-100
  autoplay: boolean;
  loopByDefault: boolean;
  fadeInDuration: number; // 秒
  fadeOutDuration: number; // 秒
  
  // 定时器偏好
  defaultTimerDuration: number; // 分钟
  autoStopEnabled: boolean;
  
  // 通知设置
  enableNotifications: boolean;
  sleepReminders: boolean;
  reminderTime: string; // HH:MM格式
  
  // 隐私设置
  analyticsEnabled: boolean;
  crashReportingEnabled: boolean;
  
  // 可访问性设置
  reducedMotion: boolean;
  highContrast: boolean;
  largeText: boolean;
  screenReaderOptimized: boolean;
}

// 用户档案
export interface UserProfile {
  id: string;
  
  // 基本信息
  ageGroup: AgeGroup;
  location?: {
    country: string;
    region: string;
    timezone: string;
  };
  
  // 睡眠相关信息
  sleepIssues: SleepIssue[];
  hasInsomnia: boolean;
  averageSleepDuration: number; // 小时
  bedtime: string; // HH:MM格式
  wakeTime: string; // HH:MM格式
  
  // 音频偏好
  favoriteCategories: SoundCategory[];
  dislikedCategories: SoundCategory[];
  preferredNoiseTypes: string[];
  volumePreference: number; // 0-100
  
  // 使用统计
  totalListeningTime: number; // 分钟
  sessionsCount: number;
  favoriteTime: 'morning' | 'afternoon' | 'evening' | 'night';
  averageSessionDuration: number; // 分钟
  
  // 偏好设置
  preferences: UserPreferences;
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
  lastActiveAt: string;
}

// 用户行为数据
export interface UserBehaviorData {
  // 播放历史
  playHistory: {
    soundId: string;
    playedAt: string;
    duration: number; // 播放时长（秒）
    completionRate: number; // 完成率 (0-1)
    volume: number;
    wasLooping: boolean;
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
    deviceType: 'mobile' | 'tablet' | 'desktop';
  }[];
  
  // 搜索历史
  searchHistory: {
    query: string;
    searchedAt: string;
    resultsCount: number;
    clickedResults: string[]; // soundId数组
  }[];
  
  // 收藏记录
  favorites: {
    soundId: string;
    favoritedAt: string;
    category: SoundCategory;
  }[];
  
  // 混音使用记录
  mixingHistory: {
    sounds: string[]; // soundId数组
    createdAt: string;
    duration: number; // 使用时长（秒）
    saved: boolean; // 是否保存为预设
    name?: string; // 预设名称
  }[];
  
  // 定时器使用记录
  timerUsage: {
    duration: number; // 设置的时长（分钟）
    actualDuration: number; // 实际使用时长（分钟）
    usedAt: string;
    completed: boolean; // 是否完整使用
  }[];
  
  // 设置变更记录
  settingsChanges: {
    setting: keyof UserPreferences;
    oldValue: any;
    newValue: any;
    changedAt: string;
  }[];
}

// 用户统计数据
export interface UserStats {
  // 总体统计
  totalSessions: number;
  totalListeningTime: number; // 分钟
  averageSessionDuration: number; // 分钟
  longestSession: number; // 分钟
  
  // 最近活动
  last7Days: {
    sessions: number;
    listeningTime: number;
    averageDuration: number;
  };
  
  last30Days: {
    sessions: number;
    listeningTime: number;
    averageDuration: number;
  };
  
  // 偏好分析
  mostPlayedCategory: SoundCategory;
  mostPlayedSound: string; // soundId
  preferredListeningTime: 'morning' | 'afternoon' | 'evening' | 'night';
  averageVolume: number;
  
  // 功能使用
  mixingUsageRate: number; // 混音功能使用率 (0-1)
  timerUsageRate: number; // 定时器使用率 (0-1)
  favoritesCount: number;
  
  // 睡眠质量指标（自报告）
  sleepQualityRatings: {
    date: string;
    rating: number; // 1-10
    soundsUsed: string[];
    notes?: string;
  }[];
  
  averageSleepQuality: number; // 1-10
}

// 用户反馈
export interface UserFeedback {
  id: string;
  userId: string;
  
  // 反馈类型
  type: 'bug_report' | 'feature_request' | 'general_feedback' | 'audio_quality' | 'translation_issue';
  
  // 反馈内容
  title: string;
  description: string;
  rating?: number; // 1-5星评分
  
  // 相关信息
  relatedSoundId?: string;
  relatedFeature?: string;
  userAgent: string;
  deviceInfo: {
    platform: string;
    browser: string;
    version: string;
    screenSize: string;
  };
  
  // 状态
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
}

// 用户会话信息
export interface UserSession {
  id: string;
  userId?: string; // 匿名用户可能没有userId
  
  // 会话信息
  startTime: string;
  endTime?: string;
  duration?: number; // 秒
  
  // 设备信息
  deviceType: 'mobile' | 'tablet' | 'desktop';
  browser: string;
  os: string;
  screenSize: string;
  language: string;
  
  // 位置信息（如果用户同意）
  country?: string;
  region?: string;
  timezone: string;
  
  // 会话活动
  pagesVisited: string[];
  soundsPlayed: string[];
  featuresUsed: string[];
  
  // 性能指标
  pageLoadTime: number; // 毫秒
  audioLoadTimes: { [soundId: string]: number }; // 毫秒
  
  // 错误记录
  errors: {
    type: string;
    message: string;
    timestamp: string;
    stack?: string;
  }[];
}

// 导出所有用户相关类型
export type UserTypes = 
  | UserProfile
  | UserPreferences
  | UserBehaviorData
  | UserStats
  | UserFeedback
  | UserSession;
