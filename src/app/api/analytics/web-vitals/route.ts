import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      name,
      value,
      id,
      delta,
      rating,
      navigationType,
      timestamp,
      url,
      userAgent
    } = body;

    // 验证必需字段
    if (!name || value === undefined || !id) {
      return NextResponse.json(
        { error: 'Missing required fields: name, value, id' },
        { status: 400 }
      );
    }

    // 验证指标名称
    const validMetrics = ['CLS', 'FID', 'FCP', 'LCP', 'TTFB'];
    if (!validMetrics.includes(name)) {
      return NextResponse.json(
        { error: 'Invalid metric name' },
        { status: 400 }
      );
    }

    // 在生产环境中，这里可以将数据发送到分析服务
    // 例如：Google Analytics、DataDog、New Relic等
    
    if (process.env.NODE_ENV === 'production') {
      // 发送到外部分析服务
      await sendToAnalyticsService({
        name,
        value,
        id,
        delta,
        rating,
        navigationType,
        timestamp,
        url,
        userAgent,
        environment: 'production'
      });
    }

    // 记录到控制台（开发环境）
    console.log('Web Vital收集:', {
      metric: name,
      value: value,
      rating: rating,
      url: url,
      timestamp: new Date(timestamp).toISOString()
    });

    // 检查性能预算
    const budgetExceeded = checkPerformanceBudget(name, value);
    if (budgetExceeded) {
      console.warn(`性能预算超标: ${name} = ${value} (阈值: ${budgetExceeded.threshold})`);
      
      // 可以发送警报到监控系统
      if (process.env.NODE_ENV === 'production') {
        await sendPerformanceAlert({
          metric: name,
          value: value,
          threshold: budgetExceeded.threshold,
          url: url,
          timestamp: timestamp
        });
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Web vital recorded successfully',
      budgetExceeded: budgetExceeded ? true : false
    });

  } catch (error) {
    console.error('Web Vitals API错误:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 性能预算检查
function checkPerformanceBudget(metric: string, value: number) {
  const budgets = {
    'LCP': 2500,  // 2.5秒
    'FID': 100,   // 100毫秒
    'CLS': 0.1,   // 0.1
    'FCP': 1800,  // 1.8秒
    'TTFB': 600   // 600毫秒
  };

  const threshold = budgets[metric as keyof typeof budgets];
  if (threshold && value > threshold) {
    return { threshold, exceeded: true };
  }
  
  return null;
}

// 发送到外部分析服务
async function sendToAnalyticsService(data: any) {
  // 这里可以集成各种分析服务
  
  // 示例：发送到自定义分析端点
  if (process.env.ANALYTICS_ENDPOINT) {
    try {
      await fetch(process.env.ANALYTICS_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ANALYTICS_API_KEY}`
        },
        body: JSON.stringify(data)
      });
    } catch (error) {
      console.error('发送到分析服务失败:', error);
    }
  }

  // 示例：发送到Google Analytics Measurement Protocol
  if (process.env.GA_MEASUREMENT_ID) {
    try {
      const params = new URLSearchParams({
        v: '1',
        tid: process.env.GA_MEASUREMENT_ID,
        cid: data.id,
        t: 'timing',
        utc: 'Web Vitals',
        utv: data.name,
        utt: Math.round(data.value).toString()
      });

      await fetch('https://www.google-analytics.com/collect', {
        method: 'POST',
        body: params
      });
    } catch (error) {
      console.error('发送到Google Analytics失败:', error);
    }
  }
}

// 发送性能警报
async function sendPerformanceAlert(data: any) {
  // 这里可以集成警报系统，如Slack、Discord、邮件等
  
  if (process.env.SLACK_WEBHOOK_URL) {
    try {
      await fetch(process.env.SLACK_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: `🚨 性能预算超标警报`,
          attachments: [{
            color: 'danger',
            fields: [
              { title: '指标', value: data.metric, short: true },
              { title: '实际值', value: data.value.toString(), short: true },
              { title: '阈值', value: data.threshold.toString(), short: true },
              { title: 'URL', value: data.url, short: false },
              { title: '时间', value: new Date(data.timestamp).toISOString(), short: true }
            ]
          }]
        })
      });
    } catch (error) {
      console.error('发送Slack警报失败:', error);
    }
  }
}
