import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/test-*',
          '/debug/',
          '/_next/',
          '/private/',
          '*.json',
          '/audio-test',
          '/test-audio',
          '/test-player',
          '/test-landing'
        ],
      },
      // 特殊规则：允许搜索引擎访问音频文件
      {
        userAgent: '*',
        allow: '/sounds/',
      },
      // 允许访问静态资源
      {
        userAgent: '*',
        allow: ['/favicon.ico', '/manifest.json', '/icon*.png'],
      }
    ],
    sitemap: 'https://noisesleep.com/sitemap.xml',
    host: 'https://noisesleep.com'
  };
}
