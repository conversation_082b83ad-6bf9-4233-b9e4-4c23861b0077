'use client';

import { getErrorPageContent } from '@/utils/i18nHelpers';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

/**
 * 本地化404页面
 * 根据用户的语言偏好显示相应的错误信息
 */
export default function LocalizedNotFound() {
  // 从URL路径中提取locale
  const pathname = usePathname();
  const locale = pathname.startsWith('/zh') ? 'zh' : 'en';
  const content = getErrorPageContent(locale, '404');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full mx-auto text-center px-4">
        {/* 404图标 */}
        <div className="w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600">
          <svg fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
          </svg>
        </div>

        {/* 404标题 */}
        <h1 className="text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          404
        </h1>

        {/* 错误消息 */}
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          {content.title}
        </h2>

        <p className="text-gray-600 dark:text-gray-400 mb-8">
          {content.message}
        </p>

        {/* 操作按钮 */}
        <div className="space-y-4">
          <Link
            href={locale === 'zh' ? '/zh' : '/'}
            className="inline-block w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors"
          >
            {content.action}
          </Link>

          <div className="flex space-x-4">
            <Link
              href={locale === 'zh' ? '/zh/sounds' : '/sounds'}
              className="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              {locale === 'zh' ? '浏览音频' : 'Browse Sounds'}
            </Link>
            
            <Link
              href={locale === 'zh' ? '/zh/mixing' : '/mixing'}
              className="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              {locale === 'zh' ? '混音器' : 'Mixer'}
            </Link>
          </div>
        </div>

        {/* 搜索建议 */}
        <div className="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>{locale === 'zh' ? '您可能在寻找：' : 'You might be looking for:'}</p>
          <div className="mt-2 space-x-4">
            <Link 
              href={locale === 'zh' ? '/zh/sounds/rain' : '/sounds/rain'} 
              className="text-indigo-500 hover:text-indigo-600"
            >
              {locale === 'zh' ? '雨声' : 'Rain Sounds'}
            </Link>
            <Link 
              href={locale === 'zh' ? '/zh/sounds/nature' : '/sounds/nature'} 
              className="text-indigo-500 hover:text-indigo-600"
            >
              {locale === 'zh' ? '自然音' : 'Nature Sounds'}
            </Link>
            <Link 
              href={locale === 'zh' ? '/zh/sounds/noise' : '/sounds/noise'} 
              className="text-indigo-500 hover:text-indigo-600"
            >
              {locale === 'zh' ? '白噪音' : 'White Noise'}
            </Link>
          </div>
        </div>

        {/* 语言切换 */}
        <div className="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
            {locale === 'zh' ? '切换语言：' : 'Switch language:'}
          </p>
          <div className="flex justify-center space-x-4">
            {locale !== 'en' && (
              <Link
                href="/404"
                className="text-indigo-500 hover:text-indigo-600 text-sm"
              >
                🇺🇸 English
              </Link>
            )}
            {locale !== 'zh' && (
              <Link
                href="/zh/404"
                className="text-indigo-500 hover:text-indigo-600 text-sm"
              >
                🇨🇳 中文
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
