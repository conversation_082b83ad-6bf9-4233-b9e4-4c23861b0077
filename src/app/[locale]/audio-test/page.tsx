'use client';

import { useState } from 'react';
import { audioData } from '@/data/audioData_real';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';

export default function AudioTestPage() {
  const [selectedAudio, setSelectedAudio] = useState(audioData[0]);
  const { 
    isLoading, 
    isPlaying, 
    currentTime, 
    duration, 
    volume,
    error,
    play, 
    pause, 
    stop, 
    setVolume 
  } = useAudioPlayer();

  const handlePlay = () => {
    if (selectedAudio) {
      play(selectedAudio);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">音频播放测试</h1>
        
        {/* 音频选择 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">选择音频文件</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {audioData.map((audio) => (
              <button
                key={audio.id}
                onClick={() => setSelectedAudio(audio)}
                className={`p-4 rounded-lg border-2 text-left transition-colors ${
                  selectedAudio?.id === audio.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium">{audio.title.zh}</div>
                <div className="text-sm text-gray-600">{audio.category}</div>
                <div className="text-xs text-gray-500">{audio.filename}</div>
              </button>
            ))}
          </div>
        </div>

        {/* 播放器控制 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">播放器控制</h2>
          
          {selectedAudio && (
            <div className="mb-4">
              <h3 className="font-medium">当前选择: {selectedAudio.title.zh}</h3>
              <p className="text-sm text-gray-600">
                文件路径: /Sounds/{selectedAudio.category.charAt(0).toUpperCase() + selectedAudio.category.slice(1)}/{selectedAudio.filename}
              </p>
            </div>
          )}

          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={handlePlay}
              disabled={isLoading || !selectedAudio}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300"
            >
              {isLoading ? '加载中...' : '播放'}
            </button>
            
            <button
              onClick={pause}
              disabled={!isPlaying}
              className="px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 disabled:bg-gray-300"
            >
              暂停
            </button>
            
            <button
              onClick={stop}
              className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
            >
              停止
            </button>
          </div>

          {/* 音量控制 */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              音量: {Math.round(volume * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          {/* 进度显示 */}
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all"
                style={{
                  width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%'
                }}
              />
            </div>
          </div>
        </div>

        {/* 状态信息 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">状态信息</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">加载状态:</span>
              <span className={`ml-2 px-2 py-1 rounded ${
                isLoading ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
              }`}>
                {isLoading ? '加载中' : '已就绪'}
              </span>
            </div>
            <div>
              <span className="font-medium">播放状态:</span>
              <span className={`ml-2 px-2 py-1 rounded ${
                isPlaying ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {isPlaying ? '播放中' : '已停止'}
              </span>
            </div>
            <div>
              <span className="font-medium">当前时间:</span>
              <span className="ml-2">{formatTime(currentTime)}</span>
            </div>
            <div>
              <span className="font-medium">总时长:</span>
              <span className="ml-2">{formatTime(duration)}</span>
            </div>
          </div>
          
          {error && (
            <div className="mt-4 p-4 bg-red-100 border border-red-300 rounded-lg">
              <h3 className="font-medium text-red-800 mb-2">错误信息:</h3>
              <p className="text-red-700">{error}</p>
            </div>
          )}
        </div>

        {/* 文件系统验证 */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4">文件系统验证</h2>
          <div className="text-sm">
            <p className="mb-2">实际音频文件列表 (/Sounds/):</p>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li>Rain/heavy-rain.mp3, light-rain.mp3, rain-on-car-roof.mp3, etc.</li>
              <li>Nature/campfire.mp3, droplets.mp3, river.mp3, waves.mp3, etc.</li>
              <li>Noise/brown-noise.wav, pink-noise.wav, white-noise.wav</li>
              <li>Animals/beehive.mp3, birds.mp3, cat-purring.mp3, etc.</li>
              <li>Things/boiling-water.mp3, bubbles.mp3, clock.mp3, etc.</li>
              <li>Transport/airplane.mp3, train.mp3, sailboat.mp3, etc.</li>
              <li>Urban/busy-street.mp3, traffic.mp3, highway.mp3, etc.</li>
              <li>Places/airport.mp3, cafe.mp3, church.mp3, etc.</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
