'use client';

import { useTranslations } from 'next-intl';
import { AudioCard } from '@/components/AudioCard/AudioCard';
import { PlayerDebug } from '@/components/Debug/PlayerDebug';
import { PlayerTestSuite } from '@/components/Debug/PlayerTestSuite';
import { MultilingualAudioItem } from '@/types/audio';

// 测试音频数据
const testAudioList: MultilingualAudioItem[] = [
  {
    id: 'test-rain-1',
    title: {
      zh: '轻柔雨声',
      en: 'Gentle Rain'
    },
    description: {
      zh: '舒缓的雨声，帮助放松和睡眠',
      en: 'Soothing rain sounds for relaxation and sleep'
    },
    category: 'rain',
    tags: ['rain', 'nature', 'sleep'],
    duration: 30,
    filename: 'gentle-rain.wav',



    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'test-rain-2',
    title: {
      zh: '暴雨声',
      en: 'Heavy Rain'
    },
    description: {
      zh: '强烈的暴雨声，适合深度专注',
      en: 'Intense rain sounds for deep focus'
    },
    category: 'rain',
    tags: ['rain', 'storm', 'focus'],
    duration: 30,
    filename: 'heavy-rain.wav',



    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'test-nature-1',
    title: {
      zh: '森林鸟声',
      en: 'Forest Birds'
    },
    description: {
      zh: '清晨森林中的鸟儿歌唱',
      en: 'Morning birds singing in the forest'
    },
    category: 'nature',
    tags: ['birds', 'forest', 'morning'],
    duration: 30,
    filename: 'forest-birds.wav',



    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'test-ocean-1',
    title: {
      zh: '海浪声',
      en: 'Ocean Waves'
    },
    description: {
      zh: '平静的海浪拍打海岸的声音',
      en: 'Peaceful ocean waves hitting the shore'
    },
    category: 'ocean',
    tags: ['ocean', 'waves', 'peaceful'],
    duration: 30,
    filename: 'ocean-waves.wav',



    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

export default function TestPlayerPage() {
  const t = useTranslations('common');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <PlayerDebug />
      {testAudioList[0] && <PlayerTestSuite testAudio={testAudioList[0]} />}
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            音频播放器测试页面
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            点击下面的音频卡片来测试播放器功能
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testAudioList.map((audio, index) => (
            <AudioCard
              key={audio.id}
              audio={audio}
              variant={index === 3 ? "compact" : "default"}
              showDuration={true}


            />
          ))}
        </div>

        <div className="mt-12 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            测试说明
          </h2>
          <ul className="space-y-2 text-gray-600 dark:text-gray-400">
            <li>• 点击任意音频卡片应该会显示底部播放器</li>
            <li>• 播放器应该显示当前播放的音频信息</li>
            <li>• 播放器应该有播放/暂停、停止、音量控制等功能</li>
            <li>• 在桌面端应该显示定时器和混音按钮</li>
            <li>• 在移动端这些按钮应该被隐藏</li>
            <li>• 播放器应该支持最小化和关闭</li>
            <li>• 播放器应该有平滑的动画效果</li>
            <li>• 音频文件为30秒的测试文件（静音），用于验证播放器功能</li>
            <li>• 测试不同类型的音频：雨声、自然声音、海浪声</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
