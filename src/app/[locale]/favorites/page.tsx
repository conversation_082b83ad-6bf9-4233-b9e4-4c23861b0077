import { getTranslations } from 'next-intl/server';
import { FavoritesContent } from './FavoritesContent';

interface FavoritesPageProps {
  params: {
    locale: string;
  };
}

export async function generateMetadata({ params }: { params: { locale: string } }) {
  const t = await getTranslations({ locale: params.locale, namespace: 'metadata' });

  return {
    title: t('favorites.title'),
    description: t('favorites.description'),
  };
}

export default function FavoritesPage({ params }: FavoritesPageProps) {
  return <FavoritesContent params={params} />;
}
