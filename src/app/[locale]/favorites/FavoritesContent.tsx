'use client';

import { useTranslations } from 'next-intl';
import { AudioGrid } from '@/components/AudioCard';
import { PageLayout } from '@/components/Layout';
import { useAudioStore } from '@/store/audioStore';
import { audioData } from '@/data/audioData_real';

interface FavoritesContentProps {
  params: {
    locale: string;
  };
}

export function FavoritesContent({ params }: FavoritesContentProps) {
  const t = useTranslations('favorites');
  const { favorites } = useAudioStore();

  // 获取收藏的音频数据
  const favoriteAudios = audioData.filter(audio => favorites.includes(audio.id));

  return (
    <PageLayout headerVariant="default" showGetStarted={false}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
            {t('description')}
          </p>

          {/* 统计信息 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 mb-6">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {t('totalFavorites', { count: favoriteAudios.length })}
                </span>
              </div>
              
              {favoriteAudios.length > 0 && (
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5 text-amber-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {t('averageRating', { 
                      rating: (favoriteAudios.reduce((sum, audio) => sum + (audio.scientificRating || 0), 0) / favoriteAudios.length).toFixed(1)
                    })}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 音频网格或空状态 */}
        {favoriteAudios.length > 0 ? (
          <AudioGrid
            audios={favoriteAudios}
            variant="default"
            showSearch={true}
            showFilter={true}
            showSort={true}
            emptyMessage={t('noFavorites')}
          />
        ) : (
          <div className="text-center py-16">
            <div className="w-24 h-24 mx-auto mb-6 text-gray-300 dark:text-gray-600">
              <svg fill="currentColor" viewBox="0 0 24 24">
                <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">
              {t('emptyTitle')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              {t('emptyDescription')}
            </p>
            <a
              href={params.locale === 'zh' ? '/zh/sounds' : '/sounds'}
              className="inline-flex items-center gap-2 px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
              </svg>
              {t('browseAudio')}
            </a>
          </div>
        )}
      </div>
    </PageLayout>
  );
}
