import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { AudioGrid } from '@/components/AudioCard';
import { PageLayout } from '@/components/Layout';
import { audioData } from '@/data/audioData_real';

interface SoundsPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    search?: string;
  };
}

export async function generateMetadata({ params }: { params: { locale: string } }) {
  const t = await getTranslations({ locale: params.locale, namespace: 'metadata' });
  
  return {
    title: t('sounds.title'),
    description: t('sounds.description'),
  };
}

export default function SoundsPage({ params, searchParams }: SoundsPageProps) {
  const t = useTranslations('sounds');
  const { locale } = params;

  // 获取所有音频数据
  const allAudios = audioData;

  // 获取分类统计
  const categoryStats = allAudios.reduce((acc, audio) => {
    acc[audio.category] = (acc[audio.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const categories = Object.keys(categoryStats).sort();

  return (
    <PageLayout headerVariant="default" showGetStarted={false}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
            {t('description')}
          </p>

          {/* 分类快速导航 */}
          <div className="flex flex-wrap gap-2">
            <a
              href={locale === 'zh' ? '/zh/sounds' : '/sounds'}
              className="px-4 py-2 rounded-full text-sm font-medium transition-colors bg-indigo-500 text-white"
            >
              {t('allCategories')} ({allAudios.length})
            </a>
            {categories.map((category) => (
              <a
                key={category}
                href={locale === 'zh' ? `/zh/sounds/${encodeURIComponent(category)}` : `/sounds/${encodeURIComponent(category)}`}
                className="px-4 py-2 rounded-full text-sm font-medium transition-colors bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                {getCategoryIcon(category)} {category} ({categoryStats[category]})
              </a>
            ))}
          </div>
        </div>

        {/* 音频网格 */}
        <AudioGrid
          audios={allAudios}
          variant="default"
          showSearch={true}
          showFilter={true}
          showSort={true}
          emptyMessage={t('noAudiosFound')}
        />
      </div>
    </PageLayout>
  );
}

// 获取分类图标
function getCategoryIcon(category: string): string {
  const iconMap: Record<string, string> = {
    rain: '🌧️',
    nature: '🌿',
    noise: '🔊',
    animals: '🐾',
    things: '🏠',
    transport: '🚗',
    urban: '🏙️',
    places: '📍',
  };
  return iconMap[category.toLowerCase()] || '🎵';
}
