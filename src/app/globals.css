@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义CSS变量 */
:root {
  /* 颜色系统 */
  --color-primary: 245 158 11; /* amber-500 */
  --color-primary-dark: 217 119 6; /* amber-600 */
  --color-background: 255 255 255;
  --color-foreground: 15 23 42;
  --color-muted: 248 250 252;
  --color-muted-foreground: 100 116 139;
  --color-border: 226 232 240;
  --color-input: 226 232 240;
  --color-ring: 245 158 11;
  
  /* 字体系统 */
  --font-sans: var(--font-inter);
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  /* 边框圆角 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* 深色模式 */
[data-theme="dark"] {
  --color-background: 15 23 42;
  --color-foreground: 248 250 252;
  --color-muted: 30 41 59;
  --color-muted-foreground: 148 163 184;
  --color-border: 51 65 85;
  --color-input: 51 65 85;
}

/* 夜间模式 - 专为睡眠优化 */
[data-theme="night"] {
  --color-background: 15 15 15; /* #0f0f0f */
  --color-foreground: 229 229 229; /* #e5e5e5 */
  --color-muted: 26 26 26; /* #1a1a1a */
  --color-muted-foreground: 163 163 163; /* #a3a3a3 */
  --color-border: 51 51 51; /* #333333 */
  --color-input: 51 51 51;
  --color-ring: 245 158 11; /* 保持琥珀色 */
}

/* 中文字体优化 */
[data-locale="zh"] {
  --font-sans: var(--font-noto-sans-sc);
  line-height: 1.7; /* 中文需要更大行高 */
  letter-spacing: 0.025em; /* 中文字符间距 */
}

/* 英文字体优化 */
[data-locale="en"] {
  --font-sans: var(--font-inter);
  line-height: 1.5;
  letter-spacing: 0;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-sans);
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  transition: background-color var(--duration-normal) ease,
              color var(--duration-normal) ease;
}

/* 无障碍访问优化 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --color-border: 0 0 0;
    --color-ring: 0 0 0;
  }
  
  [data-theme="dark"] {
    --color-border: 255 255 255;
    --color-ring: 255 255 255;
  }
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid rgb(var(--color-ring));
  outline-offset: 2px;
}

/* 选择文本样式 */
::selection {
  background-color: rgb(var(--color-primary) / 0.2);
  color: rgb(var(--color-foreground));
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--color-muted));
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--color-muted-foreground) / 0.5);
  border-radius: var(--radius-md);
  transition: background-color var(--duration-fast) ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--color-muted-foreground) / 0.7);
}

/* 音频播放器专用样式 */
.audio-player {
  background: linear-gradient(135deg, 
    rgb(var(--color-background)) 0%, 
    rgb(var(--color-muted)) 100%);
  border: 1px solid rgb(var(--color-border));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal) ease;
}

.audio-player:hover {
  box-shadow: var(--shadow-lg), 0 0 20px rgb(var(--color-primary) / 0.1);
  transform: translateY(-2px);
}

/* 夜间模式音频播放器 */
[data-theme="night"] .audio-player {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-color: #333;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

[data-theme="night"] .audio-player:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 
              0 0 20px rgba(245, 158, 11, 0.2);
}

/* 播放按钮样式 */
.play-button {
  background: rgb(var(--color-primary));
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-fast) ease;
  box-shadow: var(--shadow-md);
}

.play-button:hover {
  background: rgb(var(--color-primary-dark));
  transform: scale(1.05);
  box-shadow: var(--shadow-lg), 0 0 15px rgb(var(--color-primary) / 0.4);
}

.play-button:active {
  transform: scale(0.95);
}

/* 音量滑块样式 */
.volume-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: var(--radius-lg);
  background: rgb(var(--color-muted));
  outline: none;
  transition: background var(--duration-fast) ease;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgb(var(--color-primary));
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-fast) ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  background: rgb(var(--color-primary-dark));
  transform: scale(1.1);
  box-shadow: var(--shadow-md), 0 0 10px rgb(var(--color-primary) / 0.3);
}

.volume-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgb(var(--color-primary));
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-fast) ease;
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgb(var(--color-muted));
  border-top: 4px solid rgb(var(--color-primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 脉冲动画 */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgb(var(--color-primary) / 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgb(var(--color-primary) / 0.8);
  }
}

/* 渐入动画 */
.fade-in {
  animation: fadeIn var(--duration-slow) ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停效果 */
.card-hover {
  transition: all var(--duration-normal) ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg), 0 0 25px rgb(var(--color-primary) / 0.1);
}

/* 文字渐变效果 */
.text-gradient {
  background: linear-gradient(135deg, 
    rgb(var(--color-primary)) 0%, 
    rgb(var(--color-primary-dark)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式隐藏类 */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}
