import { MetadataRoute } from 'next';
import { routing } from '@/i18n/routing';
import { audioDatabase } from '@/lib/audioDatabase';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://noisesleep.com';
  const currentDate = new Date();
  
  // 基础页面
  const basePages = [
    '',
    '/sounds',
    '/mixing',
    '/favorites',
  ];

  // 音频分类页面
  const categories = [
    'rain',
    'nature', 
    'noise',
    'animals',
    'things',
    'transport',
    'urban',
    'places'
  ];

  const sitemap: MetadataRoute.Sitemap = [];

  // 为每个基础页面生成多语言版本
  basePages.forEach(page => {
    routing.locales.forEach(locale => {
      const url = locale === 'en' 
        ? `${baseUrl}${page}`
        : `${baseUrl}/${locale}${page}`;

      sitemap.push({
        url,
        lastModified: currentDate,
        changeFrequency: page === '' ? 'daily' : 'weekly',
        priority: page === '' ? 1.0 : 0.8,
        alternates: {
          languages: {
            'en': `${baseUrl}${page}`,
            'zh': `${baseUrl}/zh${page}`
          }
        }
      });
    });
  });

  // 为每个音频分类页面生成多语言版本
  categories.forEach(category => {
    routing.locales.forEach(locale => {
      const url = locale === 'en'
        ? `${baseUrl}/sounds/${category}`
        : `${baseUrl}/${locale}/sounds/${category}`;

      sitemap.push({
        url,
        lastModified: currentDate,
        changeFrequency: 'weekly',
        priority: 0.7,
        alternates: {
          languages: {
            'en': `${baseUrl}/sounds/${category}`,
            'zh': `${baseUrl}/zh/sounds/${category}`
          }
        }
      });
    });
  });

  // 为每个音频文件生成详情页面（如果有的话）
  try {
    audioDatabase.forEach(audio => {
      routing.locales.forEach(locale => {
        const url = locale === 'en'
          ? `${baseUrl}/sounds/${audio.category}/${audio.id}`
          : `${baseUrl}/${locale}/sounds/${audio.category}/${audio.id}`;

        sitemap.push({
          url,
          lastModified: currentDate,
          changeFrequency: 'monthly',
          priority: 0.6,
          alternates: {
            languages: {
              'en': `${baseUrl}/sounds/${audio.category}/${audio.id}`,
              'zh': `${baseUrl}/zh/sounds/${audio.category}/${audio.id}`
            }
          }
        });
      });
    });
  } catch (error) {
    console.warn('无法加载音频数据库，跳过音频详情页面的sitemap生成');
  }

  return sitemap;
}
