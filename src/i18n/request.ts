import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  // 获取请求的locale
  let locale = await requestLocale;

  // 验证传入的locale是否有效，如果无效则使用默认locale
  if (!locale || !routing.locales.includes(locale as any)) {
    console.warn(`Invalid locale: ${locale}, using default locale: ${routing.defaultLocale}`);
    locale = routing.defaultLocale;
  }

  return {
    locale,
    messages: (await import(`./locales/${locale}.json`)).default,
    timeZone: locale === 'zh' ? 'Asia/Shanghai' : 'America/New_York',
    now: new Date(),
    formats: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        }
      },
      number: {
        precise: {
          maximumFractionDigits: 2
        }
      }
    }
  };
});
