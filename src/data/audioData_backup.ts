import { MultilingualAudioItem } from '@/types/audio';

export const audioData: MultilingualAudioItem[] = [
  // Rain Category (3 files - 匹配实际文件)
  {
    id: 'rain_gentle_rain',
    filename: 'gentle-rain.wav',
    category: 'rain',
    title: {
      en: 'Gentle Rain',
      zh: '轻柔雨声'
    },
    description: {
      en: 'Soft, gentle rain falling steadily for relaxation',
      zh: '轻柔细雨，持续降落，带来放松感受'
    },
    tags: ['gentle', 'soft', 'continuous'],
    duration: 3600, // 1 hour
    scientificRating: 9.2,
    sleepEffectiveness: 9.5,
    focusEffectiveness: 8.8
  },
  {
    id: 'rain_heavy_rain',
    filename: 'heavy-rain.wav',
    category: 'rain',
    title: {
      en: 'Heavy Rain',
      zh: '大雨'
    },
    description: {
      en: 'Intense rainfall with thunder for deep relaxation',
      zh: '强烈降雨伴随雷声，深度放松'
    },
    tags: ['intense', 'thunder', 'powerful'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 7.5
  },
  {
    id: 'rain_drops',
    filename: 'rain-drops.wav',
    category: 'rain',
    title: {
      en: 'Rain Drops',
      zh: '雨滴声'
    },
    description: {
      en: 'Rhythmic rain drops for peaceful relaxation',
      zh: '有节奏的雨滴声，带来平静放松'
    },
    tags: ['rhythmic', 'peaceful', 'relaxing'],
    duration: 3600,
    scientificRating: 9.0,
    sleepEffectiveness: 9.2,
    focusEffectiveness: 8.5
  },

  // Nature Category (2 files - 匹配实际文件)
  {
    id: 'nature_forest_birds',
    filename: 'forest-birds.wav',
    category: 'nature',
    title: {
      en: 'Forest Birds',
      zh: '森林鸟鸣'
    },
    description: {
      en: 'Peaceful bird songs in the forest',
      zh: '森林中宁静的鸟儿歌声'
    },
    tags: ['birds', 'forest', 'natural'],
    duration: 3600,
    scientificRating: 8.7,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 9.0
  },
  {
    id: 'nature_wind_leaves',
    filename: 'wind-leaves.wav',
    category: 'nature',
    title: {
      en: 'Wind in Leaves',
      zh: '风吹叶子'
    },
    description: {
      en: 'Gentle wind rustling through leaves',
      zh: '微风轻抚树叶的沙沙声'
    },
    tags: ['wind', 'leaves', 'gentle'],
    duration: 3600,
    scientificRating: 8.5,
    sleepEffectiveness: 8.7,
    focusEffectiveness: 8.8
  },

  // Ocean Category (1 file - 匹配实际文件)
  {
    id: 'ocean_waves',
    filename: 'ocean-waves.wav',
    category: 'ocean',
    title: {
      en: 'Ocean Waves',
      zh: '海浪声'
    },
    description: {
      en: 'Gentle ocean waves for deep relaxation',
      zh: '轻柔海浪声，带来深度放松'
    },
    tags: ['ocean', 'waves', 'peaceful'],
    duration: 3600,
    scientificRating: 9.0,
    sleepEffectiveness: 9.2,
    focusEffectiveness: 8.5
  },

  // White Noise Category (1 file - 匹配实际文件)
  {
    id: 'white_noise_pink',
    filename: 'pink-noise.wav',
    category: 'white-noise',
    title: {
      en: 'Pink Noise',
      zh: '粉红噪音'
    },
    description: {
      en: 'Balanced pink noise for focus and sleep',
      zh: '平衡的粉红噪音，有助于专注和睡眠'
    },
    tags: ['pink-noise', 'focus', 'sleep'],
    duration: 3600,
    scientificRating: 9.5,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 9.8
  }
];

// 按分类分组的音频数据
export const audioByCategory = audioData.reduce((acc, audio) => {
  if (!acc[audio.category]) {
    acc[audio.category] = [];
  }
  acc[audio.category]!.push(audio);
  return acc;
}, {} as Record<string, MultilingualAudioItem[]>);

// 获取所有分类
export const categories = Object.keys(audioByCategory).sort();

// 获取分类统计
export const categoryStats = categories.map(category => ({
  name: category,
  count: audioByCategory[category]!.length,
  avgRating: audioByCategory[category]!.reduce((sum, audio) => sum + (audio.scientificRating || 0), 0) / audioByCategory[category]!.length
}));
