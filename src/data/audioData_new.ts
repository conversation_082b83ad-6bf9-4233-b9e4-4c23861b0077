import { MultilingualAudioItem } from '@/types/audio';

export const audioData: MultilingualAudioItem[] = [
  // Rain Category - 匹配实际文件 (/Sounds/Rain/)
  {
    id: 'rain_heavy_rain',
    filename: 'heavy-rain.mp3',
    category: 'rain',
    title: {
      en: 'Heavy Rain',
      zh: '大雨'
    },
    description: {
      en: 'Intense rainfall for deep relaxation',
      zh: '强烈降雨，深度放松'
    },
    tags: ['intense', 'powerful', 'relaxing'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 7.5
  },
  {
    id: 'rain_light_rain',
    filename: 'light-rain.mp3',
    category: 'rain',
    title: {
      en: 'Light Rain',
      zh: '轻雨'
    },
    description: {
      en: 'Gentle light rain for peaceful relaxation',
      zh: '轻柔细雨，宁静放松'
    },
    tags: ['gentle', 'soft', 'peaceful'],
    duration: 3600,
    scientificRating: 9.2,
    sleepEffectiveness: 9.5,
    focusEffectiveness: 8.8
  },
  {
    id: 'rain_on_car_roof',
    filename: 'rain-on-car-roof.mp3',
    category: 'rain',
    title: {
      en: 'Rain on Car Roof',
      zh: '雨打车顶'
    },
    description: {
      en: 'Rain drumming on car roof for cozy atmosphere',
      zh: '雨滴敲打车顶，营造温馨氛围'
    },
    tags: ['cozy', 'rhythmic', 'comforting'],
    duration: 3600,
    scientificRating: 8.5,
    sleepEffectiveness: 8.7,
    focusEffectiveness: 8.0
  },
  {
    id: 'rain_on_leaves',
    filename: 'rain-on-leaves.mp3',
    category: 'rain',
    title: {
      en: 'Rain on Leaves',
      zh: '雨打树叶'
    },
    description: {
      en: 'Rain falling on leaves for natural ambiance',
      zh: '雨滴落在树叶上，自然环境音'
    },
    tags: ['natural', 'organic', 'soothing'],
    duration: 3600,
    scientificRating: 8.8,
    sleepEffectiveness: 9.1,
    focusEffectiveness: 8.3
  },
  {
    id: 'rain_on_tent',
    filename: 'rain-on-tent.mp3',
    category: 'rain',
    title: {
      en: 'Rain on Tent',
      zh: '雨打帐篷'
    },
    description: {
      en: 'Rain pattering on tent fabric for camping atmosphere',
      zh: '雨滴敲打帐篷，露营氛围'
    },
    tags: ['camping', 'adventure', 'cozy'],
    duration: 3600,
    scientificRating: 8.4,
    sleepEffectiveness: 8.6,
    focusEffectiveness: 7.8
  },
  {
    id: 'rain_on_umbrella',
    filename: 'rain-on-umbrella.mp3',
    category: 'rain',
    title: {
      en: 'Rain on Umbrella',
      zh: '雨打雨伞'
    },
    description: {
      en: 'Rain hitting umbrella for intimate sound experience',
      zh: '雨滴敲打雨伞，亲密的声音体验'
    },
    tags: ['intimate', 'close', 'personal'],
    duration: 3600,
    scientificRating: 8.3,
    sleepEffectiveness: 8.5,
    focusEffectiveness: 7.9
  },
  {
    id: 'rain_on_window',
    filename: 'rain-on-window.mp3',
    category: 'rain',
    title: {
      en: 'Rain on Window',
      zh: '雨打窗户'
    },
    description: {
      en: 'Rain streaming down window glass for indoor comfort',
      zh: '雨水流淌在窗玻璃上，室内舒适感'
    },
    tags: ['indoor', 'comfort', 'peaceful'],
    duration: 3600,
    scientificRating: 8.7,
    sleepEffectiveness: 8.9,
    focusEffectiveness: 8.1
  },
  {
    id: 'rain_thunder',
    filename: 'thunder.mp3',
    category: 'rain',
    title: {
      en: 'Thunder',
      zh: '雷声'
    },
    description: {
      en: 'Thunder sounds for dramatic weather atmosphere',
      zh: '雷声，戏剧性天气氛围'
    },
    tags: ['dramatic', 'powerful', 'weather'],
    duration: 3600,
    scientificRating: 7.8,
    sleepEffectiveness: 7.5,
    focusEffectiveness: 6.8
  },

  // Nature Category - 匹配实际文件 (/Sounds/Nature/)
  {
    id: 'nature_campfire',
    filename: 'campfire.mp3',
    category: 'nature',
    title: {
      en: 'Campfire',
      zh: '篝火'
    },
    description: {
      en: 'Crackling campfire for cozy outdoor atmosphere',
      zh: '噼啪作响的篝火，温馨户外氛围'
    },
    tags: ['cozy', 'warm', 'outdoor'],
    duration: 3600,
    scientificRating: 8.6,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 8.2
  },
  {
    id: 'nature_droplets',
    filename: 'droplets.mp3',
    category: 'nature',
    title: {
      en: 'Water Droplets',
      zh: '水滴'
    },
    description: {
      en: 'Gentle water droplets for meditation',
      zh: '轻柔水滴声，适合冥想'
    },
    tags: ['gentle', 'meditation', 'pure'],
    duration: 3600,
    scientificRating: 9.0,
    sleepEffectiveness: 9.3,
    focusEffectiveness: 8.7
  },
  {
    id: 'nature_howling_wind',
    filename: 'howling-wind.mp3',
    category: 'nature',
    title: {
      en: 'Howling Wind',
      zh: '呼啸风声'
    },
    description: {
      en: 'Wind howling through landscapes',
      zh: '风声呼啸穿过大地'
    },
    tags: ['wind', 'atmospheric', 'dramatic'],
    duration: 3600,
    scientificRating: 7.9,
    sleepEffectiveness: 7.8,
    focusEffectiveness: 7.2
  },
  {
    id: 'nature_jungle',
    filename: 'jungle.mp3',
    category: 'nature',
    title: {
      en: 'Jungle',
      zh: '丛林'
    },
    description: {
      en: 'Tropical jungle ambiance with wildlife',
      zh: '热带丛林环境音，伴有野生动物声'
    },
    tags: ['tropical', 'wildlife', 'exotic'],
    duration: 3600,
    scientificRating: 8.4,
    sleepEffectiveness: 8.2,
    focusEffectiveness: 7.8
  },
  {
    id: 'nature_river',
    filename: 'river.mp3',
    category: 'nature',
    title: {
      en: 'River',
      zh: '河流'
    },
    description: {
      en: 'Flowing river water for natural relaxation',
      zh: '流淌的河水，自然放松'
    },
    tags: ['flowing', 'natural', 'peaceful'],
    duration: 3600,
    scientificRating: 9.1,
    sleepEffectiveness: 9.4,
    focusEffectiveness: 8.9
  },
  {
    id: 'nature_walk_in_snow',
    filename: 'walk-in-snow.mp3',
    category: 'nature',
    title: {
      en: 'Walk in Snow',
      zh: '雪中漫步'
    },
    description: {
      en: 'Footsteps crunching in fresh snow',
      zh: '脚步踩在新雪上的声音'
    },
    tags: ['winter', 'crisp', 'peaceful'],
    duration: 3600,
    scientificRating: 8.2,
    sleepEffectiveness: 8.4,
    focusEffectiveness: 8.0
  },
  {
    id: 'nature_walk_on_gravel',
    filename: 'walk-on-gravel.mp3',
    category: 'nature',
    title: {
      en: 'Walk on Gravel',
      zh: '砾石路漫步'
    },
    description: {
      en: 'Footsteps on gravel path',
      zh: '脚步声在砾石路上'
    },
    tags: ['walking', 'texture', 'rhythmic'],
    duration: 3600,
    scientificRating: 7.8,
    sleepEffectiveness: 7.9,
    focusEffectiveness: 7.5
  },
  {
    id: 'nature_walk_on_leaves',
    filename: 'walk-on-leaves.mp3',
    category: 'nature',
    title: {
      en: 'Walk on Leaves',
      zh: '踏叶而行'
    },
    description: {
      en: 'Footsteps rustling through autumn leaves',
      zh: '脚步踩过秋叶的沙沙声'
    },
    tags: ['autumn', 'rustling', 'seasonal'],
    duration: 3600,
    scientificRating: 8.3,
    sleepEffectiveness: 8.5,
    focusEffectiveness: 8.1
  },
  {
    id: 'nature_waterfall',
    filename: 'waterfall.mp3',
    category: 'nature',
    title: {
      en: 'Waterfall',
      zh: '瀑布'
    },
    description: {
      en: 'Powerful waterfall cascading down rocks',
      zh: '瀑布从岩石上倾泻而下'
    },
    tags: ['powerful', 'cascading', 'majestic'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 8.7,
    focusEffectiveness: 8.4
  },
  {
    id: 'nature_waves',
    filename: 'waves.mp3',
    category: 'nature',
    title: {
      en: 'Ocean Waves',
      zh: '海浪'
    },
    description: {
      en: 'Gentle ocean waves lapping the shore',
      zh: '轻柔的海浪拍打海岸'
    },
    tags: ['ocean', 'rhythmic', 'calming'],
    duration: 3600,
    scientificRating: 9.3,
    sleepEffectiveness: 9.6,
    focusEffectiveness: 9.0
  },
  {
    id: 'nature_wind_in_trees',
    filename: 'wind-in-trees.mp3',
    category: 'nature',
    title: {
      en: 'Wind in Trees',
      zh: '林间风声'
    },
    description: {
      en: 'Wind rustling through tree branches',
      zh: '风吹过树枝的沙沙声'
    },
    tags: ['rustling', 'peaceful', 'forest'],
    duration: 3600,
    scientificRating: 8.8,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 8.6
  },
  {
    id: 'nature_wind',
    filename: 'wind.mp3',
    category: 'nature',
    title: {
      en: 'Wind',
      zh: '风声'
    },
    description: {
      en: 'Gentle wind blowing across open spaces',
      zh: '轻风吹过开阔地带'
    },
    tags: ['gentle', 'open', 'airy'],
    duration: 3600,
    scientificRating: 8.5,
    sleepEffectiveness: 8.7,
    focusEffectiveness: 8.3
  },

  // Noise Category - 匹配实际文件 (/Sounds/Noise/)
  {
    id: 'noise_brown_noise',
    filename: 'brown-noise.wav',
    category: 'noise',
    title: {
      en: 'Brown Noise',
      zh: '棕色噪音'
    },
    description: {
      en: 'Deep brown noise for focus and relaxation',
      zh: '深沉的棕色噪音，专注与放松'
    },
    tags: ['deep', 'focus', 'masking'],
    duration: 3600,
    scientificRating: 9.0,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 9.2
  },
  {
    id: 'noise_pink_noise',
    filename: 'pink-noise.wav',
    category: 'noise',
    title: {
      en: 'Pink Noise',
      zh: '粉色噪音'
    },
    description: {
      en: 'Balanced pink noise for sleep and concentration',
      zh: '平衡的粉色噪音，助眠与专注'
    },
    tags: ['balanced', 'sleep', 'concentration'],
    duration: 3600,
    scientificRating: 9.2,
    sleepEffectiveness: 9.4,
    focusEffectiveness: 9.1
  },
  {
    id: 'noise_white_noise',
    filename: 'white-noise.wav',
    category: 'noise',
    title: {
      en: 'White Noise',
      zh: '白色噪音'
    },
    description: {
      en: 'Classic white noise for sound masking',
      zh: '经典白色噪音，声音遮蔽'
    },
    tags: ['classic', 'masking', 'consistent'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 9.3
  },

  // Animals Category - 匹配实际文件 (/Sounds/Animals/)
  {
    id: 'animals_beehive',
    filename: 'beehive.mp3',
    category: 'animals',
    title: {
      en: 'Beehive',
      zh: '蜂巢'
    },
    description: {
      en: 'Gentle buzzing of bees in their hive',
      zh: '蜜蜂在蜂巢中轻柔的嗡嗡声'
    },
    tags: ['buzzing', 'gentle', 'productive'],
    duration: 3600,
    scientificRating: 8.1,
    sleepEffectiveness: 8.3,
    focusEffectiveness: 8.5
  },
  {
    id: 'animals_birds',
    filename: 'birds.mp3',
    category: 'animals',
    title: {
      en: 'Birds',
      zh: '鸟鸣'
    },
    description: {
      en: 'Cheerful bird songs for morning atmosphere',
      zh: '欢快的鸟鸣声，晨间氛围'
    },
    tags: ['cheerful', 'morning', 'natural'],
    duration: 3600,
    scientificRating: 8.7,
    sleepEffectiveness: 8.2,
    focusEffectiveness: 8.8
  },
  {
    id: 'animals_cat_purring',
    filename: 'cat-purring.mp3',
    category: 'animals',
    title: {
      en: 'Cat Purring',
      zh: '猫咪呼噜声'
    },
    description: {
      en: 'Soothing cat purring for comfort and relaxation',
      zh: '舒缓的猫咪呼噜声，带来安慰与放松'
    },
    tags: ['soothing', 'comfort', 'cozy'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 9.1,
    focusEffectiveness: 8.4
  }
];
