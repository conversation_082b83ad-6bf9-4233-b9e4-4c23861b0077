import { Metadata } from 'next';

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  locale: string;
  path?: string;
  image?: string;
  type?: 'website' | 'article';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

/**
 * 生成完整的SEO元数据
 */
export function generateSEOMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [],
    canonical,
    locale,
    path = '',
    image = '/icon-192x192.png',
    type = 'website',
    author,
    publishedTime,
    modifiedTime
  } = config;

  const baseUrl = 'https://noisesleep.com';
  const fullUrl = canonical || `${baseUrl}${path}`;
  const fullImageUrl = image.startsWith('http') ? image : `${baseUrl}${image}`;

  // 构建alternates
  const alternates: any = {
    canonical: fullUrl,
    languages: {
      'en': `${baseUrl}${path}`,
      'zh': `${baseUrl}/zh${path}`,
    }
  };

  const metadata: Metadata = {
    title,
    description,
    keywords: keywords.join(', '),
    authors: author ? [{ name: author }] : undefined,
    
    // Open Graph
    openGraph: {
      title,
      description,
      url: fullUrl,
      siteName: locale === 'zh' ? '睡个好觉' : 'Sleep Well',
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type,
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
      publishedTime,
      modifiedTime,
    },

    // Twitter
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [fullImageUrl],
      creator: '@noisesleep',
      site: '@noisesleep',
    },

    // Alternates
    alternates,

    // 其他元数据
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    // 验证标签
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
    },
  };

  return metadata;
}

/**
 * 生成结构化数据 (JSON-LD)
 */
export function generateStructuredData(config: {
  type: 'WebSite' | 'WebPage' | 'AudioObject' | 'Organization' | 'Article';
  locale: string;
  data: any;
}) {
  const { type, locale, data } = config;
  const baseUrl = 'https://noisesleep.com';

  const baseStructuredData = {
    '@context': 'https://schema.org',
    '@type': type,
    ...data
  };

  // 根据类型添加特定的结构化数据
  switch (type) {
    case 'WebSite':
      return {
        ...baseStructuredData,
        name: locale === 'zh' ? '睡个好觉' : 'Sleep Well',
        alternateName: locale === 'zh' ? 'NoiseSleep' : '睡个好觉',
        url: baseUrl,
        description: locale === 'zh' 
          ? '基于科学的睡眠音频平台，提供高质量白噪音、自然声音和助眠音乐'
          : 'Science-based sleep audio platform with high-quality white noise, nature sounds, and sleep music',
        potentialAction: {
          '@type': 'SearchAction',
          target: `${baseUrl}/sounds?q={search_term_string}`,
          'query-input': 'required name=search_term_string'
        },
        publisher: {
          '@type': 'Organization',
          name: locale === 'zh' ? '睡个好觉' : 'Sleep Well',
          logo: {
            '@type': 'ImageObject',
            url: `${baseUrl}/icon-192x192.png`
          }
        }
      };

    case 'AudioObject':
      return {
        ...baseStructuredData,
        encodingFormat: 'audio/mpeg',
        contentUrl: data.contentUrl,
        duration: data.duration,
        genre: locale === 'zh' ? '助眠音频' : 'Sleep Audio',
        inLanguage: locale,
        isAccessibleForFree: true,
        license: 'https://creativecommons.org/licenses/by/4.0/',
        publisher: {
          '@type': 'Organization',
          name: locale === 'zh' ? '睡个好觉' : 'Sleep Well'
        }
      };

    case 'Organization':
      return {
        ...baseStructuredData,
        name: locale === 'zh' ? '睡个好觉' : 'Sleep Well',
        alternateName: 'NoiseSleep',
        url: baseUrl,
        logo: `${baseUrl}/icon-192x192.png`,
        sameAs: [
          'https://twitter.com/noisesleep',
          'https://facebook.com/noisesleep'
        ],
        contactPoint: {
          '@type': 'ContactPoint',
          contactType: 'customer service',
          availableLanguage: ['English', 'Chinese']
        }
      };

    default:
      return baseStructuredData;
  }
}

/**
 * 生成hreflang标签
 */
export function generateHreflangTags(path: string) {
  const baseUrl = 'https://noisesleep.com';
  
  return [
    {
      hrefLang: 'en',
      href: `${baseUrl}${path}`
    },
    {
      hrefLang: 'zh',
      href: `${baseUrl}/zh${path}`
    },
    {
      hrefLang: 'x-default',
      href: `${baseUrl}${path}`
    }
  ];
}

/**
 * 优化标题长度
 */
export function optimizeTitle(title: string, maxLength: number = 60): string {
  if (title.length <= maxLength) return title;
  
  // 在单词边界处截断
  const truncated = title.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + '...';
  }
  
  return truncated + '...';
}

/**
 * 优化描述长度
 */
export function optimizeDescription(description: string, maxLength: number = 155): string {
  if (description.length <= maxLength) return description;
  
  // 在句子边界处截断
  const truncated = description.substring(0, maxLength);
  const lastPeriod = truncated.lastIndexOf('。');
  const lastDot = truncated.lastIndexOf('.');
  const lastBoundary = Math.max(lastPeriod, lastDot);
  
  if (lastBoundary > maxLength * 0.8) {
    return truncated.substring(0, lastBoundary + 1);
  }
  
  return truncated + '...';
}
