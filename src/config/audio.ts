// src/config/audio.ts
// NoiseSleep音频CDN配置

/**
 * 音频配置常量
 */
export const AUDIO_CONFIG = {
  // 基础URL配置
  CDN_BASE_URL: 'https://cdn.noisesleep.com/sounds',
  LOCAL_BASE_URL: '/sounds',
  
  // 当前环境的基础URL
  BASE_URL: process.env.NEXT_PUBLIC_AUDIO_CDN_URL || 
    (process.env.NODE_ENV === 'production' 
      ? 'https://cdn.noisesleep.com/sounds'
      : '/sounds'),
  
  // 性能配置
  ENABLE_PRELOAD: process.env.NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD === 'true',
  PRELOAD_COUNT: 5,
  
  // 质量配置
  QUALITY: (process.env.NEXT_PUBLIC_AUDIO_QUALITY as 'low' | 'medium' | 'high') || 'high',
  
  // 缓存配置
  CACHE_MAX_SIZE: 50 * 1024 * 1024, // 50MB
  CACHE_TTL: 24 * 60 * 60 * 1000, // 24小时
  
  // 渐进迁移配置 (用于A/B测试)
  CDN_PERCENTAGE: parseInt(process.env.NEXT_PUBLIC_CDN_PERCENTAGE || '100'),
} as const;

/**
 * 分类名称映射
 * 将audioData中的小写分类名映射到CDN的目录结构
 */
const CATEGORY_MAP: Record<string, string> = {
  'rain': 'rain',
  'nature': 'nature',
  'noise': 'noise',
  'animals': 'animals',
  'things': 'things',
  'transport': 'transport',
  'urban': 'urban',
  'places': 'places',
  'ocean': 'ocean',
} as const;

/**
 * 获取音频文件的完整URL
 * @param category 音频分类
 * @param filename 文件名
 * @returns 完整的音频文件URL
 */
export function getAudioUrl(category: string, filename: string): string {
  // 🚨 CRITICAL DEBUG: 函数被调用
  console.log('🚨🚨🚨 getAudioUrl 函数被调用!!! 🚨🚨🚨');
  console.log('📥 输入参数:', { category, filename });

  const folderName = CATEGORY_MAP[category.toLowerCase()] || category.toLowerCase();
  console.log('📁 文件夹映射:', { category, folderName });

  // 调试日志：检查环境变量
  console.log('🔍 getAudioUrl 调试:', {
    category,
    filename,
    folderName,
    NEXT_PUBLIC_AUDIO_CDN_URL: process.env.NEXT_PUBLIC_AUDIO_CDN_URL,
    NEXT_PUBLIC_CDN_PERCENTAGE: process.env.NEXT_PUBLIC_CDN_PERCENTAGE,
    allEnvVars: Object.keys(process.env).filter(key => key.startsWith('NEXT_PUBLIC_'))
  });

  // 优先使用环境变量配置的CDN URL
  if (process.env.NEXT_PUBLIC_AUDIO_CDN_URL) {
    const cdnUrl = `${process.env.NEXT_PUBLIC_AUDIO_CDN_URL}/${folderName}/${filename}`;
    console.log('✅ 使用CDN URL:', cdnUrl);
    console.log('🚨 返回CDN URL:', cdnUrl);
    return cdnUrl;
  }

  // 渐进迁移：根据百分比决定使用CDN还是本地
  const useCDN = Math.random() * 100 < AUDIO_CONFIG.CDN_PERCENTAGE;
  const baseUrl = useCDN ? AUDIO_CONFIG.CDN_BASE_URL : AUDIO_CONFIG.LOCAL_BASE_URL;
  const finalUrl = `${baseUrl}/${folderName}/${filename}`;

  console.log('⚠️ 使用备用URL:', { useCDN, baseUrl, finalUrl });
  console.log('🚨 返回备用URL:', finalUrl);
  return finalUrl;
}

/**
 * 获取音频文件的预加载URL (总是使用CDN以提高性能)
 * @param category 音频分类
 * @param filename 文件名
 * @returns CDN音频文件URL
 */
export function getPreloadAudioUrl(category: string, filename: string): string {
  const folderName = CATEGORY_MAP[category.toLowerCase()] || category.toLowerCase();
  return `${AUDIO_CONFIG.CDN_BASE_URL}/${folderName}/${filename}`;
}

/**
 * 检查是否启用CDN
 * @returns 是否使用CDN
 */
export function isCDNEnabled(): boolean {
  return AUDIO_CONFIG.BASE_URL.startsWith('https://');
}

/**
 * 获取音频质量配置
 * @returns 音频质量设置
 */
export function getAudioQuality(): 'low' | 'medium' | 'high' {
  return AUDIO_CONFIG.QUALITY;
}

/**
 * 获取缓存配置
 * @returns 缓存配置对象
 */
export function getCacheConfig() {
  return {
    maxSize: AUDIO_CONFIG.CACHE_MAX_SIZE,
    ttl: AUDIO_CONFIG.CACHE_TTL,
  };
}

/**
 * 音频文件类型定义
 */
export interface AudioFileInfo {
  url: string;
  category: string;
  filename: string;
  size?: number;
  lastModified?: Date;
  cached?: boolean;
}

/**
 * 获取音频文件信息
 * @param category 音频分类
 * @param filename 文件名
 * @returns 音频文件信息对象
 */
export function getAudioFileInfo(category: string, filename: string): AudioFileInfo {
  return {
    url: getAudioUrl(category, filename),
    category,
    filename,
    cached: false, // 将由缓存系统更新
  };
}

/**
 * 验证音频URL是否可访问
 * @param url 音频URL
 * @returns Promise<boolean> 是否可访问
 */
export async function validateAudioUrl(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('音频URL验证失败:', url, error);
    return false;
  }
}

/**
 * 获取音频文件大小
 * @param url 音频URL
 * @returns Promise<number> 文件大小（字节）
 */
export async function getAudioFileSize(url: string): Promise<number> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    const contentLength = response.headers.get('content-length');
    return contentLength ? parseInt(contentLength, 10) : 0;
  } catch (error) {
    console.error('获取音频文件大小失败:', url, error);
    return 0;
  }
}

/**
 * 音频配置调试信息
 */
export function getAudioConfigDebugInfo() {
  return {
    baseUrl: AUDIO_CONFIG.BASE_URL,
    isCDN: isCDNEnabled(),
    quality: getAudioQuality(),
    preloadEnabled: AUDIO_CONFIG.ENABLE_PRELOAD,
    cdnPercentage: AUDIO_CONFIG.CDN_PERCENTAGE,
    environment: process.env.NODE_ENV,
  };
}

// 开发环境下输出配置信息
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  console.log('🎵 NoiseSleep音频配置:', getAudioConfigDebugInfo());
}
