'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

interface CategoryCardProps {
  icon: string;
  type: string;
  name: string;
  description: string;
  className?: string;
}

export default function CategoryCard({
  icon,
  type,
  name,
  description,
  className = ''
}: CategoryCardProps) {
  const t = useTranslations('landing');
  const [isPlaying, setIsPlaying] = useState(false);

  const getTypeColor = (type: string) => {
    const colors = {
      noise: 'bg-blue-100 text-blue-800',
      things: 'bg-gray-100 text-gray-800',
      transport: 'bg-yellow-100 text-yellow-800',
      places: 'bg-purple-100 text-purple-800',
      urban: 'bg-orange-100 text-orange-800',
      animals: 'bg-green-100 text-green-800',
      rain: 'bg-cyan-100 text-cyan-800',
      nature: 'bg-emerald-100 text-emerald-800',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const handlePlay = () => {
    // 简单的演示功能，切换播放状态
    setIsPlaying(!isPlaying);

    // 3秒后自动停止（模拟播放）
    if (!isPlaying) {
      setTimeout(() => {
        setIsPlaying(false);
      }, 3000);
    }
  };

  return (
    <div className={`bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100 ${className}`}>
      <div className="flex justify-between items-start mb-4">
        <div className="text-4xl">{icon}</div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(type)}`}>
          {t(`typeLabels.${type}` as any)}
        </span>
      </div>
      <h4 className="text-xl font-medium text-gray-800 mb-2">{name}</h4>
      <p className="text-gray-600 mb-4">{description}</p>
      <button
        onClick={handlePlay}
        className={`w-full py-3 rounded-full transition-all ${
          isPlaying
            ? 'bg-indigo-600 text-white'
            : 'bg-gray-100 text-gray-700 hover:bg-indigo-100'
        }`}
      >
        {isPlaying ? t('buttons.pause') : t('buttons.play')}
      </button>
    </div>
  );
}
