interface BlogPost {
  title: string;
  excerpt: string;
  date: string;
  readTime: string;
}

interface BlogCardProps {
  post: BlogPost;
  className?: string;
  onClick?: () => void;
}

export default function BlogCard({
  post,
  className = '',
  onClick
}: BlogCardProps) {
  return (
    <article
      className={`bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all cursor-pointer ${className}`}
      onClick={onClick}
    >
      <h4 className="text-xl font-medium text-gray-800 mb-3 hover:text-indigo-600 transition-colors">
        {post.title}
      </h4>
      <p className="text-gray-600 mb-4 leading-relaxed">
        {post.excerpt}
      </p>
      <div className="flex justify-between items-center text-sm text-gray-500">
        <span>{post.date}</span>
        <span>{post.readTime}</span>
      </div>
    </article>
  );
}
