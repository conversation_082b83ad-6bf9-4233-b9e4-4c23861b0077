'use client';

import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';

interface HeroSectionProps {
  className?: string;
}

export default function HeroSection({ className = '' }: HeroSectionProps) {
  const t = useTranslations('landing');
  const locale = useLocale();

  return (
    <section className={`py-20 px-4 sm:px-6 lg:px-8 ${className}`}>
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-5xl md:text-6xl font-light text-gray-800 mb-6 leading-tight">
          {locale === 'zh' ? (
            <>
              让
              <span className="text-indigo-600">
                {t('hero.titleHighlight')}
              </span>
              <br />
              陪伴你的美梦
            </>
          ) : (
            <>
              Let{' '}
              <span className="text-indigo-600">
                {t('hero.titleHighlight')}
              </span>
              <br />
              Accompany Your Sweet Dreams
            </>
          )}
        </h2>
        <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed">
          {t('hero.description')}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href={`/${locale}/sounds`}
            className="inline-block bg-indigo-600 text-white px-8 py-4 rounded-full text-lg hover:bg-indigo-700 transition-all transform hover:scale-105"
          >
            {t('buttons.experience')}
          </Link>
          <button className="border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-full text-lg hover:bg-indigo-50 transition-colors">
            {t('buttons.learnMore')}
          </button>
        </div>
      </div>
    </section>
  );
}
