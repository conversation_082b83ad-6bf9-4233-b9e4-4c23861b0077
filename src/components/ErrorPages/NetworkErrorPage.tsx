'use client';

import { getErrorPageContent } from '@/utils/i18nHelpers';
import { useLocale } from 'next-intl';

interface NetworkErrorPageProps {
  onRetry?: () => void;
  error?: Error;
}

/**
 * 网络错误页面组件
 * 当网络连接失败时显示
 */
export default function NetworkErrorPage({ onRetry, error }: NetworkErrorPageProps) {
  const locale = useLocale();
  const content = getErrorPageContent(locale, 'network');

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full mx-auto text-center px-4">
        {/* 网络错误图标 */}
        <div className="w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600">
          <svg fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM13 17h-2v-6h2v6zm0-8h-2V7h2v2z"/>
            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" opacity="0.3"/>
          </svg>
        </div>

        {/* 错误标题 */}
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          {content.title}
        </h1>

        {/* 错误消息 */}
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          {content.message}
        </p>

        {/* 操作按钮 */}
        <div className="space-y-4">
          <button
            onClick={handleRetry}
            className="w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors"
          >
            {content.action}
          </button>

          <button
            onClick={() => window.location.href = locale === 'zh' ? '/zh' : '/'}
            className="w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            {locale === 'zh' ? '返回首页' : 'Go to Homepage'}
          </button>
        </div>

        {/* 网络状态检查 */}
        <div className="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span>
              {locale === 'zh' ? '网络连接状态：离线' : 'Network Status: Offline'}
            </span>
          </div>
        </div>

        {/* 开发环境下显示错误详情 */}
        {process.env.NODE_ENV === 'development' && error && (
          <details className="mt-8 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
              {locale === 'zh' ? '错误详情 (开发模式)' : 'Error Details (Development)'}
            </summary>
            <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg text-sm">
              <div className="font-mono text-red-600 dark:text-red-400 mb-2">
                {error.message}
              </div>
              {error.stack && (
                <pre className="text-xs text-red-500 dark:text-red-300 overflow-auto">
                  {error.stack}
                </pre>
              )}
            </div>
          </details>
        )}
      </div>
    </div>
  );
}

/**
 * 网络状态检查Hook
 */
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = React.useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
}

import React from 'react';
