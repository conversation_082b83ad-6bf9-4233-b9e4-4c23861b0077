'use client';

import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';

interface CTASectionProps {
  className?: string;
}

export default function CTASection({ className = '' }: CTASectionProps) {
  const t = useTranslations('landing');
  const locale = useLocale();

  return (
    <section className={`py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-indigo-600 to-purple-600 ${className}`}>
      <div className="max-w-4xl mx-auto text-center">
        <h3 className="text-4xl font-light text-white mb-6">
          {t('cta.title')}
        </h3>
        <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
          {t('cta.description')}
        </p>
        <Link
          href={`/${locale}/sounds`}
          className="inline-block bg-white text-indigo-600 px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-50 transition-all transform hover:scale-105"
        >
          {t('buttons.startFree')}
        </Link>
      </div>
    </section>
  );
}
