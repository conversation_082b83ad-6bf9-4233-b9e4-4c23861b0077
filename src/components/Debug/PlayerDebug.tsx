'use client';

import { useAudioStore } from '@/store/audioStore';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';

export function PlayerDebug() {
  const { 
    currentSound, 
    playState, 
    playerUI 
  } = useAudioStore();
  
  const { 
    isPlaying, 
    isLoading, 
    currentTime, 
    duration 
  } = useAudioPlayer();

  return (
    <div className="fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-[100] max-w-xs">
      <h3 className="font-bold mb-2">播放器调试信息</h3>
      
      <div className="space-y-1">
        <div>当前音频: {currentSound?.title.zh || '无'}</div>
        <div>播放状态: {isPlaying ? '播放中' : '暂停'}</div>
        <div>加载状态: {isLoading ? '加载中' : '已加载'}</div>
        <div>播放器可见: {playerUI.isVisible ? '是' : '否'}</div>
        <div>播放器模式: {playerUI.mode}</div>
        <div>播放器位置: {playerUI.position}</div>
        <div>最小化: {playerUI.isMinimized ? '是' : '否'}</div>
        <div>当前时间: {Math.floor(currentTime)}s</div>
        <div>总时长: {Math.floor(duration)}s</div>
        <div>音量: {Math.floor(playState.volume * 100)}%</div>
      </div>
    </div>
  );
}
