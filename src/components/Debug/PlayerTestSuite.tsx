'use client';

import { useState } from 'react';
import { useAudioStore } from '@/store/audioStore';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { MultilingualAudioItem } from '@/types/audio';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  message?: string;
}

interface PlayerTestSuiteProps {
  testAudio: MultilingualAudioItem;
}

export function PlayerTestSuite({ testAudio }: PlayerTestSuiteProps) {
  const [tests, setTests] = useState<TestResult[]>([
    { name: '播放器显示测试', status: 'pending' },
    { name: '音频加载测试', status: 'pending' },
    { name: '播放/暂停测试', status: 'pending' },
    { name: '音量控制测试', status: 'pending' },
    { name: '进度条测试', status: 'pending' },
    { name: '停止功能测试', status: 'pending' },
    { name: '最小化测试', status: 'pending' },
    { name: '响应式布局测试', status: 'pending' },
  ]);

  const { 
    currentSound, 
    playerUI,
    setPlayerVisible,
    togglePlayerMinimized,
    setUserVolume
  } = useAudioStore();
  
  const { 
    play, 
    pause, 
    stop, 
    setVolume,
    isPlaying, 
    isLoading,
    volume,
    currentTime,
    duration
  } = useAudioPlayer();

  const updateTest = (index: number, status: TestResult['status'], message?: string) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message } : test
    ));
  };

  const runTest = async (testIndex: number) => {
    updateTest(testIndex, 'running');
    
    try {
      switch (testIndex) {
        case 0: // 播放器显示测试
          await testPlayerDisplay();
          break;
        case 1: // 音频加载测试
          await testAudioLoading();
          break;
        case 2: // 播放/暂停测试
          await testPlayPause();
          break;
        case 3: // 音量控制测试
          await testVolumeControl();
          break;
        case 4: // 进度条测试
          await testProgressBar();
          break;
        case 5: // 停止功能测试
          await testStopFunction();
          break;
        case 6: // 最小化测试
          await testMinimize();
          break;
        case 7: // 响应式布局测试
          await testResponsiveLayout();
          break;
      }
      updateTest(testIndex, 'passed', '测试通过');
    } catch (error) {
      updateTest(testIndex, 'failed', error instanceof Error ? error.message : '测试失败');
    }
  };

  const testPlayerDisplay = async () => {
    // 测试播放器显示
    play(testAudio);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (!playerUI.isVisible) {
      throw new Error('播放器未显示');
    }
    
    if (!currentSound || currentSound.id !== testAudio.id) {
      throw new Error('当前音频设置错误');
    }
  };

  const testAudioLoading = async () => {
    // 测试音频加载
    play(testAudio);
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    if (duration <= 0) {
      throw new Error('音频时长获取失败');
    }
  };

  const testPlayPause = async () => {
    // 测试播放/暂停
    play(testAudio);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (!isPlaying) {
      throw new Error('播放功能异常');
    }
    
    pause();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (isPlaying) {
      throw new Error('暂停功能异常');
    }
  };

  const testVolumeControl = async () => {
    // 测试音量控制
    const originalVolume = volume;
    
    setVolume(0.5);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (Math.abs(volume - 0.5) > 0.1) {
      throw new Error('音量设置失败');
    }
    
    // 恢复原音量
    setVolume(originalVolume);
  };

  const testProgressBar = async () => {
    // 测试进度条
    play(testAudio);
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    if (currentTime <= 0) {
      throw new Error('进度时间未更新');
    }
  };

  const testStopFunction = async () => {
    // 测试停止功能
    play(testAudio);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    stop();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (isPlaying || currentTime > 0) {
      throw new Error('停止功能异常');
    }
  };

  const testMinimize = async () => {
    // 测试最小化功能
    setPlayerVisible(true);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const wasMinimized = playerUI.isMinimized;
    togglePlayerMinimized();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (playerUI.isMinimized === wasMinimized) {
      throw new Error('最小化切换失败');
    }
  };

  const testResponsiveLayout = async () => {
    // 测试响应式布局（简单检查）
    const playerElement = document.querySelector('[data-testid="standard-player"]');
    if (!playerElement) {
      throw new Error('播放器元素未找到');
    }
    
    // 检查是否有响应式类名
    const hasResponsiveClasses = playerElement.className.includes('sm:') || 
                                playerElement.className.includes('md:') || 
                                playerElement.className.includes('lg:');
    
    if (!hasResponsiveClasses) {
      throw new Error('缺少响应式样式类');
    }
  };

  const runAllTests = async () => {
    for (let i = 0; i < tests.length; i++) {
      await runTest(i);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 测试间隔
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending': return 'text-gray-500';
      case 'running': return 'text-blue-500';
      case 'passed': return 'text-green-500';
      case 'failed': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'running': return '🔄';
      case 'passed': return '✅';
      case 'failed': return '❌';
      default: return '⏳';
    }
  };

  return (
    <div className="fixed top-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg z-[100] max-w-sm">
      <h3 className="font-bold mb-3 text-gray-900 dark:text-gray-100">播放器功能测试</h3>
      
      <div className="space-y-2 mb-4">
        {tests.map((test, index) => (
          <div key={index} className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <span>{getStatusIcon(test.status)}</span>
              <span className={getStatusColor(test.status)}>{test.name}</span>
            </div>
            <button
              onClick={() => runTest(index)}
              disabled={test.status === 'running'}
              className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              测试
            </button>
          </div>
        ))}
      </div>
      
      <div className="flex gap-2">
        <button
          onClick={runAllTests}
          className="flex-1 px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
        >
          运行全部测试
        </button>
        <button
          onClick={() => setTests(prev => prev.map(test => ({ ...test, status: 'pending', message: undefined })))}
          className="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm"
        >
          重置
        </button>
      </div>
      
      {tests.some(test => test.message) && (
        <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
          <h4 className="font-semibold mb-1">测试结果:</h4>
          {tests.filter(test => test.message).map((test, index) => (
            <div key={index} className={`${getStatusColor(test.status)} mb-1`}>
              {test.name}: {test.message}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
