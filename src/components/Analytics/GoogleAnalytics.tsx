'use client';

import Script from 'next/script';
import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_TRACKING_ID || 'G-FKSNVZQTMD';

// gtag类型声明已在 src/types/global.ts 中定义

export function GoogleAnalytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (!GA_TRACKING_ID) return;

    const url = pathname + searchParams.toString();
    
    // 页面浏览事件
    if (window.gtag) {
      window.gtag('config', GA_TRACKING_ID, {
        page_path: url,
      });
    }
  }, [pathname, searchParams]);

  if (!GA_TRACKING_ID) {
    return null;
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_path: window.location.pathname,
              anonymize_ip: true,
              allow_google_signals: false,
              allow_ad_personalization_signals: false
            });
          `,
        }}
      />
    </>
  );
}

// 自定义事件追踪函数
export const trackEvent = (action: string, category: string, label?: string, value?: number) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// 音频播放事件追踪
export const trackAudioPlay = (soundId: string, category: string, language: string) => {
  trackEvent('audio_play', 'Audio', `${category}/${soundId}`, 1);
  
  // 额外的音频特定数据
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'audio_interaction', {
      sound_id: soundId,
      sound_category: category,
      language: language,
      interaction_type: 'play'
    });
  }
};

// 混音使用追踪
export const trackMixingUsage = (sounds: string[], language: string) => {
  trackEvent('mixing_used', 'Audio', `${sounds.length}_sounds`, sounds.length);
  
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'mixing_interaction', {
      sounds_count: sounds.length,
      sound_combination: sounds.join(','),
      language: language,
    });
  }
};

// 睡眠会话追踪
export const trackSleepSession = (duration: number, sounds: string[], language: string) => {
  const durationMinutes = Math.floor(duration / 60);
  trackEvent('sleep_session_complete', 'Engagement', 'session_duration', durationMinutes);
  
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'sleep_session', {
      session_duration_seconds: duration,
      session_duration_minutes: durationMinutes,
      sounds_used: sounds.join(','),
      language: language,
    });
  }
};

// 语言切换追踪
export const trackLanguageSwitch = (fromLang: string, toLang: string) => {
  trackEvent('language_switch', 'User Preference', `${fromLang}_to_${toLang}`);
};

// 搜索追踪
export const trackSearch = (query: string, results: number, language: string) => {
  trackEvent('search', 'Search', query, results);
  
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'search', {
      search_term: query,
      results_count: results,
      language: language,
    });
  }
};

// 错误追踪
export const trackError = (error: string, context: string, fatal: boolean = false) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'exception', {
      description: error,
      fatal: fatal,
      context: context,
    });
  }
};
