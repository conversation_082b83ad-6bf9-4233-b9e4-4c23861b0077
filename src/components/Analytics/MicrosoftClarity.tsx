'use client';

import Script from 'next/script';
import { useEffect } from 'react';

const CLARITY_PROJECT_ID = process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID;
const ENABLE_ANALYTICS = process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true';

declare global {
  interface Window {
    clarity: (...args: any[]) => void;
  }
}

export function MicrosoftClarity() {
  useEffect(() => {
    // 仅在生产环境或明确启用分析时加载
    if (!CLARITY_PROJECT_ID || !ENABLE_ANALYTICS) {
      console.log('🔍 Microsoft Clarity 未启用:', { 
        hasProjectId: !!CLARITY_PROJECT_ID, 
        analyticsEnabled: ENABLE_ANALYTICS 
      });
      return;
    }

    console.log('🔍 Microsoft Clarity 已启用:', CLARITY_PROJECT_ID);
  }, []);

  // 开发环境下不加载分析代码
  if (process.env.NODE_ENV === 'development' && !ENABLE_ANALYTICS) {
    return null;
  }

  if (!CLARITY_PROJECT_ID) {
    return null;
  }

  return (
    <Script
      id="microsoft-clarity"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{
        __html: `
          (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
          })(window, document, "clarity", "script", "${CLARITY_PROJECT_ID}");
        `,
      }}
    />
  );
}

// Clarity自定义事件追踪函数
export const trackClarityEvent = (eventName: string, data?: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('event', eventName, data);
  }
};

// 音频播放事件追踪
export const trackClarityAudioPlay = (soundId: string, category: string, language: string) => {
  trackClarityEvent('audio_play', {
    sound_id: soundId,
    category: category,
    language: language,
    timestamp: new Date().toISOString()
  });
};

// 用户交互追踪
export const trackClarityUserInteraction = (action: string, element: string, context?: string) => {
  trackClarityEvent('user_interaction', {
    action: action,
    element: element,
    context: context,
    timestamp: new Date().toISOString()
  });
};

// 页面性能追踪
export const trackClarityPagePerformance = (pageName: string, loadTime: number) => {
  trackClarityEvent('page_performance', {
    page: pageName,
    load_time_ms: loadTime,
    timestamp: new Date().toISOString()
  });
};

// 错误追踪
export const trackClarityError = (error: string, context: string, stack?: string) => {
  trackClarityEvent('error', {
    error_message: error,
    context: context,
    stack_trace: stack,
    timestamp: new Date().toISOString()
  });
};

// 语言切换追踪
export const trackClarityLanguageSwitch = (fromLang: string, toLang: string) => {
  trackClarityEvent('language_switch', {
    from_language: fromLang,
    to_language: toLang,
    timestamp: new Date().toISOString()
  });
};
