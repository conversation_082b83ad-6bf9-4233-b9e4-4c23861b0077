'use client';

import { useState, useEffect } from 'react';
import { useLocale } from 'next-intl';

interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

interface PerformanceData {
  metrics: WebVitalMetric[];
  lastUpdated: number;
  pageUrl: string;
}

export function PerformanceDashboard() {
  const locale = useLocale();
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 只在开发环境或有特殊权限时显示
    const shouldShow = process.env.NODE_ENV === 'development' ||
                      localStorage.getItem('show-performance-dashboard') === 'true';
    setIsVisible(shouldShow);

    if (shouldShow) {
      // 监听性能数据
      const handlePerformanceData = (event: CustomEvent) => {
        const metric = event.detail;
        setPerformanceData(prev => ({
          metrics: prev ? [...prev.metrics.filter(m => m.name !== metric.name), metric] : [metric],
          lastUpdated: Date.now(),
          pageUrl: window.location.href
        }));
      };

      window.addEventListener('web-vital', handlePerformanceData as EventListener);

      return () => {
        window.removeEventListener('web-vital', handlePerformanceData as EventListener);
      };
    }

    // 如果不显示，返回空的清理函数
    return () => {};
  }, []);

  // 键盘快捷键切换显示
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'P') {
        const newVisibility = !isVisible;
        setIsVisible(newVisibility);
        localStorage.setItem('show-performance-dashboard', newVisibility.toString());
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible]);

  if (!isVisible || !performanceData) {
    return null;
  }

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'good': return 'text-green-600 bg-green-100';
      case 'needs-improvement': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatValue = (name: string, value: number) => {
    switch (name) {
      case 'CLS':
        return value.toFixed(3);
      case 'FID':
      case 'FCP':
      case 'LCP':
      case 'TTFB':
        return `${Math.round(value)}ms`;
      default:
        return Math.round(value).toString();
    }
  };

  const getMetricDescription = (name: string) => {
    const descriptions = {
      'CLS': locale === 'zh' ? '累积布局偏移' : 'Cumulative Layout Shift',
      'FID': locale === 'zh' ? '首次输入延迟' : 'First Input Delay',
      'FCP': locale === 'zh' ? '首次内容绘制' : 'First Contentful Paint',
      'LCP': locale === 'zh' ? '最大内容绘制' : 'Largest Contentful Paint',
      'TTFB': locale === 'zh' ? '首字节时间' : 'Time to First Byte'
    };
    return descriptions[name as keyof typeof descriptions] || name;
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          {locale === 'zh' ? '性能监控' : 'Performance Monitor'}
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          ×
        </button>
      </div>

      <div className="space-y-2">
        {performanceData.metrics.map((metric) => (
          <div key={metric.name} className="flex items-center justify-between">
            <div className="flex-1">
              <div className="text-xs font-medium text-gray-700 dark:text-gray-300">
                {getMetricDescription(metric.name)}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {metric.name}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                {formatValue(metric.name, metric.value)}
              </span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getRatingColor(metric.rating)}`}>
                {metric.rating === 'good' ? (locale === 'zh' ? '良好' : 'Good') :
                 metric.rating === 'needs-improvement' ? (locale === 'zh' ? '需改进' : 'Fair') :
                 (locale === 'zh' ? '差' : 'Poor')}
              </span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {locale === 'zh' ? '最后更新' : 'Last updated'}: {' '}
          {new Date(performanceData.lastUpdated).toLocaleTimeString()}
        </div>
        <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
          {locale === 'zh' ? '按 Ctrl+Shift+P 切换显示' : 'Press Ctrl+Shift+P to toggle'}
        </div>
      </div>
    </div>
  );
}

// 性能数据收集器
export function PerformanceCollector() {
  useEffect(() => {
    // 扩展Web Vitals收集器，发送自定义事件
    const originalSendToAnalytics = (window as any).sendToAnalytics;
    
    (window as any).sendToAnalytics = function(metric: any) {
      // 调用原始函数
      if (originalSendToAnalytics) {
        originalSendToAnalytics(metric);
      }

      // 发送自定义事件给仪表板
      const event = new CustomEvent('web-vital', {
        detail: {
          name: metric.name,
          value: metric.value,
          rating: metric.rating,
          timestamp: Date.now()
        }
      });
      window.dispatchEvent(event);
    };
  }, []);

  return null;
}
