'use client';

import { GoogleAnalytics } from './GoogleAnalytics';
import { MicrosoftClarity } from './MicrosoftClarity';
import { WebVitals } from './WebVitals';

const ENABLE_ANALYTICS = process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true';

interface AnalyticsProviderProps {
  children?: React.ReactNode;
}

export function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  // 在开发环境下显示分析状态
  if (process.env.NODE_ENV === 'development') {
    console.log('📊 分析服务状态:', {
      enabled: ENABLE_ANALYTICS,
      ga4: !!process.env.NEXT_PUBLIC_GA_TRACKING_ID,
      clarity: !!process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID,
      environment: process.env.NODE_ENV
    });
  }

  return (
    <>
      {/* Google Analytics 4 */}
      <GoogleAnalytics />
      
      {/* Microsoft Clarity */}
      <MicrosoftClarity />
      
      {/* Web Vitals 性能监控 */}
      <WebVitals />
      
      {children}
    </>
  );
}

// 统一的事件追踪接口
export const trackUnifiedEvent = (
  eventName: string, 
  category: string, 
  data?: Record<string, any>
) => {
  // Google Analytics 事件
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      event_category: category,
      ...data
    });
  }

  // Microsoft Clarity 事件
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('event', eventName, {
      category: category,
      ...data
    });
  }
};

// 音频播放统一追踪
export const trackAudioPlayUnified = (
  soundId: string, 
  category: string, 
  language: string
) => {
  const eventData = {
    sound_id: soundId,
    sound_category: category,
    language: language,
    timestamp: new Date().toISOString()
  };

  trackUnifiedEvent('audio_play', 'Audio', eventData);
  
  // 发送到所有分析服务
  if (typeof window !== 'undefined') {
    // Google Analytics
    if (window.gtag) {
      window.gtag('event', 'audio_play', {
        event_category: 'Audio',
        event_label: `${category}/${soundId}`,
        custom_parameters: {
          sound_id: soundId,
          sound_category: category,
          language: language
        }
      });
    }

    // Microsoft Clarity
    if (window.clarity) {
      window.clarity('event', 'audio_play', eventData);
    }
  }
};

// 页面浏览统一追踪
export const trackPageViewUnified = (
  pagePath: string, 
  pageTitle: string, 
  language: string
) => {
  const eventData = {
    page_path: pagePath,
    page_title: pageTitle,
    language: language,
    timestamp: new Date().toISOString()
  };

  // Google Analytics 页面浏览
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GA_TRACKING_ID!, {
      page_path: pagePath,
      page_title: pageTitle,
      custom_map: {
        custom_parameter_1: 'language'
      }
    });
  }

  // Microsoft Clarity 页面浏览
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('event', 'page_view', eventData);
  }
};

// 用户交互统一追踪
export const trackUserInteractionUnified = (
  action: string,
  element: string,
  context?: string
) => {
  const eventData = {
    action: action,
    element: element,
    context: context,
    timestamp: new Date().toISOString()
  };

  trackUnifiedEvent('user_interaction', 'Engagement', eventData);
};

// 错误统一追踪
export const trackErrorUnified = (
  error: string,
  context: string,
  fatal: boolean = false
) => {
  const eventData = {
    error_message: error,
    context: context,
    fatal: fatal,
    timestamp: new Date().toISOString()
  };

  // Google Analytics 异常追踪
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'exception', {
      description: error,
      fatal: fatal,
      custom_parameters: {
        context: context
      }
    });
  }

  // Microsoft Clarity 错误追踪
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('event', 'error', eventData);
  }
};
