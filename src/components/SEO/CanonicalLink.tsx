'use client';

import { useLocale } from 'next-intl';
import { usePathname } from 'next/navigation';

interface CanonicalLinkProps {
  href?: string;
}

export function CanonicalLink({ href }: CanonicalLinkProps) {
  const locale = useLocale();
  const pathname = usePathname();
  
  // 如果没有提供href，则根据当前路径生成
  const canonicalUrl = href || (() => {
    const baseUrl = 'https://noisesleep.com';
    
    // 移除locale前缀来获取基础路径
    const basePath = pathname.replace(/^\/[a-z]{2}/, '') || '/';
    
    // 对于英文，使用根路径；对于中文，使用/zh前缀
    if (locale === 'en') {
      return `${baseUrl}${basePath}`;
    } else {
      return `${baseUrl}/zh${basePath}`;
    }
  })();

  return (
    <link rel="canonical" href={canonicalUrl} />
  );
}
