'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useSleepTimer } from '@/hooks/useSleepTimer';
import { TimerPreset } from '@/types/audio';
import { TimerDisplay } from './TimerDisplay';

interface SleepTimerProps {
  className?: string;
  variant?: 'compact' | 'full';
  showDisplay?: boolean;
}

export function SleepTimer({ 
  className = '', 
  variant = 'full',
  showDisplay = true 
}: SleepTimerProps) {
  const t = useTranslations('timer');
  const [customMinutes, setCustomMinutes] = useState<string>('');
  const [showCustomInput, setShowCustomInput] = useState(false);

  const {
    isActive,
    startTimer,
    stopTimer,
    setPresetTimer,
    setCustomTimer,
  } = useSleepTimer();

  // 预设时长选项
  const presetOptions: TimerPreset[] = [15, 30, 60, 120];

  const handlePresetClick = (preset: TimerPreset) => {
    setPresetTimer(preset);
    setShowCustomInput(false);
  };

  const handleCustomSubmit = () => {
    const minutes = parseInt(customMinutes);
    if (minutes > 0 && minutes <= 480) { // 最大8小时
      setCustomTimer(minutes);
      setCustomMinutes('');
      setShowCustomInput(false);
    }
  };

  const handleStopTimer = () => {
    stopTimer();
    setShowCustomInput(false);
  };

  return (
    <div className={`sleep-timer ${className}`}>
      {/* 定时器显示 */}
      {showDisplay && isActive && (
        <div className="mb-6">
          <TimerDisplay variant={variant} />
        </div>
      )}

      {/* 定时器控制 */}
      <div className="space-y-4">
        {!isActive ? (
          <>
            {/* 预设时长按钮 */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                {t('presetDurations')}
              </h3>
              <div className="grid grid-cols-2 gap-3">
                {presetOptions.map((preset) => (
                  <button
                    key={preset}
                    onClick={() => handlePresetClick(preset)}
                    className="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-amber-300 dark:hover:border-amber-600 hover:bg-amber-50 dark:hover:bg-amber-900/10 transition-colors"
                  >
                    <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
                      {preset}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {preset === 60 ? t('hour') : t('minutes')}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* 自定义时长 */}
            <div>
              <button
                onClick={() => setShowCustomInput(!showCustomInput)}
                className="w-full flex items-center justify-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>{t('customDuration')}</span>
              </button>

              {showCustomInput && (
                <div className="mt-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <input
                      type="number"
                      min="1"
                      max="480"
                      value={customMinutes}
                      onChange={(e) => setCustomMinutes(e.target.value)}
                      placeholder="30"
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    />
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t('minutes')}
                    </span>
                    <button
                      onClick={handleCustomSubmit}
                      disabled={!customMinutes || parseInt(customMinutes) <= 0}
                      className="px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {t('start')}
                    </button>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    {t('customDurationHint')}
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          /* 定时器运行中的控制 */
          <div className="text-center space-y-4">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('timerActive')}
            </div>
            <button
              onClick={handleStopTimer}
              className="inline-flex items-center space-x-2 px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10h6v4H9z" />
              </svg>
              <span>{t('stopTimer')}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
