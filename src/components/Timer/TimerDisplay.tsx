'use client';

import { useTranslations } from 'next-intl';
import { useSleepTimer } from '@/hooks/useSleepTimer';

interface TimerDisplayProps {
  className?: string;
  variant?: 'compact' | 'full';
  showProgress?: boolean;
}

export function TimerDisplay({ 
  className = '', 
  variant = 'compact',
  showProgress = true 
}: TimerDisplayProps) {
  const t = useTranslations('timer');
  const {
    isActive,
    remainingTime,
    formatRemainingTime,
    progress,
    isNearEnd,
  } = useSleepTimer();

  if (!isActive) {
    return null;
  }

  const timeDisplay = formatRemainingTime(remainingTime);
  const progressPercentage = Math.round(progress * 100);

  return (
    <div className={`timer-display ${className}`}>
      {variant === 'full' ? (
        <div className="flex flex-col items-center space-y-3">
          {/* 时间显示 */}
          <div className={`text-center ${isNearEnd ? 'animate-pulse' : ''}`}>
            <div className={`font-mono font-bold ${
              variant === 'full' ? 'text-3xl' : 'text-lg'
            } ${isNearEnd ? 'text-red-500' : 'text-gray-900 dark:text-white'}`}>
              {timeDisplay}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {t('remaining')}
            </div>
          </div>

          {/* 进度条 */}
          {showProgress && (
            <div className="w-full max-w-xs">
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                <span>{t('progress')}</span>
                <span>{progressPercentage}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-1000 ${
                    isNearEnd ? 'bg-red-500' : 'bg-amber-500'
                  }`}
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>
          )}

          {/* 状态指示 */}
          {isNearEnd && (
            <div className="flex items-center space-x-2 text-red-500 text-sm">
              <svg className="w-4 h-4 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span>{t('fadeOutSoon')}</span>
            </div>
          )}
        </div>
      ) : (
        // Compact variant
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className={`font-mono text-sm font-medium ${
              isNearEnd ? 'text-red-500 animate-pulse' : 'text-gray-700 dark:text-gray-300'
            }`}>
              {timeDisplay}
            </span>
          </div>
          
          {showProgress && (
            <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div 
                className={`h-1 rounded-full transition-all duration-1000 ${
                  isNearEnd ? 'bg-red-500' : 'bg-amber-500'
                }`}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}
