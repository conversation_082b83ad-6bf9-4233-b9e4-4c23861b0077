'use client';

import { useTranslations } from 'next-intl';
import { useAudioStore } from '@/store/audioStore';
import { SleepTimer } from './SleepTimer';

interface TimerPanelProps {
  className?: string;
}

export function TimerPanel({ className = '' }: TimerPanelProps) {
  const t = useTranslations('timer');
  const { playerUI, setTimerPanelVisible } = useAudioStore();

  if (!playerUI.showTimerPanel) {
    return null;
  }

  return (
    <div className={`timer-panel ${className}`}>
      {/* 面板背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
        onClick={() => setTimerPanelVisible(false)}
      />
      
      {/* 面板内容 */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 rounded-t-2xl shadow-2xl transform transition-transform duration-300 ease-out">
        {/* 面板头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('sleepTimer')}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('sleepTimerDescription')}
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setTimerPanelVisible(false)}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 面板主体 */}
        <div className="p-6 max-h-96 overflow-y-auto">
          <SleepTimer variant="full" />
        </div>

        {/* 面板底部提示 */}
        <div className="px-6 pb-6">
          <div className="bg-amber-50 dark:bg-amber-900/10 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <svg className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="text-sm">
                <p className="text-amber-800 dark:text-amber-200 font-medium mb-1">
                  {t('timerTips')}
                </p>
                <ul className="text-amber-700 dark:text-amber-300 space-y-1">
                  <li>• {t('tip1')}</li>
                  <li>• {t('tip2')}</li>
                  <li>• {t('tip3')}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 拖拽指示器 */}
        <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
          <div className="w-8 h-1 bg-gray-300 dark:bg-gray-600 rounded-full" />
        </div>
      </div>
    </div>
  );
}
