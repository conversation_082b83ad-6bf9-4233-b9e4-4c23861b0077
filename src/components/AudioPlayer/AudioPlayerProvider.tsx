'use client';

import { useEffect } from 'react';
import { useAudioStore } from '@/store/audioStore';
import { StandardPlayer } from './StandardPlayer';
import { TimerPanel } from '../Timer/TimerPanel';
import { SleepModePlayer } from '../SleepMode/SleepModePlayer';
import { MixingPanel } from '../MixingBoard/MixingPanel';

interface AudioPlayerProviderProps {
  children: React.ReactNode;
  className?: string;
}

export function AudioPlayerProvider({ 
  children, 
  className 
}: AudioPlayerProviderProps) {
  const { 
    currentSound, 
    playerUI, 
    setPlayerVisible 
  } = useAudioStore();

  // 监听音频播放状态，自动显示/隐藏播放器
  useEffect(() => {
    console.log('🔍 AudioPlayerProvider useEffect:', {
      currentSound: currentSound?.title,
      isVisible: playerUI.isVisible,
      mode: playerUI.mode
    });

    if (currentSound && !playerUI.isVisible) {
      console.log('🚀 显示播放器');
      setPlayerVisible(true);
    }
  }, [currentSound, playerUI.isVisible, setPlayerVisible]);

  return (
    <div className={className}>
      {children}
      
      {/* 标准播放器 - 只在标准模式下显示 */}
      {playerUI.mode === 'standard' && (
        <StandardPlayer
          position={playerUI.position}
          showMixingButton={true}
          showSleepModeButton={true}
          autoHide={false}
        />
      )}
      
      {/* 睡眠模式播放器 */}
      {playerUI.mode === 'sleep' && (
        <SleepModePlayer />
      )}

      {/* 定时器面板 */}
      <TimerPanel />

      {/* 混音面板 */}
      <MixingPanel />
    </div>
  );
}
