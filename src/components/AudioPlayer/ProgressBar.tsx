'use client';

import { useState, useRef, useEffect } from 'react';
import { clsx } from 'clsx';

interface ProgressBarProps {
  currentTime: number;
  duration: number;
  onSeek: (time: number) => void;
  isLoading?: boolean;
  showTime?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
}

export function ProgressBar({
  currentTime,
  duration,
  onSeek,
  isLoading = false,
  showTime = true,
  size = 'md',
  className,
  disabled = false,
}: ProgressBarProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [dragTime, setDragTime] = useState(0);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipTime, setTooltipTime] = useState(0);
  const progressRef = useRef<HTMLDivElement>(null);

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    if (!isFinite(seconds) || seconds < 0) return '0:00';
    
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 计算进度百分比
  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;
  const displayTime = isDragging ? dragTime : currentTime;
  const displayProgress = isDragging ? (dragTime / duration) * 100 : progress;

  // 处理拖拽开始
  const handlePointerDown = (event: React.PointerEvent) => {
    if (disabled || duration === 0) return;
    
    setIsDragging(true);
    updateTimeFromEvent(event);
    event.preventDefault();
  };

  // 处理鼠标移动（用于显示预览时间）
  const handleMouseMove = (event: React.MouseEvent) => {
    if (!progressRef.current || duration === 0) return;
    
    const rect = progressRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, x / rect.width));
    const time = percentage * duration;
    
    setTooltipTime(time);
    setShowTooltip(true);
  };

  // 处理鼠标离开
  const handleMouseLeave = () => {
    if (!isDragging) {
      setShowTooltip(false);
    }
  };

  // 从事件更新时间
  const updateTimeFromEvent = (event: PointerEvent | React.PointerEvent) => {
    if (!progressRef.current || duration === 0) return;

    const rect = progressRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, x / rect.width));
    const time = percentage * duration;
    
    setDragTime(time);
    setTooltipTime(time);
  };

  // 处理拖拽移动
  const handlePointerMove = (event: PointerEvent) => {
    if (!isDragging || disabled) return;
    updateTimeFromEvent(event);
  };

  // 处理拖拽结束
  const handlePointerUp = () => {
    if (isDragging) {
      onSeek(dragTime);
      setIsDragging(false);
      setShowTooltip(false);
    }
  };

  // 处理点击跳转
  const handleClick = (event: React.MouseEvent) => {
    if (disabled || duration === 0 || isDragging) return;
    
    const rect = progressRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, x / rect.width));
    const time = percentage * duration;
    
    onSeek(time);
  };

  // 键盘控制
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (disabled || duration === 0) return;

    let newTime = currentTime;
    const step = duration * 0.05; // 5% 步长

    switch (event.key) {
      case 'ArrowLeft':
        newTime = Math.max(0, currentTime - step);
        break;
      case 'ArrowRight':
        newTime = Math.min(duration, currentTime + step);
        break;
      case 'Home':
        newTime = 0;
        break;
      case 'End':
        newTime = duration;
        break;
      default:
        return;
    }

    event.preventDefault();
    onSeek(newTime);
  };

  // 监听全局鼠标事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('pointermove', handlePointerMove);
      document.addEventListener('pointerup', handlePointerUp);

      return () => {
        document.removeEventListener('pointermove', handlePointerMove);
        document.removeEventListener('pointerup', handlePointerUp);
      };
    }
    return undefined;
  }, [isDragging, dragTime]);

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  const thumbSize = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  return (
    <div className={clsx('w-full', className)}>
      {/* 时间显示 */}
      {showTime && (
        <div className="flex justify-between items-center mb-2 text-sm text-gray-600 dark:text-gray-400">
          <span className="font-mono">
            {formatTime(displayTime)}
          </span>
          <span className="font-mono">
            {formatTime(duration)}
          </span>
        </div>
      )}

      {/* 进度条容器 */}
      <div className="relative">
        <div
          ref={progressRef}
          className={clsx(
            'relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer group',
            'focus:outline-none focus:ring-2 focus:ring-amber-500',
            sizeClasses[size],
            disabled && 'opacity-50 cursor-not-allowed'
          )}
          onPointerDown={handlePointerDown}
          onClick={handleClick}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          onKeyDown={handleKeyDown}
          tabIndex={disabled ? -1 : 0}
          role="slider"
          aria-valuemin={0}
          aria-valuemax={duration}
          aria-valuenow={currentTime}
          aria-label="音频进度"
        >
          {/* 加载状态 */}
          {isLoading && (
            <div className="absolute inset-0 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse" />
          )}

          {/* 已播放进度 */}
          <div
            className={clsx(
              'absolute left-0 top-0 bg-amber-500 rounded-full transition-all duration-150',
              sizeClasses[size]
            )}
            style={{ width: `${Math.max(0, Math.min(100, displayProgress))}%` }}
          />

          {/* 缓冲进度（如果需要的话） */}
          {/* 这里可以添加缓冲进度显示 */}

          {/* 拖拽手柄 */}
          {duration > 0 && (
            <div
              className={clsx(
                'absolute top-1/2 bg-white border-2 border-amber-500 rounded-full shadow-md',
                'transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150',
                'opacity-0 group-hover:opacity-100',
                isDragging && 'opacity-100 scale-125',
                thumbSize[size]
              )}
              style={{ left: `${Math.max(0, Math.min(100, displayProgress))}%` }}
            />
          )}
        </div>

        {/* 时间提示 */}
        {showTooltip && duration > 0 && (
          <div
            className="absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg transform -translate-x-1/2 -top-8"
            style={{ 
              left: `${Math.max(0, Math.min(100, (tooltipTime / duration) * 100))}%` 
            }}
          >
            {formatTime(tooltipTime)}
          </div>
        )}
      </div>

      {/* 进度百分比（可选） */}
      {duration > 0 && (
        <div className="mt-1 text-xs text-gray-500 dark:text-gray-500 text-center">
          {Math.round(displayProgress)}%
        </div>
      )}
    </div>
  );
}
