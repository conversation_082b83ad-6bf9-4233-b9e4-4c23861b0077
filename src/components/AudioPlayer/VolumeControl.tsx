'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { clsx } from 'clsx';

interface VolumeControlProps {
  volume: number;
  onVolumeChange: (volume: number) => void;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showValue?: boolean;
  className?: string;
  disabled?: boolean;
}

export function VolumeControl({
  volume,
  onVolumeChange,
  orientation = 'horizontal',
  size = 'md',
  showIcon = true,
  showValue = false,
  className,
  disabled = false,
}: VolumeControlProps) {
  const t = useTranslations('common');
  const [isDragging, setIsDragging] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);
  const [previousVolume, setPreviousVolume] = useState(volume);

  // 处理音量变化
  const handleVolumeChange = (newVolume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, newVolume));
    onVolumeChange(clampedVolume);
  };

  // 处理鼠标/触摸事件
  const handlePointerDown = (event: React.PointerEvent) => {
    if (disabled) return;
    
    setIsDragging(true);
    setShowTooltip(true);
    updateVolumeFromEvent(event);
    
    // 阻止默认行为
    event.preventDefault();
  };

  const handlePointerMove = (event: PointerEvent) => {
    if (!isDragging || disabled) return;
    updateVolumeFromEvent(event);
  };

  const handlePointerUp = () => {
    setIsDragging(false);
    setShowTooltip(false);
  };

  // 从事件更新音量
  const updateVolumeFromEvent = (event: PointerEvent | React.PointerEvent) => {
    if (!sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    let newVolume: number;

    if (orientation === 'horizontal') {
      const x = event.clientX - rect.left;
      newVolume = x / rect.width;
    } else {
      const y = event.clientY - rect.top;
      newVolume = 1 - (y / rect.height); // 垂直方向反转
    }

    handleVolumeChange(newVolume);
  };

  // 键盘控制
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (disabled) return;

    let newVolume = volume;
    const step = 0.1;

    switch (event.key) {
      case 'ArrowUp':
      case 'ArrowRight':
        newVolume = Math.min(1, volume + step);
        break;
      case 'ArrowDown':
      case 'ArrowLeft':
        newVolume = Math.max(0, volume - step);
        break;
      case 'Home':
        newVolume = 1;
        break;
      case 'End':
        newVolume = 0;
        break;
      default:
        return;
    }

    event.preventDefault();
    handleVolumeChange(newVolume);
  };

  // 静音切换
  const toggleMute = () => {
    if (disabled) return;
    
    if (volume > 0) {
      setPreviousVolume(volume);
      handleVolumeChange(0);
    } else {
      handleVolumeChange(previousVolume > 0 ? previousVolume : 0.7);
    }
  };

  // 监听全局鼠标事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('pointermove', handlePointerMove);
      document.addEventListener('pointerup', handlePointerUp);

      return () => {
        document.removeEventListener('pointermove', handlePointerMove);
        document.removeEventListener('pointerup', handlePointerUp);
      };
    }
    return undefined;
  }, [isDragging]);

  // 获取音量图标
  const getVolumeIcon = () => {
    if (volume === 0) {
      return (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
        </svg>
      );
    } else if (volume < 0.3) {
      return (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M7 9v6h4l5 5V4l-5 5H7z"/>
        </svg>
      );
    } else if (volume < 0.7) {
      return (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"/>
        </svg>
      );
    } else {
      return (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
        </svg>
      );
    }
  };

  const sizeClasses = {
    sm: orientation === 'horizontal' ? 'h-1' : 'w-1 h-16',
    md: orientation === 'horizontal' ? 'h-2' : 'w-2 h-20',
    lg: orientation === 'horizontal' ? 'h-3' : 'w-3 h-24',
  };

  const thumbSize = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  return (
    <div className={clsx(
      'flex items-center gap-2',
      orientation === 'vertical' && 'flex-col',
      className
    )}>
      {/* 音量图标 */}
      {showIcon && (
        <button
          onClick={toggleMute}
          disabled={disabled}
          className={clsx(
            'p-1 rounded-md transition-colors',
            'hover:bg-gray-100 dark:hover:bg-gray-800',
            'focus:outline-none focus:ring-2 focus:ring-amber-500',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            volume === 0 ? 'text-red-500' : 'text-gray-600 dark:text-gray-400'
          )}
          aria-label={volume === 0 ? '取消静音' : '静音'}
          title={volume === 0 ? '取消静音' : '静音'}
        >
          {getVolumeIcon()}
        </button>
      )}

      {/* 音量滑块 */}
      <div className="relative flex-1">
        <div
          ref={sliderRef}
          className={clsx(
            'relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer',
            'focus:outline-none focus:ring-2 focus:ring-amber-500',
            sizeClasses[size],
            disabled && 'opacity-50 cursor-not-allowed'
          )}
          onPointerDown={handlePointerDown}
          onKeyDown={handleKeyDown}
          tabIndex={disabled ? -1 : 0}
          role="slider"
          aria-valuemin={0}
          aria-valuemax={100}
          aria-valuenow={Math.round(volume * 100)}
          aria-label={t('volume')}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => !isDragging && setShowTooltip(false)}
        >
          {/* 进度条 */}
          <div
            className="absolute bg-amber-500 rounded-full transition-all duration-150"
            style={{
              [orientation === 'horizontal' ? 'width' : 'height']: `${volume * 100}%`,
              [orientation === 'horizontal' ? 'height' : 'width']: '100%',
              [orientation === 'vertical' ? 'bottom' : 'left']: 0,
            }}
          />

          {/* 滑块手柄 */}
          <div
            className={clsx(
              'absolute bg-white border-2 border-amber-500 rounded-full shadow-md',
              'transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150',
              'hover:scale-110',
              isDragging && 'scale-125',
              thumbSize[size]
            )}
            style={{
              [orientation === 'horizontal' ? 'left' : 'bottom']: `${volume * 100}%`,
              [orientation === 'horizontal' ? 'top' : 'left']: '50%',
            }}
          />
        </div>

        {/* 音量值提示 */}
        {(showTooltip || showValue) && (
          <div className={clsx(
            'absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg',
            'transform -translate-x-1/2',
            orientation === 'horizontal' ? '-top-8 left-1/2' : '-right-12 top-1/2 -translate-y-1/2'
          )}>
            {Math.round(volume * 100)}%
          </div>
        )}
      </div>

      {/* 数值显示 */}
      {showValue && (
        <span className="text-sm font-medium text-gray-600 dark:text-gray-400 min-w-[3rem] text-right">
          {Math.round(volume * 100)}%
        </span>
      )}
    </div>
  );
}
