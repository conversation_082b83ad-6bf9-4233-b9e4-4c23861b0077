'use client';

import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { clsx } from 'clsx';
import { MultilingualAudioItem, MixingChannel as MixingChannelType } from '@/types/audio';
import { VolumeControl } from '@/components/AudioPlayer/VolumeControl';
import { SmallPlayButton } from '@/components/AudioPlayer/PlayButton';

interface MixingChannelProps {
  channel: MixingChannelType;
  audio: MultilingualAudioItem;
  isPlaying: boolean;
  isLoading: boolean;
  onPlay: () => void;
  onPause: () => void;
  onStop: () => void;
  onVolumeChange: (volume: number) => void;
  onMute: () => void;
  onRemove: () => void;
  className?: string;
}

export function MixingChannel({
  channel,
  audio,
  isPlaying,
  isLoading,
  onPlay,
  onPause,
  onStop,
  onVolumeChange,
  onMute,
  onRemove,
  className,
}: MixingChannelProps) {
  const t = useTranslations('mixing');
  const locale = useLocale();
  const [showDetails, setShowDetails] = useState(false);

  // 获取本地化文本
  const getLocalizedText = (textObj: Record<string, string>) => {
    return textObj[locale] || textObj.en || Object.values(textObj)[0] || '';
  };

  const title = getLocalizedText(audio.title);

  // 获取音频图标
  const getAudioIcon = () => {
    const iconMap: Record<string, string> = {
      rain: '🌧️',
      nature: '🌿',
      noise: '🔊',
      animals: '🐾',
      things: '🏠',
      transport: '🚗',
      urban: '🏙️',
      places: '📍',
    };
    return iconMap[audio.category.toLowerCase()] || '🎵';
  };

  return (
    <div className={clsx(
      'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700',
      'shadow-sm hover:shadow-md transition-all duration-200',
      !channel.isActive && 'opacity-60',
      className
    )}>
      {/* 频道头部 */}
      <div className="p-4 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {/* 音频图标 */}
            <div className="w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-lg">{getAudioIcon()}</span>
            </div>

            {/* 音频信息 */}
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                {title}
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                {audio.category}
              </p>
            </div>

            {/* 播放状态指示器 */}
            {isPlaying && (
              <div className="flex items-center gap-1">
                <div className="w-1 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <div className="w-1 h-4 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-1 h-3 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-1 ml-2">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="p-1 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              aria-label={showDetails ? t('hideDetails') : t('showDetails')}
            >
              <svg className={clsx('w-4 h-4 transition-transform', showDetails && 'rotate-180')} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            <button
              onClick={onRemove}
              className="p-1 rounded-md text-gray-400 hover:text-red-500 transition-colors"
              aria-label={t('removeChannel')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* 控制面板 */}
      <div className="p-4">
        {/* 播放控制 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <SmallPlayButton
              isPlaying={isPlaying}
              isLoading={isLoading}
              onPlay={onPlay}
              onPause={onPause}
              variant="secondary"
            />
            
            <button
              onClick={onStop}
              disabled={!isPlaying}
              className={clsx(
                'p-1.5 rounded-md transition-colors',
                'hover:bg-gray-100 dark:hover:bg-gray-700',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'text-gray-600 dark:text-gray-400'
              )}
              aria-label={t('stop')}
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 6h12v12H6z"/>
              </svg>
            </button>
          </div>

          {/* 静音按钮 */}
          <button
            onClick={onMute}
            className={clsx(
              'p-1.5 rounded-md transition-colors',
              'hover:bg-gray-100 dark:hover:bg-gray-700',
              channel.isMuted ? 'text-red-500' : 'text-gray-600 dark:text-gray-400'
            )}
            aria-label={channel.isMuted ? t('unmute') : t('mute')}
          >
            {channel.isMuted ? (
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
              </svg>
            )}
          </button>
        </div>

        {/* 音量控制 */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('volume')}
            </label>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {Math.round(channel.volume * 100)}%
            </span>
          </div>
          <VolumeControl
            volume={channel.isMuted ? 0 : channel.volume}
            onVolumeChange={onVolumeChange}
            showIcon={false}
            size="sm"
            disabled={channel.isMuted}
          />
        </div>

        {/* 详细信息 */}
        {showDetails && (
          <div className="pt-4 border-t border-gray-100 dark:border-gray-700">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">{t('category')}:</span>
                <span className="text-gray-900 dark:text-gray-100">{audio.category}</span>
              </div>
              
              {audio.scientificRating && (
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">{t('rating')}:</span>
                  <span className="text-gray-900 dark:text-gray-100">
                    ⭐ {audio.scientificRating.toFixed(1)}
                  </span>
                </div>
              )}
              
              {audio.tags && audio.tags.length > 0 && (
                <div>
                  <span className="text-gray-500 dark:text-gray-400 block mb-1">{t('tags')}:</span>
                  <div className="flex flex-wrap gap-1">
                    {audio.tags.map((tag) => (
                      <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
