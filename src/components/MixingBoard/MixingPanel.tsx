'use client';

import { useState, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import { clsx } from 'clsx';
import { useAudioStore } from '@/store/audioStore';
import { MultilingualAudioItem } from '@/types/audio';
import { MixingChannel } from './MixingChannel';
import { VolumeControl } from '@/components/AudioPlayer/VolumeControl';
import { useMixingPlayer } from '@/hooks/useMixingPlayer';
import { audioData } from '@/data/audioData';

interface MixingPanelProps {
  className?: string;
}

export function MixingPanel({ className }: MixingPanelProps) {
  const t = useTranslations('mixing');
  const [showAudioSelector, setShowAudioSelector] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const {
    playerUI,
    mixingChannels,
    maxChannels,
    masterVolume,
    addMixingChannel,
    removeMixingChannel,
    updateChannelVolume,
    setMasterVolume,
    setMixingPanelVisible,
  } = useAudioStore();

  // 混音播放器功能
  const {
    playChannel,
    pauseChannel,
    stopChannel,
    setChannelVolume,
    muteChannel,
    unmuteChannel,
    getChannelState,
    stopAllChannels,
    setMasterVolume: setMixingMasterVolume,
  } = useMixingPlayer();

  // 获取可用的音频列表
  const availableAudios = audioData.filter(audio => 
    !mixingChannels.some(channel => channel.soundId === audio.id)
  );

  // 按分类过滤音频
  const filteredAudios = selectedCategory === 'all' 
    ? availableAudios 
    : availableAudios.filter(audio => audio.category === selectedCategory);

  // 获取所有分类
  const categories = Array.from(new Set(audioData.map(audio => audio.category))).sort();

  // 添加音频到混音板
  const handleAddAudio = useCallback((audio: MultilingualAudioItem) => {
    const success = addMixingChannel(audio);
    if (success) {
      setShowAudioSelector(false);
    }
  }, [addMixingChannel]);

  // 移除频道
  const handleRemoveChannel = useCallback((channelId: string) => {
    removeMixingChannel(channelId);
  }, [removeMixingChannel]);

  // 频道播放控制
  const handleChannelPlay = useCallback((channelId: string) => {
    playChannel(channelId);
  }, [playChannel]);

  const handleChannelPause = useCallback((channelId: string) => {
    pauseChannel(channelId);
  }, [pauseChannel]);

  const handleChannelStop = useCallback((channelId: string) => {
    stopChannel(channelId);
  }, [stopChannel]);

  const handleChannelVolumeChange = useCallback((channelId: string, volume: number) => {
    setChannelVolume(channelId, volume);
  }, [setChannelVolume]);

  const handleChannelMute = useCallback((channelId: string, muted: boolean) => {
    if (muted) {
      muteChannel(channelId);
    } else {
      unmuteChannel(channelId);
    }
  }, [muteChannel, unmuteChannel]);

  // 主音量控制
  const handleMasterVolumeChange = useCallback((volume: number) => {
    setMasterVolume(volume);
    setMixingMasterVolume(volume);
  }, [setMasterVolume, setMixingMasterVolume]);

  // 获取音频信息
  const getAudioById = (id: string): MultilingualAudioItem | undefined => {
    return audioData.find(audio => audio.id === id);
  };

  // 关闭面板
  const handleClose = () => {
    setMixingPanelVisible(false);
  };

  if (!playerUI.showMixingPanel) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className={clsx(
            'absolute bottom-0 left-0 right-0 max-h-[80vh] overflow-y-auto',
            'bg-white dark:bg-gray-900 rounded-t-xl shadow-2xl',
            'border-t border-gray-200 dark:border-gray-700',
            className
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 面板头部 */}
          <div className="sticky top-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t('title')}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t('description', { max: maxChannels })}
                </p>
              </div>
              <button
                onClick={handleClose}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* 面板内容 */}
          <div className="p-4 space-y-4">
            {/* 主音量控制 */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t('masterVolume')}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {Math.round(masterVolume * 100)}%
                </span>
              </div>
              <VolumeControl
                volume={masterVolume}
                onVolumeChange={handleMasterVolumeChange}
                size="sm"
              />
            </div>

            {/* 混音频道 */}
            {mixingChannels.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600">
                  <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                  </svg>
                </div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {t('noChannels')}
                </h4>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  {t('addFirstAudio')}
                </p>
                <button
                  onClick={() => setShowAudioSelector(true)}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-amber-500 text-white rounded-lg font-medium hover:bg-amber-600 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  {t('addAudio')}
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                {mixingChannels.map((channel) => {
                  const audio = getAudioById(channel.soundId);
                  if (!audio) return null;

                  const channelState = getChannelState(channel.id);

                  return (
                    <MixingChannel
                      key={channel.id}
                      channel={channel}
                      audio={audio}
                      isPlaying={channelState.isPlaying}
                      isLoading={channelState.isLoading}
                      onPlay={() => handleChannelPlay(channel.id)}
                      onPause={() => handleChannelPause(channel.id)}
                      onStop={() => handleChannelStop(channel.id)}
                      onVolumeChange={(volume) => handleChannelVolumeChange(channel.id, volume)}
                      onMute={() => handleChannelMute(channel.id, !channel.isMuted)}
                      onRemove={() => handleRemoveChannel(channel.id)}
                    />
                  );
                })}
                
                {/* 添加更多频道按钮 */}
                {mixingChannels.length < maxChannels && (
                  <button
                    onClick={() => setShowAudioSelector(true)}
                    className="w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-amber-400 hover:text-amber-600 transition-colors"
                  >
                    <div className="flex items-center justify-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      {t('addAudio')}
                    </div>
                  </button>
                )}
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
