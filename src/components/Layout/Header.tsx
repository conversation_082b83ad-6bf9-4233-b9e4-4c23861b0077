'use client';

import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import LanguageSelector from '@/components/LanguageSelector';

interface HeaderProps {
  variant?: 'default' | 'landing';
  showGetStarted?: boolean;
}

export default function Header({ variant = 'default', showGetStarted = true }: HeaderProps) {
  const t = useTranslations('landing');
  const locale = useLocale();

  return (
    <header className="bg-white/80 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm">🎵</span>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">
              {t('siteName')}
            </h1>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-8">
            {variant === 'landing' ? (
              <>
                <a
                  href="#sounds"
                  className="text-gray-600 hover:text-indigo-600 font-medium transition-colors"
                >
                  {t('nav.sounds')}
                </a>
                <a
                  href="#features"
                  className="text-gray-600 hover:text-indigo-600 font-medium transition-colors"
                >
                  {t('nav.features')}
                </a>
                <a
                  href="#blog"
                  className="text-gray-600 hover:text-indigo-600 font-medium transition-colors"
                >
                  {t('nav.blog')}
                </a>
              </>
            ) : (
              <>
                <Link
                  href={`/${locale}/sounds`}
                  className="text-gray-600 hover:text-indigo-600 font-medium transition-colors"
                >
                  {t('nav.sounds')}
                </Link>
                <Link
                  href={`/${locale}/mix`}
                  className="text-gray-600 hover:text-indigo-600 font-medium transition-colors"
                >
                  {locale === 'zh' ? '混音器' : 'Mixer'}
                </Link>
                <Link
                  href={`/${locale}/favorites`}
                  className="text-gray-600 hover:text-indigo-600 font-medium transition-colors"
                >
                  {locale === 'zh' ? '收藏' : 'Favorites'}
                </Link>
              </>
            )}
          </nav>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            <LanguageSelector />
            {showGetStarted && (
              <Link
                href={`/${locale}/sounds`}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                {t('nav.getStarted')}
              </Link>
            )}
            <button className="md:hidden">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
