'use client';

import { ReactNode } from 'react';
import Header from './Header';
import Footer from './Footer';

interface PageLayoutProps {
  children: ReactNode;
  headerVariant?: 'default' | 'landing';
  showGetStarted?: boolean;
  showFooter?: boolean;
  className?: string;
}

export default function PageLayout({ 
  children, 
  headerVariant = 'default',
  showGetStarted = true,
  showFooter = true,
  className = ''
}: PageLayoutProps) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 ${className}`}>
      <Header variant={headerVariant} showGetStarted={showGetStarted} />
      <main className="flex-1">
        {children}
      </main>
      {showFooter && <Footer />}
    </div>
  );
}
