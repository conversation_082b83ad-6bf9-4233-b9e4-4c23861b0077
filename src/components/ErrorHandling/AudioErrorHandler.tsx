'use client';

import React, { useState, useCallback } from 'react';
import { useLocale } from 'next-intl';
import { getAudioErrorMessage } from '@/utils/i18nHelpers';

interface AudioErrorHandlerProps {
  children: React.ReactNode;
  audioId?: string;
  onError?: (error: Error) => void;
  fallback?: React.ReactNode;
}

interface AudioErrorState {
  hasError: boolean;
  error?: Error;
  retryCount: number;
}

/**
 * 音频错误处理组件
 * 专门处理音频加载和播放相关的错误
 */
export function AudioErrorHandler({ 
  children, 
  audioId = 'unknown', 
  onError,
  fallback 
}: AudioErrorHandlerProps) {
  const locale = useLocale();
  const [errorState, setErrorState] = useState<AudioErrorState>({
    hasError: false,
    retryCount: 0
  });

  const handleError = useCallback((error: Error) => {
    console.error(`Audio error for ${audioId}:`, error);
    
    setErrorState(prev => ({
      hasError: true,
      error,
      retryCount: prev.retryCount + 1
    }));

    if (onError) {
      onError(error);
    }
  }, [audioId, onError]);

  const handleRetry = useCallback(() => {
    if (errorState.retryCount < 3) {
      setErrorState(prev => ({
        hasError: false,
        error: undefined,
        retryCount: prev.retryCount
      }));
    }
  }, [errorState.retryCount]);

  const resetError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: undefined,
      retryCount: 0
    });
  }, []);

  // 提供错误处理上下文给子组件
  const contextValue = React.useMemo(() => ({
    handleError,
    resetError,
    hasError: errorState.hasError,
    error: errorState.error,
    retryCount: errorState.retryCount
  }), [handleError, resetError, errorState]);

  if (errorState.hasError && errorState.error) {
    // 如果提供了自定义回退组件
    if (fallback) {
      return <>{fallback}</>;
    }

    // 默认错误界面
    const errorMessage = getAudioErrorMessage(errorState.error, audioId, locale);
    const canRetry = errorState.retryCount < 3;

    return (
      <div className="flex flex-col items-center justify-center p-6 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
        {/* 错误图标 */}
        <div className="w-12 h-12 text-red-500 dark:text-red-400 mb-4">
          <svg fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>

        {/* 错误消息 */}
        <p className="text-red-700 dark:text-red-300 text-center mb-4 text-sm">
          {errorMessage}
        </p>

        {/* 操作按钮 */}
        <div className="flex space-x-3">
          {canRetry && (
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-red-500 text-white rounded-md text-sm font-medium hover:bg-red-600 transition-colors"
            >
              {locale === 'zh' ? '重试' : 'Retry'}
            </button>
          )}
          
          <button
            onClick={resetError}
            className="px-4 py-2 bg-gray-500 text-white rounded-md text-sm font-medium hover:bg-gray-600 transition-colors"
          >
            {locale === 'zh' ? '关闭' : 'Close'}
          </button>
        </div>

        {/* 重试次数提示 */}
        {errorState.retryCount > 0 && (
          <p className="text-xs text-red-600 dark:text-red-400 mt-2">
            {locale === 'zh' 
              ? `重试次数: ${errorState.retryCount}/3`
              : `Retry attempts: ${errorState.retryCount}/3`
            }
          </p>
        )}
      </div>
    );
  }

  return (
    <AudioErrorContext.Provider value={contextValue}>
      {children}
    </AudioErrorContext.Provider>
  );
}

/**
 * 音频错误上下文
 */
const AudioErrorContext = React.createContext<{
  handleError: (error: Error) => void;
  resetError: () => void;
  hasError: boolean;
  error?: Error;
  retryCount: number;
} | null>(null);

/**
 * 使用音频错误处理的Hook
 */
export function useAudioError() {
  const context = React.useContext(AudioErrorContext);
  
  if (!context) {
    throw new Error('useAudioError must be used within AudioErrorHandler');
  }
  
  return context;
}

/**
 * 音频加载错误处理Hook
 */
export function useAudioLoadError(audioId: string) {
  const locale = useLocale();
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const handleLoadError = useCallback((error: Error) => {
    console.error(`Audio load error for ${audioId}:`, error);
    setError(error);
    setIsLoading(false);
  }, [audioId]);

  const retry = useCallback(() => {
    if (retryCount < 3) {
      setError(null);
      setIsLoading(true);
      setRetryCount(prev => prev + 1);
    }
  }, [retryCount]);

  const reset = useCallback(() => {
    setError(null);
    setIsLoading(false);
    setRetryCount(0);
  }, []);

  const getErrorMessage = useCallback(() => {
    if (!error) return '';
    return getAudioErrorMessage(error, audioId, locale);
  }, [error, audioId, locale]);

  return {
    error,
    isLoading,
    retryCount,
    canRetry: retryCount < 3,
    handleLoadError,
    retry,
    reset,
    getErrorMessage
  };
}

export default AudioErrorHandler;
