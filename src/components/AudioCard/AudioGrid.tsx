'use client';

import { useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { clsx } from 'clsx';
import { MultilingualAudioItem } from '@/types/audio';
import { AudioCard } from './AudioCard';

interface AudioGridProps {
  audios: MultilingualAudioItem[];
  variant?: 'default' | 'compact' | 'detailed';
  columns?: 1 | 2 | 3 | 4 | 'auto';
  showSearch?: boolean;
  showFilter?: boolean;
  showSort?: boolean;
  onAudioPlay?: (audio: MultilingualAudioItem) => void;
  className?: string;
  emptyMessage?: string;
  loading?: boolean;
}

type SortOption = 'name' | 'category' | 'rating' | 'recent';
type FilterOption = 'all' | string; // 'all' or category name

export function AudioGrid({
  audios,
  variant = 'default',
  columns = 'auto',
  showSearch = true,
  showFilter = true,
  showSort = true,
  onAudioPlay,
  className,
  emptyMessage,
  loading = false,
}: AudioGridProps) {
  const t = useTranslations('common');
  
  // 搜索和过滤状态
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<FilterOption>('all');
  const [sortBy, setSortBy] = useState<SortOption>('name');

  // 获取所有分类
  const categories = useMemo(() => {
    const cats = Array.from(new Set(audios.map(audio => audio.category)));
    return cats.sort();
  }, [audios]);

  // 过滤和排序音频
  const filteredAndSortedAudios = useMemo(() => {
    let filtered = audios;

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(audio => 
        Object.values(audio.title).some(title => 
          title.toLowerCase().includes(query)
        ) ||
        (audio.description && Object.values(audio.description).some(desc => 
          desc.toLowerCase().includes(query)
        )) ||
        audio.category.toLowerCase().includes(query) ||
        audio.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // 分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(audio => audio.category === selectedCategory);
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return (a.title.en || a.title.zh || '').localeCompare(b.title.en || b.title.zh || '');
        case 'category':
          return a.category.localeCompare(b.category);
        case 'rating':
          return (b.scientificRating || 0) - (a.scientificRating || 0);
        case 'recent':
          // 这里可以根据实际的最近播放逻辑来排序
          return a.id.localeCompare(b.id);
        default:
          return 0;
      }
    });

    return filtered;
  }, [audios, searchQuery, selectedCategory, sortBy]);

  // 获取网格列数类名
  const getGridColumns = () => {
    if (columns === 'auto') {
      switch (variant) {
        case 'compact':
          return 'grid-cols-1';
        case 'detailed':
          return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
        default:
          return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
      }
    }
    
    const columnMap = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 sm:grid-cols-2',
      3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    };
    
    return columnMap[columns];
  };

  // 加载状态
  if (loading) {
    return (
      <div className={clsx('space-y-4', className)}>
        {/* 搜索和过滤器骨架 */}
        {(showSearch || showFilter || showSort) && (
          <div className="flex flex-col sm:flex-row gap-4">
            {showSearch && (
              <div className="flex-1">
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
              </div>
            )}
            {showFilter && (
              <div className="w-full sm:w-48">
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
              </div>
            )}
            {showSort && (
              <div className="w-full sm:w-48">
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
              </div>
            )}
          </div>
        )}

        {/* 网格骨架 */}
        <div className={clsx('grid gap-4', getGridColumns())}>
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse">
              <div className="h-48" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('space-y-6', className)}>
      {/* 搜索和过滤控件 */}
      {(showSearch || showFilter || showSort) && (
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 搜索框 */}
          {showSearch && (
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder={t('searchAudios')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={clsx(
                    'block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600',
                    'rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
                    'placeholder-gray-500 dark:placeholder-gray-400',
                    'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500',
                    'transition-colors duration-200'
                  )}
                />
              </div>
            </div>
          )}

          {/* 分类过滤 */}
          {showFilter && (
            <div className="w-full sm:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as FilterOption)}
                className={clsx(
                  'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600',
                  'rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
                  'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500',
                  'transition-colors duration-200'
                )}
              >
                <option value="all">{t('allCategories')}</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* 排序选择 */}
          {showSort && (
            <div className="w-full sm:w-48">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className={clsx(
                  'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600',
                  'rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
                  'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500',
                  'transition-colors duration-200'
                )}
              >
                <option value="name">{t('sortByName')}</option>
                <option value="category">{t('sortByCategory')}</option>
                <option value="rating">{t('sortByRating')}</option>
                <option value="recent">{t('sortByRecent')}</option>
              </select>
            </div>
          )}
        </div>
      )}

      {/* 结果统计 */}
      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
        <span>
          {t('showingResults', { 
            count: filteredAndSortedAudios.length, 
            total: audios.length 
          })}
        </span>
        {searchQuery && (
          <button
            onClick={() => setSearchQuery('')}
            className="text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300"
          >
            {t('clearSearch')}
          </button>
        )}
      </div>

      {/* 音频网格 */}
      {filteredAndSortedAudios.length > 0 ? (
        <div className={clsx(
          'grid gap-4',
          getGridColumns()
        )}>
          {filteredAndSortedAudios.map((audio) => (
            <AudioCard
              key={audio.id}
              audio={audio}
              variant={variant}
              showCategory={selectedCategory === 'all'}
              showTags={true}
              showDuration={variant === 'detailed'}
              showDescription={variant === 'detailed'}
              onPlay={onAudioPlay}
            />
          ))}
        </div>
      ) : (
        /* 空状态 */
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600">
            <svg fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            {emptyMessage || t('noAudiosFound')}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {searchQuery ? t('tryDifferentSearch') : t('noAudiosAvailable')}
          </p>
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className={clsx(
                'inline-flex items-center px-4 py-2 border border-transparent',
                'text-sm font-medium rounded-md text-white bg-amber-600',
                'hover:bg-amber-700 focus:outline-none focus:ring-2',
                'focus:ring-offset-2 focus:ring-amber-500 transition-colors'
              )}
            >
              {t('clearSearch')}
            </button>
          )}
        </div>
      )}
    </div>
  );
}
