/**
 * 国际化辅助工具
 * 提供翻译键缺失时的优雅降级机制
 */

import { useTranslations } from 'next-intl';
import { useLocale } from 'next-intl';

/**
 * 安全的翻译函数，提供回退机制
 * @param t - next-intl 翻译函数
 * @param key - 翻译键
 * @param fallback - 回退文本
 * @param params - 翻译参数
 * @returns 翻译文本或回退文本
 */
export function safeTranslate(
  t: ReturnType<typeof useTranslations>,
  key: string,
  fallback?: string,
  params?: Record<string, any>
): string {
  try {
    const result = t(key, params);
    
    // 检查是否返回了缺失消息标识
    if (result.includes('MISSING_MESSAGE') || result === key) {
      console.warn(`Translation missing for key: ${key}`);
      return fallback || key;
    }
    
    return result;
  } catch (error) {
    console.error(`Translation error for key: ${key}`, error);
    return fallback || key;
  }
}

/**
 * 获取本地化文本的安全方法
 * @param textObj - 包含多语言文本的对象
 * @param locale - 当前语言环境
 * @param fallback - 回退文本
 * @returns 本地化文本
 */
export function getLocalizedText(
  textObj: Record<string, string> | string,
  locale: string,
  fallback?: string
): string {
  // 如果是字符串，直接返回
  if (typeof textObj === 'string') {
    return textObj;
  }

  // 如果是对象，尝试获取对应语言的文本
  if (textObj && typeof textObj === 'object') {
    // 优先使用当前语言
    if (textObj[locale]) {
      return textObj[locale];
    }
    
    // 回退到英文
    if (textObj.en) {
      return textObj.en;
    }
    
    // 回退到第一个可用的值
    const firstValue = Object.values(textObj)[0];
    if (firstValue) {
      return firstValue;
    }
  }

  // 最终回退
  return fallback || 'Text not available';
}

/**
 * 语言切换错误处理
 * @param error - 错误对象
 * @param locale - 目标语言
 * @returns 错误处理结果
 */
export function handleLanguageSwitchError(error: Error, locale: string) {
  console.error(`Language switch to ${locale} failed:`, error);
  
  // 可以在这里添加用户通知逻辑
  // 例如显示 toast 消息或回退到默认语言
  
  return {
    success: false,
    error: error.message,
    fallbackLocale: 'en'
  };
}

/**
 * 音频加载错误处理
 * @param error - 错误对象
 * @param audioId - 音频ID
 * @param locale - 当前语言
 * @returns 本地化的错误消息
 */
export function getAudioErrorMessage(error: Error, audioId: string, locale: string): string {
  const errorMessages = {
    en: {
      networkError: 'Network connection failed. Please check your internet connection.',
      audioNotFound: 'Audio file not found. Please try another sound.',
      browserNotSupported: 'Your browser does not support this audio format.',
      audioContextFailed: 'Failed to initialize audio system. Please refresh the page.',
      unknownError: 'An unexpected error occurred. Please try again.'
    },
    zh: {
      networkError: '网络连接失败，请检查您的网络连接。',
      audioNotFound: '音频文件未找到，请尝试其他音频。',
      browserNotSupported: '您的浏览器不支持此音频格式。',
      audioContextFailed: '音频系统初始化失败，请刷新页面。',
      unknownError: '发生未知错误，请重试。'
    }
  };

  const messages = errorMessages[locale as keyof typeof errorMessages] || errorMessages.en;
  
  // 根据错误类型返回相应消息
  if (error.message.includes('network') || error.message.includes('fetch')) {
    return messages.networkError;
  }
  
  if (error.message.includes('404') || error.message.includes('not found')) {
    return messages.audioNotFound;
  }
  
  if (error.message.includes('browser') || error.message.includes('support')) {
    return messages.browserNotSupported;
  }
  
  if (error.message.includes('AudioContext') || error.message.includes('audio context')) {
    return messages.audioContextFailed;
  }
  
  return messages.unknownError;
}

/**
 * 创建错误边界的错误处理函数
 * @param error - 错误对象
 * @param errorInfo - 错误信息
 * @param locale - 当前语言
 */
export function logErrorBoundaryError(
  error: Error,
  errorInfo: { componentStack: string },
  locale: string
) {
  console.error('Error Boundary caught an error:', {
    error: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    locale,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'SSR'
  });

  // 在生产环境中，可以将错误发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    // 例如发送到 Sentry、LogRocket 等服务
    // sendErrorToMonitoring(error, errorInfo, locale);
  }
}

/**
 * 验证翻译键是否存在
 * @param t - 翻译函数
 * @param key - 翻译键
 * @returns 是否存在
 */
export function hasTranslationKey(
  t: ReturnType<typeof useTranslations>,
  key: string
): boolean {
  try {
    const result = t(key);
    return !result.includes('MISSING_MESSAGE') && result !== key;
  } catch {
    return false;
  }
}

/**
 * 获取错误页面的本地化内容
 * @param locale - 当前语言
 * @param errorType - 错误类型
 * @returns 本地化的错误页面内容
 */
export function getErrorPageContent(locale: string, errorType: '404' | '500' | 'network') {
  const content = {
    en: {
      '404': {
        title: 'Page Not Found',
        message: 'The page you are looking for does not exist.',
        action: 'Go to Homepage'
      },
      '500': {
        title: 'Server Error',
        message: 'Something went wrong on our end. Please try again later.',
        action: 'Refresh Page'
      },
      'network': {
        title: 'Network Error',
        message: 'Unable to connect to the server. Please check your internet connection.',
        action: 'Try Again'
      }
    },
    zh: {
      '404': {
        title: '页面未找到',
        message: '您访问的页面不存在。',
        action: '返回首页'
      },
      '500': {
        title: '服务器错误',
        message: '服务器出现问题，请稍后重试。',
        action: '刷新页面'
      },
      'network': {
        title: '网络错误',
        message: '无法连接到服务器，请检查您的网络连接。',
        action: '重试'
      }
    }
  };

  return content[locale as keyof typeof content]?.[errorType] || content.en[errorType];
}
