🧠 智能睡眠音频评估与推荐系统 - 分析报告
================================================================================
📊 分析文件数量: 3

📈 汇总统计
----------------------------------------
🎵 噪音类型分布:
   • 粉噪音: 1 个文件
   • 白噪音: 1 个文件
   • 棕噪音: 1 个文件
🛡️ 安全等级分布:
   • 需要注意: 2 个文件
   • 安全: 1 个文件
📊 睡眠适用性得分: 平均 72.5, 最高 89.7, 最低 61.0

🏆 推荐排序 (按睡眠适用性得分)
----------------------------------------
 1. white-noise.wav
    📊 得分: 89.7/100 | 🎵 类型: 白噪音 | 🛡️ 安全: 安全

 2. pink-noise.wav
    📊 得分: 66.9/100 | 🎵 类型: 粉噪音 | 🛡️ 安全: 需要注意

 3. brown-noise.wav
    📊 得分: 61.0/100 | 🎵 类型: 棕噪音 | 🛡️ 安全: 需要注意

📋 详细分析报告
================================================================================
================================================================================
🧠 智能睡眠音频评估与推荐报告
================================================================================
📁 音频文件: Sounds/Noise/white-noise.wav
⏰ 分析时间: 2025-06-25T11:23:39.707982
🎯 总体推荐: ✅ 强烈推荐：科学证据支持，安全性良好

📊 音频特征分析
----------------------------------------
🎵 噪音类型: 白噪音
🔊 音频来源: 混合声音
🏷️  声音标签: High Frequency Content
📈 频谱斜率: -0.001
📊 响度稳定性: 0.029
🎼 音调峰值比: 1.32
📏 动态范围: 3.7 dB
⏱️  音频时长: 9.5 秒

🛡️ 安全评估
----------------------------------------
🔒 总体安全: 安全
🔊 音量安全: 安全
⏰ 时长安全: 安全
📋 内容安全: 安全
📊 安全得分: 100.0/100
🎚️  推荐音量: 45-60 dB
📏 推荐距离: ≥50 cm

😴 睡眠适用性评估
----------------------------------------
📊 总体得分: 89.7/100
🎯 效果预测: 29.6%
⚠️  干扰风险: 0.0%
😌 舒适度: 89.7%
🔬 科学证据: 有限证据支持 (33%研究有效)

👥 个性化推荐
----------------------------------------
👤 成人
   📊 适用性得分: 89.7/100
   💡 使用建议: 可以使用：注意控制音量在50-60dB，避免整夜高音量播放
   ⚙️  最优设置:
      • volume_db: 60
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: bedtime_routine
   ✅ 潜在益处: 改善入睡时间, 减少夜间觉醒, 屏蔽环境噪声
   ⚠️  注意风险: 长期使用可能产生依赖
   🔄 替代建议: 尝试粉噪音（科学证据最强）, 选择自然声音（如雨声、海浪声）
   🔬 科学依据: 白噪音仅在33%的研究中显示有效，效果有限

👤 婴幼儿
   📊 适用性得分: 62.8/100
   💡 使用建议: 谨慎使用：音量≤50dB，距离≥2米，仅在入睡阶段使用，入睡后关闭
   ⚙️  最优设置:
      • volume_db: 50
      • distance_cm: 200
      • duration_hours: 1.0
      • timing: sleep_onset_only
   ✅ 潜在益处: 辅助快速入睡, 创造一致的睡眠环境
   ⚠️  注意风险: 需严格控制音量和距离, 避免长时间连续使用
   🔄 替代建议: 尝试粉噪音（科学证据最强）, 选择自然声音（如雨声、海浪声）
   🔬 科学依据: 白噪音仅在33%的研究中显示有效，效果有限；研究显示80%的新生儿在5分钟内被白噪音哄睡，但需严格控制安全参数

👤 老年人
   📊 适用性得分: 89.7/100
   💡 使用建议: 可以尝试：虽非最佳选择，但可作为环境噪声屏蔽
   ⚙️  最优设置:
      • volume_db: 55
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: full_night_if_needed
   ✅ 潜在益处: 增强深度睡眠, 改善记忆巩固, 减少环境干扰
   ⚠️  注意风险: 避免过度依赖, 注意听力保护
   🔄 替代建议: 尝试粉噪音（科学证据最强）, 选择自然声音（如雨声、海浪声）
   🔬 科学依据: 白噪音仅在33%的研究中显示有效，效果有限；粉噪音可增强老年人深度睡眠的慢波活动，改善记忆表现

👤 失眠患者
   📊 适用性得分: 98.7/100
   💡 使用建议: 可以尝试：作为环境噪声屏蔽，但需配合其他治疗方法
   ⚙️  最优设置:
      • volume_db: 60
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: bedtime_routine
   ✅ 潜在益处: 辅助放松, 屏蔽干扰, 建立睡眠仪式感
   ⚠️  注意风险: 仅为辅助手段, 需配合其他治疗方法
   🔄 替代建议: 尝试粉噪音（科学证据最强）, 选择自然声音（如雨声、海浪声）
   🔬 科学依据: 白噪音仅在33%的研究中显示有效，效果有限；白噪音可使失眠患者入睡时间缩短38%，但仅作为辅助治疗

📚 科学参考文献
----------------------------------------
1. 粉噪音82%有效率 vs 白噪音33%有效率 - 多项睡眠研究荟萃分析
2. 婴幼儿白噪音安全标准：音量≤50dB，距离≥2米 - 儿科睡眠医学研究
3. 老年人粉噪音深度睡眠改善 - 神经科学睡眠研究
4. 失眠患者入睡时间缩短38% - 临床睡眠医学试验
5. 绿噪音中频集中特性与自然环境音模拟 - 心理声学理论分析
6. 自然声音偏好与低频丰富特性 - 心理声学研究

================================================================================

================================================================================
🧠 智能睡眠音频评估与推荐报告
================================================================================
📁 音频文件: Sounds/Noise/pink-noise.wav
⏰ 分析时间: 2025-06-25T11:23:38.091950
🎯 总体推荐: ⚠️ 可以使用：有一定效果，注意使用方法

📊 音频特征分析
----------------------------------------
🎵 噪音类型: 粉噪音
🔊 音频来源: 混合声音
🏷️  声音标签: High Frequency Content
📈 频谱斜率: -1.084
📊 响度稳定性: 0.104
🎼 音调峰值比: 1207.29
📏 动态范围: 7.3 dB
⏱️  音频时长: 9.5 秒

🛡️ 安全评估
----------------------------------------
🔒 总体安全: 需要注意
🔊 音量安全: 安全
⏰ 时长安全: 安全
📋 内容安全: 需要注意
📊 安全得分: 90.0/100
🎚️  推荐音量: 45-60 dB
📏 推荐距离: ≥50 cm
⚠️  安全警告:
   • 包含明显音调成分，可能影响睡眠

😴 睡眠适用性评估
----------------------------------------
📊 总体得分: 66.9/100
🎯 效果预测: 54.8%
⚠️  干扰风险: 30.0%
😌 舒适度: 66.9%
🔬 科学证据: 强证据支持 (82%研究有效)

👥 个性化推荐
----------------------------------------
👤 成人
   📊 适用性得分: 80.2/100
   💡 使用建议: 可以使用：注意控制音量在50-60dB，避免整夜高音量播放
   ⚙️  最优设置:
      • volume_db: 60
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: bedtime_routine
   ✅ 潜在益处: 改善入睡时间, 减少夜间觉醒, 屏蔽环境噪声
   ⚠️  注意风险: 长期使用可能产生依赖
   🔄 替代建议: 选择自然声音（如雨声、海浪声）
   🔬 科学依据: 粉噪音在82%的科学研究中显示有效，明显优于白噪音的33%

👤 婴幼儿
   📊 适用性得分: 46.8/100
   💡 使用建议: 不推荐使用：不符合婴幼儿安全标准
   ⚙️  最优设置:
      • volume_db: 50
      • distance_cm: 200
      • duration_hours: 1.0
      • timing: sleep_onset_only
   ✅ 潜在益处: 辅助快速入睡, 创造一致的睡眠环境
   ⚠️  注意风险: 需严格控制音量和距离, 避免长时间连续使用
   🔄 替代建议: 选择自然声音（如雨声、海浪声）
   🔬 科学依据: 粉噪音在82%的科学研究中显示有效，明显优于白噪音的33%；研究显示80%的新生儿在5分钟内被白噪音哄睡，但需严格控制安全参数

👤 老年人
   📊 适用性得分: 86.9/100
   💡 使用建议: 推荐使用：低频丰富的声音有助于深度睡眠，建议音量45-55dB
   ⚙️  最优设置:
      • volume_db: 55
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: full_night_if_needed
   ✅ 潜在益处: 增强深度睡眠, 改善记忆巩固, 减少环境干扰
   ⚠️  注意风险: 避免过度依赖, 注意听力保护
   🔄 替代建议: 选择自然声音（如雨声、海浪声）
   🔬 科学依据: 粉噪音在82%的科学研究中显示有效，明显优于白噪音的33%；粉噪音可增强老年人深度睡眠的慢波活动，改善记忆表现

👤 失眠患者
   📊 适用性得分: 66.9/100
   💡 使用建议: 不推荐使用：建议咨询专业医生选择更合适的治疗方案
   ⚙️  最优设置:
      • volume_db: 60
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: bedtime_routine
   ✅ 潜在益处: 辅助放松, 屏蔽干扰, 建立睡眠仪式感
   ⚠️  注意风险: 仅为辅助手段, 需配合其他治疗方法
   🔄 替代建议: 选择自然声音（如雨声、海浪声）
   🔬 科学依据: 粉噪音在82%的科学研究中显示有效，明显优于白噪音的33%；白噪音可使失眠患者入睡时间缩短38%，但仅作为辅助治疗

📚 科学参考文献
----------------------------------------
1. 粉噪音82%有效率 vs 白噪音33%有效率 - 多项睡眠研究荟萃分析
2. 婴幼儿白噪音安全标准：音量≤50dB，距离≥2米 - 儿科睡眠医学研究
3. 老年人粉噪音深度睡眠改善 - 神经科学睡眠研究
4. 失眠患者入睡时间缩短38% - 临床睡眠医学试验
5. 绿噪音中频集中特性与自然环境音模拟 - 心理声学理论分析
6. 自然声音偏好与低频丰富特性 - 心理声学研究

================================================================================

================================================================================
🧠 智能睡眠音频评估与推荐报告
================================================================================
📁 音频文件: Sounds/Noise/brown-noise.wav
⏰ 分析时间: 2025-06-25T11:23:41.611186
🎯 总体推荐: ⚠️ 可以使用：有一定效果，注意使用方法

📊 音频特征分析
----------------------------------------
🎵 噪音类型: 棕噪音
🔊 音频来源: 自然声音
🏷️  声音标签: Low Frequency Dominant
📈 频谱斜率: -2.041
📊 响度稳定性: 0.266
🎼 音调峰值比: 1745963.38
📏 动态范围: 19.8 dB
⏱️  音频时长: 9.5 秒

🛡️ 安全评估
----------------------------------------
🔒 总体安全: 需要注意
🔊 音量安全: 安全
⏰ 时长安全: 安全
📋 内容安全: 需要注意
📊 安全得分: 90.0/100
🎚️  推荐音量: 45-60 dB
📏 推荐距离: ≥50 cm
⚠️  安全警告:
   • 包含明显音调成分，可能影响睡眠

😴 睡眠适用性评估
----------------------------------------
📊 总体得分: 61.0/100
🎯 效果预测: 39.6%
⚠️  干扰风险: 30.0%
😌 舒适度: 67.1%
🔬 科学证据: 理论支持 (低频偏好)

👥 个性化推荐
----------------------------------------
👤 成人
   📊 适用性得分: 61.0/100
   💡 使用建议: 可以使用：注意控制音量在50-60dB，避免整夜高音量播放
   ⚙️  最优设置:
      • volume_db: 60
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: bedtime_routine
   ✅ 潜在益处: 改善入睡时间, 减少夜间觉醒, 屏蔽环境噪声
   ⚠️  注意风险: 长期使用可能产生依赖
   🔄 替代建议: 尝试粉噪音（科学证据最强）, 寻找动态范围更小的音频
   🔬 科学依据: 自然声音通常属于粉噪音或棕噪音，低频丰富，更受用户偏好

👤 婴幼儿
   📊 适用性得分: 42.7/100
   💡 使用建议: 不推荐使用：不符合婴幼儿安全标准
   ⚙️  最优设置:
      • volume_db: 50
      • distance_cm: 200
      • duration_hours: 1.0
      • timing: sleep_onset_only
   ✅ 潜在益处: 辅助快速入睡, 创造一致的睡眠环境
   ⚠️  注意风险: 需严格控制音量和距离, 避免长时间连续使用
   🔄 替代建议: 尝试粉噪音（科学证据最强）, 寻找动态范围更小的音频
   🔬 科学依据: 研究显示80%的新生儿在5分钟内被白噪音哄睡，但需严格控制安全参数；自然声音通常属于粉噪音或棕噪音，低频丰富，更受用户偏好

👤 老年人
   📊 适用性得分: 79.3/100
   💡 使用建议: 推荐使用：低频丰富的声音有助于深度睡眠，建议音量45-55dB
   ⚙️  最优设置:
      • volume_db: 55
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: full_night_if_needed
   ✅ 潜在益处: 增强深度睡眠, 改善记忆巩固, 减少环境干扰
   ⚠️  注意风险: 避免过度依赖, 注意听力保护
   🔄 替代建议: 尝试粉噪音（科学证据最强）, 寻找动态范围更小的音频
   🔬 科学依据: 粉噪音可增强老年人深度睡眠的慢波活动，改善记忆表现；自然声音通常属于粉噪音或棕噪音，低频丰富，更受用户偏好

👤 失眠患者
   📊 适用性得分: 61.0/100
   💡 使用建议: 不推荐使用：建议咨询专业医生选择更合适的治疗方案
   ⚙️  最优设置:
      • volume_db: 60
      • distance_cm: 50
      • duration_hours: 8.0
      • timing: bedtime_routine
   ✅ 潜在益处: 辅助放松, 屏蔽干扰, 建立睡眠仪式感
   ⚠️  注意风险: 仅为辅助手段, 需配合其他治疗方法
   🔄 替代建议: 尝试粉噪音（科学证据最强）, 寻找动态范围更小的音频
   🔬 科学依据: 白噪音可使失眠患者入睡时间缩短38%，但仅作为辅助治疗；自然声音通常属于粉噪音或棕噪音，低频丰富，更受用户偏好

📚 科学参考文献
----------------------------------------
1. 粉噪音82%有效率 vs 白噪音33%有效率 - 多项睡眠研究荟萃分析
2. 婴幼儿白噪音安全标准：音量≤50dB，距离≥2米 - 儿科睡眠医学研究
3. 老年人粉噪音深度睡眠改善 - 神经科学睡眠研究
4. 失眠患者入睡时间缩短38% - 临床睡眠医学试验
5. 绿噪音中频集中特性与自然环境音模拟 - 心理声学理论分析
6. 自然声音偏好与低频丰富特性 - 心理声学研究

================================================================================
