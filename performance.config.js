// 性能监控配置文件

module.exports = {
  // 性能预算配置
  performanceBudget: {
    // Core Web Vitals阈值
    LCP: 2500,    // Largest Contentful Paint (毫秒)
    FID: 100,     // First Input Delay (毫秒)
    CLS: 0.1,     // Cumulative Layout Shift
    FCP: 1800,    // First Contentful Paint (毫秒)
    TTFB: 600,    // Time to First Byte (毫秒)
    
    // 其他性能指标
    TTI: 3800,    // Time to Interactive (毫秒)
    SI: 3000,     // Speed Index (毫秒)
    TBT: 200,     // Total Blocking Time (毫秒)
  },

  // Lighthouse配置
  lighthouse: {
    // 分数阈值
    thresholds: {
      performance: 90,
      accessibility: 95,
      'best-practices': 90,
      seo: 95,
      pwa: 80
    },
    
    // 要审计的页面
    pages: [
      { path: '/', name: 'homepage-en', locale: 'en' },
      { path: '/zh', name: 'homepage-zh', locale: 'zh' },
      { path: '/sounds', name: 'sounds-en', locale: 'en' },
      { path: '/zh/sounds', name: 'sounds-zh', locale: 'zh' },
      { path: '/sounds/rain', name: 'category-rain-en', locale: 'en' },
      { path: '/zh/sounds/rain', name: 'category-rain-zh', locale: 'zh' },
      { path: '/mixing', name: 'mixing-en', locale: 'en' },
      { path: '/zh/mixing', name: 'mixing-zh', locale: 'zh' }
    ],

    // Lighthouse运行配置
    config: {
      extends: 'lighthouse:default',
      settings: {
        onlyAudits: [
          // 性能审计
          'first-contentful-paint',
          'largest-contentful-paint',
          'first-meaningful-paint',
          'speed-index',
          'cumulative-layout-shift',
          'total-blocking-time',
          'max-potential-fid',
          'time-to-interactive',
          'server-response-time',
          'render-blocking-resources',
          'unused-css-rules',
          'unused-javascript',
          'modern-image-formats',
          'uses-optimized-images',
          'uses-text-compression',
          'uses-responsive-images',
          'efficient-animated-content',
          'preload-lcp-image',
          'uses-rel-preconnect',
          'uses-rel-preload',
          'font-display',
          'unminified-css',
          'unminified-javascript',
          'uses-long-cache-ttl',
          'total-byte-weight',
          'dom-size',
          'critical-request-chains',
          'user-timings',
          'bootup-time',
          'mainthread-work-breakdown',
          'diagnostics',
          'network-requests',
          'network-rtt',
          'network-server-latency',
          'main-thread-tasks',
          'metrics',
          'screenshot-thumbnails',
          'final-screenshot',
          
          // 可访问性审计
          'accessibility',
          
          // SEO审计
          'seo',
          
          // 最佳实践审计
          'best-practices'
        ]
      }
    }
  },

  // SEO检查配置
  seo: {
    // 要检查的页面
    pages: [
      { path: '/', name: 'homepage-en', locale: 'en' },
      { path: '/zh', name: 'homepage-zh', locale: 'zh' },
      { path: '/sounds', name: 'sounds-en', locale: 'en' },
      { path: '/zh/sounds', name: 'sounds-zh', locale: 'zh' },
      { path: '/sounds/rain', name: 'category-rain-en', locale: 'en' },
      { path: '/zh/sounds/rain', name: 'category-rain-zh', locale: 'zh' }
    ],

    // SEO检查规则
    rules: {
      title: {
        required: true,
        minLength: 30,
        maxLength: 60
      },
      description: {
        required: true,
        minLength: 120,
        maxLength: 155
      },
      h1: {
        required: true,
        maxCount: 1
      },
      images: {
        requireAlt: true
      },
      canonical: {
        required: true
      },
      openGraph: {
        required: ['og:title', 'og:description', 'og:image']
      },
      hreflang: {
        required: true
      },
      structuredData: {
        recommended: true
      }
    }
  },

  // 监控配置
  monitoring: {
    // 是否启用实时监控
    enabled: process.env.NODE_ENV === 'production',
    
    // 采样率 (0-1)
    sampleRate: 0.1,
    
    // 警报配置
    alerts: {
      // 性能预算超标警报
      performanceBudget: {
        enabled: true,
        channels: ['slack', 'email']
      },
      
      // 错误率警报
      errorRate: {
        enabled: true,
        threshold: 0.05, // 5%
        channels: ['slack']
      },
      
      // 可用性警报
      availability: {
        enabled: true,
        threshold: 0.99, // 99%
        channels: ['slack', 'email']
      }
    },

    // 外部服务配置
    services: {
      googleAnalytics: {
        enabled: !!process.env.NEXT_PUBLIC_GA_TRACKING_ID,
        trackingId: process.env.NEXT_PUBLIC_GA_TRACKING_ID
      },
      
      googleSearchConsole: {
        enabled: !!process.env.GOOGLE_SEARCH_CONSOLE_SITE_URL,
        siteUrl: process.env.GOOGLE_SEARCH_CONSOLE_SITE_URL
      },
      
      slack: {
        enabled: !!process.env.SLACK_WEBHOOK_URL,
        webhookUrl: process.env.SLACK_WEBHOOK_URL
      }
    }
  },

  // 报告配置
  reporting: {
    // 报告输出目录
    outputDir: './reports',
    
    // 报告保留天数
    retentionDays: 30,
    
    // 报告格式
    formats: ['json', 'html'],
    
    // 自动生成汇总报告
    generateSummary: true,
    
    // 发送报告到外部服务
    external: {
      enabled: false,
      endpoint: process.env.REPORTS_ENDPOINT,
      apiKey: process.env.REPORTS_API_KEY
    }
  }
};
