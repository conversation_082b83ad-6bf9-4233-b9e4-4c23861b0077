# NoiseSleep Cloudflare Pages 部署指南

**项目状态**: ✅ 已准备部署  
**部署方式**: Git集成 (推荐) 或 手动上传  
**更新时间**: 2025年07月13日  

## 🎯 部署概述

NoiseSleep是一个基于Next.js 14的白噪音应用，具备以下特性：
- ✅ 多语言支持 (英文/中文)
- ✅ 音频CDN集成 (https://cdn.noisesleep.com)
- ✅ 分析服务集成 (GA4, Clarity, Bing)
- ✅ SEO优化 (sitemap, robots.txt)
- ✅ 响应式设计

## 🚀 推荐部署方式：Git集成

### 步骤1: 准备Git仓库
```bash
# 如果还没有Git仓库，初始化一个
git init
git add .
git commit -m "Initial commit for Cloudflare Pages deployment"

# 推送到GitHub/GitLab
git remote add origin <your-repo-url>
git push -u origin main
```

### 步骤2: 在Cloudflare Pages创建项目
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 进入 **Pages** 部分
3. 点击 **Create a project**
4. 选择 **Connect to Git**
5. 授权并选择您的仓库

### 步骤3: 配置构建设置
```yaml
# 构建配置
Framework preset: Next.js
Build command: npm run build
Build output directory: out
Root directory: (留空)
```

### 步骤4: 设置环境变量
在Cloudflare Pages项目设置中添加以下环境变量：

```bash
# 基础配置
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://noisesleep.com

# 音频CDN配置
NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds
NEXT_PUBLIC_CDN_PERCENTAGE=100
NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD=true

# 分析服务配置
NEXT_PUBLIC_GA_TRACKING_ID=G-FKSNVZQTMD
NEXT_PUBLIC_CLARITY_PROJECT_ID=se2huma822
NEXT_PUBLIC_BING_SITE_VERIFICATION=********************************
NEXT_PUBLIC_ENABLE_ANALYTICS=true

# 性能优化
NEXT_PUBLIC_CACHE_STRATEGY=aggressive
NEXT_PUBLIC_CSP_ENABLED=true
```

## 🔧 解决静态导出问题

### 问题诊断
当前项目在静态导出时遇到以下问题：
- `headers()` 函数在静态导出中不被支持
- next-intl国际化需要运行时请求头检测

### 解决方案A: 使用Cloudflare Pages Functions (推荐)

修改 `next.config.js`:
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 使用Cloudflare Pages Functions而不是静态导出
  output: 'standalone',
  
  // 其他配置保持不变...
  experimental: {
    serverComponentsExternalPackages: ['howler']
  }
};
```

### 解决方案B: 静态导出 + 客户端路由

如果必须使用静态导出，需要修改国际化配置：

1. **修改 `src/i18n/routing.ts`**:
```typescript
export const routing = defineRouting({
  locales: ['en', 'zh'],
  defaultLocale: 'en',
  // 添加静态导出支持
  localePrefix: 'always'
});
```

2. **添加动态配置到页面**:
```typescript
// 在需要的页面添加
export const dynamic = 'force-static';
```

## 📁 手动部署方式

如果选择手动上传，请按以下步骤操作：

### 步骤1: 本地构建
```bash
# 安装依赖
npm install

# 构建项目
npm run build

# 如果使用静态导出
npm run build:static
```

### 步骤2: 准备部署文件
构建完成后，部署文件位于：
- **Standalone模式**: `.next/` 目录
- **静态导出模式**: `out/` 目录

### 步骤3: 上传到Cloudflare Pages
1. 在Cloudflare Pages中选择 **Upload assets**
2. 上传构建输出目录中的所有文件
3. 配置环境变量（同上）

## 🌐 域名配置

### 自定义域名设置
1. 在Cloudflare Pages项目中进入 **Custom domains**
2. 添加域名 `noisesleep.com`
3. 配置DNS记录：
   ```
   Type: CNAME
   Name: @
   Target: <your-pages-subdomain>.pages.dev
   ```

### SSL/TLS配置
- Cloudflare Pages自动提供SSL证书
- 确保SSL/TLS模式设置为 **Full (strict)**

## 🔍 部署后验证清单

### ✅ 基础功能验证
- [ ] 英文主页正常加载: `https://noisesleep.com`
- [ ] 中文主页正常加载: `https://noisesleep.com/zh`
- [ ] 音频播放功能正常
- [ ] 语言切换功能正常

### ✅ SEO功能验证
- [ ] Sitemap可访问: `https://noisesleep.com/sitemap.xml`
- [ ] Robots.txt可访问: `https://noisesleep.com/robots.txt`
- [ ] Meta标签正确显示
- [ ] Open Graph标签正确

### ✅ 分析服务验证
- [ ] Google Analytics数据收集正常
- [ ] Microsoft Clarity会话记录正常
- [ ] Bing网站管理员工具验证通过

### ✅ 性能验证
- [ ] 页面加载速度 < 3秒
- [ ] 音频文件从CDN正确加载
- [ ] 移动端响应式正常
- [ ] PWA功能正常（如果启用）

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 音频无法播放
**症状**: 点击音频卡片后播放器出现但无声音
**解决方案**:
- 检查CDN URL配置: `NEXT_PUBLIC_AUDIO_CDN_URL`
- 验证音频文件可访问性
- 检查浏览器控制台错误

#### 2. 国际化不工作
**症状**: 语言切换无效或显示错误语言
**解决方案**:
- 确认路由配置正确
- 检查locale参数传递
- 验证翻译文件加载

#### 3. 分析服务无数据
**症状**: Google Analytics或Clarity无数据
**解决方案**:
- 确认环境变量设置正确
- 检查脚本加载状态
- 验证追踪ID正确性

#### 4. 构建失败
**症状**: Cloudflare Pages构建过程失败
**解决方案**:
- 检查Node.js版本兼容性
- 确认所有依赖已安装
- 查看构建日志详细错误

## 📊 监控和维护

### 性能监控
- 使用Cloudflare Analytics监控访问量
- 通过Web Vitals监控页面性能
- 设置Uptime监控确保服务可用性

### 定期维护
- 每月检查依赖更新
- 每季度审查分析数据
- 定期备份配置和内容

## 🔗 相关链接

- [Cloudflare Pages文档](https://developers.cloudflare.com/pages/)
- [Next.js部署指南](https://nextjs.org/docs/deployment)
- [项目GitHub仓库](https://github.com/your-username/noisesleep)

---

**部署完成后，请按照验证清单逐项检查，确保所有功能正常运行。**
