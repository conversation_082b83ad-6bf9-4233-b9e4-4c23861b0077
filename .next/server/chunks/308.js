exports.id=308,exports.ids=[308],exports.modules={3222:e=>{e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},8391:e=>{e.exports={style:{fontFamily:"'__Noto_Sans_SC_8dfb49', '__Noto_Sans_SC_Fallback_8dfb49'",fontStyle:"normal"},className:"__className_8dfb49",variable:"__variable_8dfb49"}},8913:(e,t,r)=>{"use strict";function n(e,t){var r=t&&t.cache?t.cache:l,n=t&&t.serializer?t.serializer:s;return(t&&t.strategy?t.strategy:function(e,t){var r,n,s=1===e.length?i:o;return r=t.cache.create(),n=t.serializer,s.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function i(e,t,r,n){var i=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),o=t.get(i);return void 0===o&&(o=e.call(this,n),t.set(i,o)),o}function o(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),o=t.get(i);return void 0===o&&(o=e.apply(this,n),t.set(i,o)),o}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>u});var s=function(){return JSON.stringify(arguments)},a=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),l={create:function(){return new a}},u={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)}}},7230:(e,t)=>{var r;(function(){"use strict";var n=function(){this.init()};n.prototype={init:function(){var e=this||i;return e._counter=1e3,e._html5AudioPool=[],e.html5PoolSize=10,e._codecs={},e._howls=[],e._muted=!1,e._volume=1,e._canPlayEvent="canplaythrough",e._navigator="undefined"!=typeof window&&window.navigator?window.navigator:null,e.masterGain=null,e.noAudio=!1,e.usingWebAudio=!0,e.autoSuspend=!0,e.ctx=null,e.autoUnlock=!0,e._setup(),e},volume:function(e){var t=this||i;if(e=parseFloat(e),t.ctx||d(),void 0!==e&&e>=0&&e<=1){if(t._volume=e,t._muted)return t;t.usingWebAudio&&t.masterGain.gain.setValueAtTime(e,i.ctx.currentTime);for(var r=0;r<t._howls.length;r++)if(!t._howls[r]._webAudio)for(var n=t._howls[r]._getSoundIds(),o=0;o<n.length;o++){var s=t._howls[r]._soundById(n[o]);s&&s._node&&(s._node.volume=s._volume*e)}return t}return t._volume},mute:function(e){var t=this||i;t.ctx||d(),t._muted=e,t.usingWebAudio&&t.masterGain.gain.setValueAtTime(e?0:t._volume,i.ctx.currentTime);for(var r=0;r<t._howls.length;r++)if(!t._howls[r]._webAudio)for(var n=t._howls[r]._getSoundIds(),o=0;o<n.length;o++){var s=t._howls[r]._soundById(n[o]);s&&s._node&&(s._node.muted=!!e||s._muted)}return t},stop:function(){for(var e=this||i,t=0;t<e._howls.length;t++)e._howls[t].stop();return e},unload:function(){for(var e=this||i,t=e._howls.length-1;t>=0;t--)e._howls[t].unload();return e.usingWebAudio&&e.ctx&&void 0!==e.ctx.close&&(e.ctx.close(),e.ctx=null,d()),e},codecs:function(e){return(this||i)._codecs[e.replace(/^x-/,"")]},_setup:function(){var e=this||i;if(e.state=e.ctx&&e.ctx.state||"suspended",e._autoSuspend(),!e.usingWebAudio){if("undefined"!=typeof Audio)try{var t=new Audio;void 0===t.oncanplaythrough&&(e._canPlayEvent="canplay")}catch(t){e.noAudio=!0}else e.noAudio=!0}try{var t=new Audio;t.muted&&(e.noAudio=!0)}catch(e){}return e.noAudio||e._setupCodecs(),e},_setupCodecs:function(){var e=this||i,t=null;try{t="undefined"!=typeof Audio?new Audio:null}catch(t){return e}if(!t||"function"!=typeof t.canPlayType)return e;var r=t.canPlayType("audio/mpeg;").replace(/^no$/,""),n=e._navigator?e._navigator.userAgent:"",o=n.match(/OPR\/(\d+)/g),s=o&&33>parseInt(o[0].split("/")[1],10),a=-1!==n.indexOf("Safari")&&-1===n.indexOf("Chrome"),l=n.match(/Version\/(.*?) /),u=a&&l&&15>parseInt(l[1],10);return e._codecs={mp3:!!(!s&&(r||t.canPlayType("audio/mp3;").replace(/^no$/,""))),mpeg:!!r,opus:!!t.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/,""),ogg:!!t.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),oga:!!t.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),wav:!!(t.canPlayType('audio/wav; codecs="1"')||t.canPlayType("audio/wav")).replace(/^no$/,""),aac:!!t.canPlayType("audio/aac;").replace(/^no$/,""),caf:!!t.canPlayType("audio/x-caf;").replace(/^no$/,""),m4a:!!(t.canPlayType("audio/x-m4a;")||t.canPlayType("audio/m4a;")||t.canPlayType("audio/aac;")).replace(/^no$/,""),m4b:!!(t.canPlayType("audio/x-m4b;")||t.canPlayType("audio/m4b;")||t.canPlayType("audio/aac;")).replace(/^no$/,""),mp4:!!(t.canPlayType("audio/x-mp4;")||t.canPlayType("audio/mp4;")||t.canPlayType("audio/aac;")).replace(/^no$/,""),weba:!!(!u&&t.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),webm:!!(!u&&t.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),dolby:!!t.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/,""),flac:!!(t.canPlayType("audio/x-flac;")||t.canPlayType("audio/flac;")).replace(/^no$/,"")},e},_unlockAudio:function(){var e=this||i;if(!e._audioUnlocked&&e.ctx){e._audioUnlocked=!1,e.autoUnlock=!1,e._mobileUnloaded||44100===e.ctx.sampleRate||(e._mobileUnloaded=!0,e.unload()),e._scratchBuffer=e.ctx.createBuffer(1,1,22050);var t=function(r){for(;e._html5AudioPool.length<e.html5PoolSize;)try{var n=new Audio;n._unlocked=!0,e._releaseHtml5Audio(n)}catch(t){e.noAudio=!0;break}for(var i=0;i<e._howls.length;i++)if(!e._howls[i]._webAudio)for(var o=e._howls[i]._getSoundIds(),s=0;s<o.length;s++){var a=e._howls[i]._soundById(o[s]);a&&a._node&&!a._node._unlocked&&(a._node._unlocked=!0,a._node.load())}e._autoResume();var l=e.ctx.createBufferSource();l.buffer=e._scratchBuffer,l.connect(e.ctx.destination),void 0===l.start?l.noteOn(0):l.start(0),"function"==typeof e.ctx.resume&&e.ctx.resume(),l.onended=function(){l.disconnect(0),e._audioUnlocked=!0,document.removeEventListener("touchstart",t,!0),document.removeEventListener("touchend",t,!0),document.removeEventListener("click",t,!0),document.removeEventListener("keydown",t,!0);for(var r=0;r<e._howls.length;r++)e._howls[r]._emit("unlock")}};return document.addEventListener("touchstart",t,!0),document.addEventListener("touchend",t,!0),document.addEventListener("click",t,!0),document.addEventListener("keydown",t,!0),e}},_obtainHtml5Audio:function(){var e=this||i;if(e._html5AudioPool.length)return e._html5AudioPool.pop();var t=new Audio().play();return t&&"undefined"!=typeof Promise&&(t instanceof Promise||"function"==typeof t.then)&&t.catch(function(){console.warn("HTML5 Audio pool exhausted, returning potentially locked audio object.")}),new Audio},_releaseHtml5Audio:function(e){var t=this||i;return e._unlocked&&t._html5AudioPool.push(e),t},_autoSuspend:function(){var e=this;if(e.autoSuspend&&e.ctx&&void 0!==e.ctx.suspend&&i.usingWebAudio){for(var t=0;t<e._howls.length;t++)if(e._howls[t]._webAudio){for(var r=0;r<e._howls[t]._sounds.length;r++)if(!e._howls[t]._sounds[r]._paused)return e}return e._suspendTimer&&clearTimeout(e._suspendTimer),e._suspendTimer=setTimeout(function(){if(e.autoSuspend){e._suspendTimer=null,e.state="suspending";var t=function(){e.state="suspended",e._resumeAfterSuspend&&(delete e._resumeAfterSuspend,e._autoResume())};e.ctx.suspend().then(t,t)}},3e4),e}},_autoResume:function(){var e=this;if(e.ctx&&void 0!==e.ctx.resume&&i.usingWebAudio)return"running"===e.state&&"interrupted"!==e.ctx.state&&e._suspendTimer?(clearTimeout(e._suspendTimer),e._suspendTimer=null):"suspended"===e.state||"running"===e.state&&"interrupted"===e.ctx.state?(e.ctx.resume().then(function(){e.state="running";for(var t=0;t<e._howls.length;t++)e._howls[t]._emit("resume")}),e._suspendTimer&&(clearTimeout(e._suspendTimer),e._suspendTimer=null)):"suspending"===e.state&&(e._resumeAfterSuspend=!0),e}};var i=new n,o=function(e){if(!e.src||0===e.src.length){console.error("An array of source files must be passed with any new Howl.");return}this.init(e)};o.prototype={init:function(e){var t=this;return i.ctx||d(),t._autoplay=e.autoplay||!1,t._format="string"!=typeof e.format?e.format:[e.format],t._html5=e.html5||!1,t._muted=e.mute||!1,t._loop=e.loop||!1,t._pool=e.pool||5,t._preload="boolean"!=typeof e.preload&&"metadata"!==e.preload||e.preload,t._rate=e.rate||1,t._sprite=e.sprite||{},t._src="string"!=typeof e.src?e.src:[e.src],t._volume=void 0!==e.volume?e.volume:1,t._xhr={method:e.xhr&&e.xhr.method?e.xhr.method:"GET",headers:e.xhr&&e.xhr.headers?e.xhr.headers:null,withCredentials:!!e.xhr&&!!e.xhr.withCredentials&&e.xhr.withCredentials},t._duration=0,t._state="unloaded",t._sounds=[],t._endTimers={},t._queue=[],t._playLock=!1,t._onend=e.onend?[{fn:e.onend}]:[],t._onfade=e.onfade?[{fn:e.onfade}]:[],t._onload=e.onload?[{fn:e.onload}]:[],t._onloaderror=e.onloaderror?[{fn:e.onloaderror}]:[],t._onplayerror=e.onplayerror?[{fn:e.onplayerror}]:[],t._onpause=e.onpause?[{fn:e.onpause}]:[],t._onplay=e.onplay?[{fn:e.onplay}]:[],t._onstop=e.onstop?[{fn:e.onstop}]:[],t._onmute=e.onmute?[{fn:e.onmute}]:[],t._onvolume=e.onvolume?[{fn:e.onvolume}]:[],t._onrate=e.onrate?[{fn:e.onrate}]:[],t._onseek=e.onseek?[{fn:e.onseek}]:[],t._onunlock=e.onunlock?[{fn:e.onunlock}]:[],t._onresume=[],t._webAudio=i.usingWebAudio&&!t._html5,void 0!==i.ctx&&i.ctx&&i.autoUnlock&&i._unlockAudio(),i._howls.push(t),t._autoplay&&t._queue.push({event:"play",action:function(){t.play()}}),t._preload&&"none"!==t._preload&&t.load(),t},load:function(){var e,t,r=null;if(i.noAudio){this._emit("loaderror",null,"No audio support.");return}"string"==typeof this._src&&(this._src=[this._src]);for(var n=0;n<this._src.length;n++){if(this._format&&this._format[n])e=this._format[n];else{if("string"!=typeof(t=this._src[n])){this._emit("loaderror",null,"Non-string found in selected audio sources - ignoring.");continue}(e=/^data:audio\/([^;,]+);/i.exec(t))||(e=/\.([^.]+)$/.exec(t.split("?",1)[0])),e&&(e=e[1].toLowerCase())}if(e||console.warn('No file extension was found. Consider using the "format" property or specify an extension.'),e&&i.codecs(e)){r=this._src[n];break}}if(!r){this._emit("loaderror",null,"No codec support for selected audio sources.");return}return this._src=r,this._state="loading","https:"===window.location.protocol&&"http:"===r.slice(0,5)&&(this._html5=!0,this._webAudio=!1),new s(this),this._webAudio&&l(this),this},play:function(e,t){var r=this,n=null;if("number"==typeof e)n=e,e=null;else if("string"==typeof e&&"loaded"===r._state&&!r._sprite[e])return null;else if(void 0===e&&(e="__default",!r._playLock)){for(var o=0,s=0;s<r._sounds.length;s++)r._sounds[s]._paused&&!r._sounds[s]._ended&&(o++,n=r._sounds[s]._id);1===o?e=null:n=null}var a=n?r._soundById(n):r._inactiveSound();if(!a)return null;if(n&&!e&&(e=a._sprite||"__default"),"loaded"!==r._state){a._sprite=e,a._ended=!1;var l=a._id;return r._queue.push({event:"play",action:function(){r.play(l)}}),l}if(n&&!a._paused)return t||r._loadQueue("play"),a._id;r._webAudio&&i._autoResume();var u=Math.max(0,a._seek>0?a._seek:r._sprite[e][0]/1e3),h=Math.max(0,(r._sprite[e][0]+r._sprite[e][1])/1e3-u),c=1e3*h/Math.abs(a._rate),d=r._sprite[e][0]/1e3,f=(r._sprite[e][0]+r._sprite[e][1])/1e3;a._sprite=e,a._ended=!1;var p=function(){a._paused=!1,a._seek=u,a._start=d,a._stop=f,a._loop=!!(a._loop||r._sprite[e][2])};if(u>=f){r._ended(a);return}var m=a._node;if(r._webAudio){var g=function(){r._playLock=!1,p(),r._refreshBuffer(a);var e=a._muted||r._muted?0:a._volume;m.gain.setValueAtTime(e,i.ctx.currentTime),a._playStart=i.ctx.currentTime,void 0===m.bufferSource.start?a._loop?m.bufferSource.noteGrainOn(0,u,86400):m.bufferSource.noteGrainOn(0,u,h):a._loop?m.bufferSource.start(0,u,86400):m.bufferSource.start(0,u,h),c!==1/0&&(r._endTimers[a._id]=setTimeout(r._ended.bind(r,a),c)),t||setTimeout(function(){r._emit("play",a._id),r._loadQueue()},0)};"running"===i.state&&"interrupted"!==i.ctx.state?g():(r._playLock=!0,r.once("resume",g),r._clearTimer(a._id))}else{var v=function(){m.currentTime=u,m.muted=a._muted||r._muted||i._muted||m.muted,m.volume=a._volume*i.volume(),m.playbackRate=a._rate;try{var n=m.play();if(n&&"undefined"!=typeof Promise&&(n instanceof Promise||"function"==typeof n.then)?(r._playLock=!0,p(),n.then(function(){r._playLock=!1,m._unlocked=!0,t?r._loadQueue():r._emit("play",a._id)}).catch(function(){r._playLock=!1,r._emit("playerror",a._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction."),a._ended=!0,a._paused=!0})):t||(r._playLock=!1,p(),r._emit("play",a._id)),m.playbackRate=a._rate,m.paused){r._emit("playerror",a._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.");return}"__default"!==e||a._loop?r._endTimers[a._id]=setTimeout(r._ended.bind(r,a),c):(r._endTimers[a._id]=function(){r._ended(a),m.removeEventListener("ended",r._endTimers[a._id],!1)},m.addEventListener("ended",r._endTimers[a._id],!1))}catch(e){r._emit("playerror",a._id,e)}};"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"===m.src&&(m.src=r._src,m.load());var y=window&&window.ejecta||!m.readyState&&i._navigator.isCocoonJS;if(m.readyState>=3||y)v();else{r._playLock=!0,r._state="loading";var _=function(){r._state="loaded",v(),m.removeEventListener(i._canPlayEvent,_,!1)};m.addEventListener(i._canPlayEvent,_,!1),r._clearTimer(a._id)}}return a._id},pause:function(e){var t=this;if("loaded"!==t._state||t._playLock)return t._queue.push({event:"pause",action:function(){t.pause(e)}}),t;for(var r=t._getSoundIds(e),n=0;n<r.length;n++){t._clearTimer(r[n]);var i=t._soundById(r[n]);if(i&&!i._paused&&(i._seek=t.seek(r[n]),i._rateSeek=0,i._paused=!0,t._stopFade(r[n]),i._node)){if(t._webAudio){if(!i._node.bufferSource)continue;void 0===i._node.bufferSource.stop?i._node.bufferSource.noteOff(0):i._node.bufferSource.stop(0),t._cleanBuffer(i._node)}else isNaN(i._node.duration)&&i._node.duration!==1/0||i._node.pause()}arguments[1]||t._emit("pause",i?i._id:null)}return t},stop:function(e,t){var r=this;if("loaded"!==r._state||r._playLock)return r._queue.push({event:"stop",action:function(){r.stop(e)}}),r;for(var n=r._getSoundIds(e),i=0;i<n.length;i++){r._clearTimer(n[i]);var o=r._soundById(n[i]);o&&(o._seek=o._start||0,o._rateSeek=0,o._paused=!0,o._ended=!0,r._stopFade(n[i]),o._node&&(r._webAudio?o._node.bufferSource&&(void 0===o._node.bufferSource.stop?o._node.bufferSource.noteOff(0):o._node.bufferSource.stop(0),r._cleanBuffer(o._node)):isNaN(o._node.duration)&&o._node.duration!==1/0||(o._node.currentTime=o._start||0,o._node.pause(),o._node.duration===1/0&&r._clearSound(o._node))),t||r._emit("stop",o._id))}return r},mute:function(e,t){var r=this;if("loaded"!==r._state||r._playLock)return r._queue.push({event:"mute",action:function(){r.mute(e,t)}}),r;if(void 0===t){if("boolean"!=typeof e)return r._muted;r._muted=e}for(var n=r._getSoundIds(t),o=0;o<n.length;o++){var s=r._soundById(n[o]);s&&(s._muted=e,s._interval&&r._stopFade(s._id),r._webAudio&&s._node?s._node.gain.setValueAtTime(e?0:s._volume,i.ctx.currentTime):s._node&&(s._node.muted=!!i._muted||e),r._emit("mute",s._id))}return r},volume:function(){var e,t,r,n=this,o=arguments;if(0===o.length)return n._volume;if(1===o.length||2===o.length&&void 0===o[1]?n._getSoundIds().indexOf(o[0])>=0?t=parseInt(o[0],10):e=parseFloat(o[0]):o.length>=2&&(e=parseFloat(o[0]),t=parseInt(o[1],10)),void 0===e||!(e>=0)||!(e<=1))return(r=t?n._soundById(t):n._sounds[0])?r._volume:0;if("loaded"!==n._state||n._playLock)return n._queue.push({event:"volume",action:function(){n.volume.apply(n,o)}}),n;void 0===t&&(n._volume=e),t=n._getSoundIds(t);for(var s=0;s<t.length;s++)(r=n._soundById(t[s]))&&(r._volume=e,o[2]||n._stopFade(t[s]),n._webAudio&&r._node&&!r._muted?r._node.gain.setValueAtTime(e,i.ctx.currentTime):r._node&&!r._muted&&(r._node.volume=e*i.volume()),n._emit("volume",r._id));return n},fade:function(e,t,r,n){var o=this;if("loaded"!==o._state||o._playLock)return o._queue.push({event:"fade",action:function(){o.fade(e,t,r,n)}}),o;e=Math.min(Math.max(0,parseFloat(e)),1),t=Math.min(Math.max(0,parseFloat(t)),1),r=parseFloat(r),o.volume(e,n);for(var s=o._getSoundIds(n),a=0;a<s.length;a++){var l=o._soundById(s[a]);if(l){if(n||o._stopFade(s[a]),o._webAudio&&!l._muted){var u=i.ctx.currentTime,h=u+r/1e3;l._volume=e,l._node.gain.setValueAtTime(e,u),l._node.gain.linearRampToValueAtTime(t,h)}o._startFadeInterval(l,e,t,r,s[a],void 0===n)}}return o},_startFadeInterval:function(e,t,r,n,i,o){var s=this,a=t,l=r-t,u=Math.abs(l/.01),h=Date.now();e._fadeTo=r,e._interval=setInterval(function(){var i=(Date.now()-h)/n;h=Date.now(),a+=l*i,a=Math.round(100*a)/100,a=l<0?Math.max(r,a):Math.min(r,a),s._webAudio?e._volume=a:s.volume(a,e._id,!0),o&&(s._volume=a),(r<t&&a<=r||r>t&&a>=r)&&(clearInterval(e._interval),e._interval=null,e._fadeTo=null,s.volume(r,e._id),s._emit("fade",e._id))},Math.max(4,u>0?n/u:n))},_stopFade:function(e){var t=this._soundById(e);return t&&t._interval&&(this._webAudio&&t._node.gain.cancelScheduledValues(i.ctx.currentTime),clearInterval(t._interval),t._interval=null,this.volume(t._fadeTo,e),t._fadeTo=null,this._emit("fade",e)),this},loop:function(){var e,t,r,n=arguments;if(0===n.length)return this._loop;if(1===n.length){if("boolean"!=typeof n[0])return!!(r=this._soundById(parseInt(n[0],10)))&&r._loop;e=n[0],this._loop=e}else 2===n.length&&(e=n[0],t=parseInt(n[1],10));for(var i=this._getSoundIds(t),o=0;o<i.length;o++)(r=this._soundById(i[o]))&&(r._loop=e,this._webAudio&&r._node&&r._node.bufferSource&&(r._node.bufferSource.loop=e,e&&(r._node.bufferSource.loopStart=r._start||0,r._node.bufferSource.loopEnd=r._stop,this.playing(i[o])&&(this.pause(i[o],!0),this.play(i[o],!0)))));return this},rate:function(){var e,t,r,n=this,o=arguments;if(0===o.length?t=n._sounds[0]._id:1===o.length?n._getSoundIds().indexOf(o[0])>=0?t=parseInt(o[0],10):e=parseFloat(o[0]):2===o.length&&(e=parseFloat(o[0]),t=parseInt(o[1],10)),"number"!=typeof e)return(r=n._soundById(t))?r._rate:n._rate;if("loaded"!==n._state||n._playLock)return n._queue.push({event:"rate",action:function(){n.rate.apply(n,o)}}),n;void 0===t&&(n._rate=e),t=n._getSoundIds(t);for(var s=0;s<t.length;s++)if(r=n._soundById(t[s])){n.playing(t[s])&&(r._rateSeek=n.seek(t[s]),r._playStart=n._webAudio?i.ctx.currentTime:r._playStart),r._rate=e,n._webAudio&&r._node&&r._node.bufferSource?r._node.bufferSource.playbackRate.setValueAtTime(e,i.ctx.currentTime):r._node&&(r._node.playbackRate=e);var a=n.seek(t[s]),l=1e3*((n._sprite[r._sprite][0]+n._sprite[r._sprite][1])/1e3-a)/Math.abs(r._rate);(n._endTimers[t[s]]||!r._paused)&&(n._clearTimer(t[s]),n._endTimers[t[s]]=setTimeout(n._ended.bind(n,r),l)),n._emit("rate",r._id)}return n},seek:function(){var e,t,r=this,n=arguments;if(0===n.length?r._sounds.length&&(t=r._sounds[0]._id):1===n.length?r._getSoundIds().indexOf(n[0])>=0?t=parseInt(n[0],10):r._sounds.length&&(t=r._sounds[0]._id,e=parseFloat(n[0])):2===n.length&&(e=parseFloat(n[0]),t=parseInt(n[1],10)),void 0===t)return 0;if("number"==typeof e&&("loaded"!==r._state||r._playLock))return r._queue.push({event:"seek",action:function(){r.seek.apply(r,n)}}),r;var o=r._soundById(t);if(o){if("number"==typeof e&&e>=0){var s=r.playing(t);s&&r.pause(t,!0),o._seek=e,o._ended=!1,r._clearTimer(t),r._webAudio||!o._node||isNaN(o._node.duration)||(o._node.currentTime=e);var a=function(){s&&r.play(t,!0),r._emit("seek",t)};if(s&&!r._webAudio){var l=function(){r._playLock?setTimeout(l,0):a()};setTimeout(l,0)}else a()}else{if(!r._webAudio)return o._node.currentTime;var u=r.playing(t)?i.ctx.currentTime-o._playStart:0,h=o._rateSeek?o._rateSeek-o._seek:0;return o._seek+(h+u*Math.abs(o._rate))}}return r},playing:function(e){if("number"==typeof e){var t=this._soundById(e);return!!t&&!t._paused}for(var r=0;r<this._sounds.length;r++)if(!this._sounds[r]._paused)return!0;return!1},duration:function(e){var t=this._duration,r=this._soundById(e);return r&&(t=this._sprite[r._sprite][1]/1e3),t},state:function(){return this._state},unload:function(){for(var e=this,t=e._sounds,r=0;r<t.length;r++)t[r]._paused||e.stop(t[r]._id),e._webAudio||(e._clearSound(t[r]._node),t[r]._node.removeEventListener("error",t[r]._errorFn,!1),t[r]._node.removeEventListener(i._canPlayEvent,t[r]._loadFn,!1),t[r]._node.removeEventListener("ended",t[r]._endFn,!1),i._releaseHtml5Audio(t[r]._node)),delete t[r]._node,e._clearTimer(t[r]._id);var n=i._howls.indexOf(e);n>=0&&i._howls.splice(n,1);var o=!0;for(r=0;r<i._howls.length;r++)if(i._howls[r]._src===e._src||e._src.indexOf(i._howls[r]._src)>=0){o=!1;break}return a&&o&&delete a[e._src],i.noAudio=!1,e._state="unloaded",e._sounds=[],e=null,null},on:function(e,t,r,n){var i=this["_on"+e];return"function"==typeof t&&i.push(n?{id:r,fn:t,once:n}:{id:r,fn:t}),this},off:function(e,t,r){var n=this["_on"+e],i=0;if("number"==typeof t&&(r=t,t=null),t||r)for(i=0;i<n.length;i++){var o=r===n[i].id;if(t===n[i].fn&&o||!t&&o){n.splice(i,1);break}}else if(e)this["_on"+e]=[];else{var s=Object.keys(this);for(i=0;i<s.length;i++)0===s[i].indexOf("_on")&&Array.isArray(this[s[i]])&&(this[s[i]]=[])}return this},once:function(e,t,r){return this.on(e,t,r,1),this},_emit:function(e,t,r){for(var n=this["_on"+e],i=n.length-1;i>=0;i--)(!n[i].id||n[i].id===t||"load"===e)&&(setTimeout((function(e){e.call(this,t,r)}).bind(this,n[i].fn),0),n[i].once&&this.off(e,n[i].fn,n[i].id));return this._loadQueue(e),this},_loadQueue:function(e){if(this._queue.length>0){var t=this._queue[0];t.event===e&&(this._queue.shift(),this._loadQueue()),e||t.action()}return this},_ended:function(e){var t=e._sprite;if(!this._webAudio&&e._node&&!e._node.paused&&!e._node.ended&&e._node.currentTime<e._stop)return setTimeout(this._ended.bind(this,e),100),this;var r=!!(e._loop||this._sprite[t][2]);if(this._emit("end",e._id),!this._webAudio&&r&&this.stop(e._id,!0).play(e._id),this._webAudio&&r){this._emit("play",e._id),e._seek=e._start||0,e._rateSeek=0,e._playStart=i.ctx.currentTime;var n=(e._stop-e._start)*1e3/Math.abs(e._rate);this._endTimers[e._id]=setTimeout(this._ended.bind(this,e),n)}return this._webAudio&&!r&&(e._paused=!0,e._ended=!0,e._seek=e._start||0,e._rateSeek=0,this._clearTimer(e._id),this._cleanBuffer(e._node),i._autoSuspend()),this._webAudio||r||this.stop(e._id,!0),this},_clearTimer:function(e){if(this._endTimers[e]){if("function"!=typeof this._endTimers[e])clearTimeout(this._endTimers[e]);else{var t=this._soundById(e);t&&t._node&&t._node.removeEventListener("ended",this._endTimers[e],!1)}delete this._endTimers[e]}return this},_soundById:function(e){for(var t=0;t<this._sounds.length;t++)if(e===this._sounds[t]._id)return this._sounds[t];return null},_inactiveSound:function(){this._drain();for(var e=0;e<this._sounds.length;e++)if(this._sounds[e]._ended)return this._sounds[e].reset();return new s(this)},_drain:function(){var e=this._pool,t=0,r=0;if(!(this._sounds.length<e)){for(r=0;r<this._sounds.length;r++)this._sounds[r]._ended&&t++;for(r=this._sounds.length-1;r>=0;r--){if(t<=e)return;this._sounds[r]._ended&&(this._webAudio&&this._sounds[r]._node&&this._sounds[r]._node.disconnect(0),this._sounds.splice(r,1),t--)}}},_getSoundIds:function(e){if(void 0!==e)return[e];for(var t=[],r=0;r<this._sounds.length;r++)t.push(this._sounds[r]._id);return t},_refreshBuffer:function(e){return e._node.bufferSource=i.ctx.createBufferSource(),e._node.bufferSource.buffer=a[this._src],e._panner?e._node.bufferSource.connect(e._panner):e._node.bufferSource.connect(e._node),e._node.bufferSource.loop=e._loop,e._loop&&(e._node.bufferSource.loopStart=e._start||0,e._node.bufferSource.loopEnd=e._stop||0),e._node.bufferSource.playbackRate.setValueAtTime(e._rate,i.ctx.currentTime),this},_cleanBuffer:function(e){var t=i._navigator&&i._navigator.vendor.indexOf("Apple")>=0;if(!e.bufferSource)return this;if(i._scratchBuffer&&e.bufferSource&&(e.bufferSource.onended=null,e.bufferSource.disconnect(0),t))try{e.bufferSource.buffer=i._scratchBuffer}catch(e){}return e.bufferSource=null,this},_clearSound:function(e){/MSIE |Trident\//.test(i._navigator&&i._navigator.userAgent)||(e.src="data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA")}};var s=function(e){this._parent=e,this.init()};s.prototype={init:function(){var e=this._parent;return this._muted=e._muted,this._loop=e._loop,this._volume=e._volume,this._rate=e._rate,this._seek=0,this._paused=!0,this._ended=!0,this._sprite="__default",this._id=++i._counter,e._sounds.push(this),this.create(),this},create:function(){var e=this._parent,t=i._muted||this._muted||this._parent._muted?0:this._volume;return e._webAudio?(this._node=void 0===i.ctx.createGain?i.ctx.createGainNode():i.ctx.createGain(),this._node.gain.setValueAtTime(t,i.ctx.currentTime),this._node.paused=!0,this._node.connect(i.masterGain)):i.noAudio||(this._node=i._obtainHtml5Audio(),this._errorFn=this._errorListener.bind(this),this._node.addEventListener("error",this._errorFn,!1),this._loadFn=this._loadListener.bind(this),this._node.addEventListener(i._canPlayEvent,this._loadFn,!1),this._endFn=this._endListener.bind(this),this._node.addEventListener("ended",this._endFn,!1),this._node.src=e._src,this._node.preload=!0===e._preload?"auto":e._preload,this._node.volume=t*i.volume(),this._node.load()),this},reset:function(){var e=this._parent;return this._muted=e._muted,this._loop=e._loop,this._volume=e._volume,this._rate=e._rate,this._seek=0,this._rateSeek=0,this._paused=!0,this._ended=!0,this._sprite="__default",this._id=++i._counter,this},_errorListener:function(){this._parent._emit("loaderror",this._id,this._node.error?this._node.error.code:0),this._node.removeEventListener("error",this._errorFn,!1)},_loadListener:function(){var e=this._parent;e._duration=Math.ceil(10*this._node.duration)/10,0===Object.keys(e._sprite).length&&(e._sprite={__default:[0,1e3*e._duration]}),"loaded"!==e._state&&(e._state="loaded",e._emit("load"),e._loadQueue()),this._node.removeEventListener(i._canPlayEvent,this._loadFn,!1)},_endListener:function(){var e=this._parent;e._duration===1/0&&(e._duration=Math.ceil(10*this._node.duration)/10,e._sprite.__default[1]===1/0&&(e._sprite.__default[1]=1e3*e._duration),e._ended(this)),this._node.removeEventListener("ended",this._endFn,!1)}};var a={},l=function(e){var t=e._src;if(a[t]){e._duration=a[t].duration,c(e);return}if(/^data:[^;]+;base64,/.test(t)){for(var r=atob(t.split(",")[1]),n=new Uint8Array(r.length),i=0;i<r.length;++i)n[i]=r.charCodeAt(i);h(n.buffer,e)}else{var o=new XMLHttpRequest;o.open(e._xhr.method,t,!0),o.withCredentials=e._xhr.withCredentials,o.responseType="arraybuffer",e._xhr.headers&&Object.keys(e._xhr.headers).forEach(function(t){o.setRequestHeader(t,e._xhr.headers[t])}),o.onload=function(){var t=(o.status+"")[0];if("0"!==t&&"2"!==t&&"3"!==t){e._emit("loaderror",null,"Failed loading audio file with status: "+o.status+".");return}h(o.response,e)},o.onerror=function(){e._webAudio&&(e._html5=!0,e._webAudio=!1,e._sounds=[],delete a[t],e.load())},u(o)}},u=function(e){try{e.send()}catch(t){e.onerror()}},h=function(e,t){var r=function(){t._emit("loaderror",null,"Decoding audio data failed.")},n=function(e){e&&t._sounds.length>0?(a[t._src]=e,c(t,e)):r()};"undefined"!=typeof Promise&&1===i.ctx.decodeAudioData.length?i.ctx.decodeAudioData(e).then(n).catch(r):i.ctx.decodeAudioData(e,n,r)},c=function(e,t){t&&!e._duration&&(e._duration=t.duration),0===Object.keys(e._sprite).length&&(e._sprite={__default:[0,1e3*e._duration]}),"loaded"!==e._state&&(e._state="loaded",e._emit("load"),e._loadQueue())},d=function(){if(i.usingWebAudio){try{"undefined"!=typeof AudioContext?i.ctx=new AudioContext:"undefined"!=typeof webkitAudioContext?i.ctx=new webkitAudioContext:i.usingWebAudio=!1}catch(e){i.usingWebAudio=!1}i.ctx||(i.usingWebAudio=!1);var e=/iP(hone|od|ad)/.test(i._navigator&&i._navigator.platform),t=i._navigator&&i._navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/),r=t?parseInt(t[1],10):null;if(e&&r&&r<9){var n=/safari/.test(i._navigator&&i._navigator.userAgent.toLowerCase());i._navigator&&!n&&(i.usingWebAudio=!1)}i.usingWebAudio&&(i.masterGain=void 0===i.ctx.createGain?i.ctx.createGainNode():i.ctx.createGain(),i.masterGain.gain.setValueAtTime(i._muted?0:i._volume,i.ctx.currentTime),i.masterGain.connect(i.ctx.destination)),i._setup()}};void 0!==(r=(function(){return{Howler:i,Howl:o}}).apply(t,[]))&&(e.exports=r),t.Howler=i,t.Howl=o,"undefined"!=typeof global?(global.HowlerGlobal=n,global.Howler=i,global.Howl=o,global.Sound=s):"undefined"!=typeof window&&(window.HowlerGlobal=n,window.Howler=i,window.Howl=o,window.Sound=s)})(),function(){"use strict";HowlerGlobal.prototype._pos=[0,0,0],HowlerGlobal.prototype._orientation=[0,0,-1,0,1,0],HowlerGlobal.prototype.stereo=function(e){if(!this.ctx||!this.ctx.listener)return this;for(var t=this._howls.length-1;t>=0;t--)this._howls[t].stereo(e);return this},HowlerGlobal.prototype.pos=function(e,t,r){return this.ctx&&this.ctx.listener?(t="number"!=typeof t?this._pos[1]:t,r="number"!=typeof r?this._pos[2]:r,"number"!=typeof e)?this._pos:(this._pos=[e,t,r],void 0!==this.ctx.listener.positionX?(this.ctx.listener.positionX.setTargetAtTime(this._pos[0],Howler.ctx.currentTime,.1),this.ctx.listener.positionY.setTargetAtTime(this._pos[1],Howler.ctx.currentTime,.1),this.ctx.listener.positionZ.setTargetAtTime(this._pos[2],Howler.ctx.currentTime,.1)):this.ctx.listener.setPosition(this._pos[0],this._pos[1],this._pos[2]),this):this},HowlerGlobal.prototype.orientation=function(e,t,r,n,i,o){if(!this.ctx||!this.ctx.listener)return this;var s=this._orientation;return(t="number"!=typeof t?s[1]:t,r="number"!=typeof r?s[2]:r,n="number"!=typeof n?s[3]:n,i="number"!=typeof i?s[4]:i,o="number"!=typeof o?s[5]:o,"number"!=typeof e)?s:(this._orientation=[e,t,r,n,i,o],void 0!==this.ctx.listener.forwardX?(this.ctx.listener.forwardX.setTargetAtTime(e,Howler.ctx.currentTime,.1),this.ctx.listener.forwardY.setTargetAtTime(t,Howler.ctx.currentTime,.1),this.ctx.listener.forwardZ.setTargetAtTime(r,Howler.ctx.currentTime,.1),this.ctx.listener.upX.setTargetAtTime(n,Howler.ctx.currentTime,.1),this.ctx.listener.upY.setTargetAtTime(i,Howler.ctx.currentTime,.1),this.ctx.listener.upZ.setTargetAtTime(o,Howler.ctx.currentTime,.1)):this.ctx.listener.setOrientation(e,t,r,n,i,o),this)},Howl.prototype.init=(e=Howl.prototype.init,function(t){return this._orientation=t.orientation||[1,0,0],this._stereo=t.stereo||null,this._pos=t.pos||null,this._pannerAttr={coneInnerAngle:void 0!==t.coneInnerAngle?t.coneInnerAngle:360,coneOuterAngle:void 0!==t.coneOuterAngle?t.coneOuterAngle:360,coneOuterGain:void 0!==t.coneOuterGain?t.coneOuterGain:0,distanceModel:void 0!==t.distanceModel?t.distanceModel:"inverse",maxDistance:void 0!==t.maxDistance?t.maxDistance:1e4,panningModel:void 0!==t.panningModel?t.panningModel:"HRTF",refDistance:void 0!==t.refDistance?t.refDistance:1,rolloffFactor:void 0!==t.rolloffFactor?t.rolloffFactor:1},this._onstereo=t.onstereo?[{fn:t.onstereo}]:[],this._onpos=t.onpos?[{fn:t.onpos}]:[],this._onorientation=t.onorientation?[{fn:t.onorientation}]:[],e.call(this,t)}),Howl.prototype.stereo=function(e,t){var r=this;if(!r._webAudio)return r;if("loaded"!==r._state)return r._queue.push({event:"stereo",action:function(){r.stereo(e,t)}}),r;var i=void 0===Howler.ctx.createStereoPanner?"spatial":"stereo";if(void 0===t){if("number"!=typeof e)return r._stereo;r._stereo=e,r._pos=[e,0,0]}for(var o=r._getSoundIds(t),s=0;s<o.length;s++){var a=r._soundById(o[s]);if(a){if("number"!=typeof e)return a._stereo;a._stereo=e,a._pos=[e,0,0],a._node&&(a._pannerAttr.panningModel="equalpower",a._panner&&a._panner.pan||n(a,i),"spatial"===i?void 0!==a._panner.positionX?(a._panner.positionX.setValueAtTime(e,Howler.ctx.currentTime),a._panner.positionY.setValueAtTime(0,Howler.ctx.currentTime),a._panner.positionZ.setValueAtTime(0,Howler.ctx.currentTime)):a._panner.setPosition(e,0,0):a._panner.pan.setValueAtTime(e,Howler.ctx.currentTime)),r._emit("stereo",a._id)}}return r},Howl.prototype.pos=function(e,t,r,i){var o=this;if(!o._webAudio)return o;if("loaded"!==o._state)return o._queue.push({event:"pos",action:function(){o.pos(e,t,r,i)}}),o;if(t="number"!=typeof t?0:t,r="number"!=typeof r?-.5:r,void 0===i){if("number"!=typeof e)return o._pos;o._pos=[e,t,r]}for(var s=o._getSoundIds(i),a=0;a<s.length;a++){var l=o._soundById(s[a]);if(l){if("number"!=typeof e)return l._pos;l._pos=[e,t,r],l._node&&((!l._panner||l._panner.pan)&&n(l,"spatial"),void 0!==l._panner.positionX?(l._panner.positionX.setValueAtTime(e,Howler.ctx.currentTime),l._panner.positionY.setValueAtTime(t,Howler.ctx.currentTime),l._panner.positionZ.setValueAtTime(r,Howler.ctx.currentTime)):l._panner.setPosition(e,t,r)),o._emit("pos",l._id)}}return o},Howl.prototype.orientation=function(e,t,r,i){var o=this;if(!o._webAudio)return o;if("loaded"!==o._state)return o._queue.push({event:"orientation",action:function(){o.orientation(e,t,r,i)}}),o;if(t="number"!=typeof t?o._orientation[1]:t,r="number"!=typeof r?o._orientation[2]:r,void 0===i){if("number"!=typeof e)return o._orientation;o._orientation=[e,t,r]}for(var s=o._getSoundIds(i),a=0;a<s.length;a++){var l=o._soundById(s[a]);if(l){if("number"!=typeof e)return l._orientation;l._orientation=[e,t,r],l._node&&(l._panner||(l._pos||(l._pos=o._pos||[0,0,-.5]),n(l,"spatial")),void 0!==l._panner.orientationX?(l._panner.orientationX.setValueAtTime(e,Howler.ctx.currentTime),l._panner.orientationY.setValueAtTime(t,Howler.ctx.currentTime),l._panner.orientationZ.setValueAtTime(r,Howler.ctx.currentTime)):l._panner.setOrientation(e,t,r)),o._emit("orientation",l._id)}}return o},Howl.prototype.pannerAttr=function(){var e,t,r,i=arguments;if(!this._webAudio)return this;if(0===i.length)return this._pannerAttr;if(1===i.length){if("object"!=typeof i[0])return(r=this._soundById(parseInt(i[0],10)))?r._pannerAttr:this._pannerAttr;e=i[0],void 0===t&&(e.pannerAttr||(e.pannerAttr={coneInnerAngle:e.coneInnerAngle,coneOuterAngle:e.coneOuterAngle,coneOuterGain:e.coneOuterGain,distanceModel:e.distanceModel,maxDistance:e.maxDistance,refDistance:e.refDistance,rolloffFactor:e.rolloffFactor,panningModel:e.panningModel}),this._pannerAttr={coneInnerAngle:void 0!==e.pannerAttr.coneInnerAngle?e.pannerAttr.coneInnerAngle:this._coneInnerAngle,coneOuterAngle:void 0!==e.pannerAttr.coneOuterAngle?e.pannerAttr.coneOuterAngle:this._coneOuterAngle,coneOuterGain:void 0!==e.pannerAttr.coneOuterGain?e.pannerAttr.coneOuterGain:this._coneOuterGain,distanceModel:void 0!==e.pannerAttr.distanceModel?e.pannerAttr.distanceModel:this._distanceModel,maxDistance:void 0!==e.pannerAttr.maxDistance?e.pannerAttr.maxDistance:this._maxDistance,refDistance:void 0!==e.pannerAttr.refDistance?e.pannerAttr.refDistance:this._refDistance,rolloffFactor:void 0!==e.pannerAttr.rolloffFactor?e.pannerAttr.rolloffFactor:this._rolloffFactor,panningModel:void 0!==e.pannerAttr.panningModel?e.pannerAttr.panningModel:this._panningModel})}else 2===i.length&&(e=i[0],t=parseInt(i[1],10));for(var o=this._getSoundIds(t),s=0;s<o.length;s++)if(r=this._soundById(o[s])){var a=r._pannerAttr;a={coneInnerAngle:void 0!==e.coneInnerAngle?e.coneInnerAngle:a.coneInnerAngle,coneOuterAngle:void 0!==e.coneOuterAngle?e.coneOuterAngle:a.coneOuterAngle,coneOuterGain:void 0!==e.coneOuterGain?e.coneOuterGain:a.coneOuterGain,distanceModel:void 0!==e.distanceModel?e.distanceModel:a.distanceModel,maxDistance:void 0!==e.maxDistance?e.maxDistance:a.maxDistance,refDistance:void 0!==e.refDistance?e.refDistance:a.refDistance,rolloffFactor:void 0!==e.rolloffFactor?e.rolloffFactor:a.rolloffFactor,panningModel:void 0!==e.panningModel?e.panningModel:a.panningModel};var l=r._panner;l||(r._pos||(r._pos=this._pos||[0,0,-.5]),n(r,"spatial"),l=r._panner),l.coneInnerAngle=a.coneInnerAngle,l.coneOuterAngle=a.coneOuterAngle,l.coneOuterGain=a.coneOuterGain,l.distanceModel=a.distanceModel,l.maxDistance=a.maxDistance,l.refDistance=a.refDistance,l.rolloffFactor=a.rolloffFactor,l.panningModel=a.panningModel}return this},Sound.prototype.init=(t=Sound.prototype.init,function(){var e=this._parent;this._orientation=e._orientation,this._stereo=e._stereo,this._pos=e._pos,this._pannerAttr=e._pannerAttr,t.call(this),this._stereo?e.stereo(this._stereo):this._pos&&e.pos(this._pos[0],this._pos[1],this._pos[2],this._id)}),Sound.prototype.reset=(r=Sound.prototype.reset,function(){var e=this._parent;return this._orientation=e._orientation,this._stereo=e._stereo,this._pos=e._pos,this._pannerAttr=e._pannerAttr,this._stereo?e.stereo(this._stereo):this._pos?e.pos(this._pos[0],this._pos[1],this._pos[2],this._id):this._panner&&(this._panner.disconnect(0),this._panner=void 0,e._refreshBuffer(this)),r.call(this)});var e,t,r,n=function(e,t){"spatial"===(t=t||"spatial")?(e._panner=Howler.ctx.createPanner(),e._panner.coneInnerAngle=e._pannerAttr.coneInnerAngle,e._panner.coneOuterAngle=e._pannerAttr.coneOuterAngle,e._panner.coneOuterGain=e._pannerAttr.coneOuterGain,e._panner.distanceModel=e._pannerAttr.distanceModel,e._panner.maxDistance=e._pannerAttr.maxDistance,e._panner.refDistance=e._pannerAttr.refDistance,e._panner.rolloffFactor=e._pannerAttr.rolloffFactor,e._panner.panningModel=e._pannerAttr.panningModel,void 0!==e._panner.positionX?(e._panner.positionX.setValueAtTime(e._pos[0],Howler.ctx.currentTime),e._panner.positionY.setValueAtTime(e._pos[1],Howler.ctx.currentTime),e._panner.positionZ.setValueAtTime(e._pos[2],Howler.ctx.currentTime)):e._panner.setPosition(e._pos[0],e._pos[1],e._pos[2]),void 0!==e._panner.orientationX?(e._panner.orientationX.setValueAtTime(e._orientation[0],Howler.ctx.currentTime),e._panner.orientationY.setValueAtTime(e._orientation[1],Howler.ctx.currentTime),e._panner.orientationZ.setValueAtTime(e._orientation[2],Howler.ctx.currentTime)):e._panner.setOrientation(e._orientation[0],e._orientation[1],e._orientation[2])):(e._panner=Howler.ctx.createStereoPanner(),e._panner.pan.setValueAtTime(e._stereo,Howler.ctx.currentTime)),e._panner.connect(e._node),e._paused||e._parent.pause(e._id,!0).play(e._id,!0)}}()},2638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ErrorCode:()=>l,FormatError:()=>eo,IntlMessageFormat:()=>ed,InvalidValueError:()=>es,InvalidValueTypeError:()=>ea,MissingValueError:()=>el,PART_TYPE:()=>u,default:()=>ef,formatToParts:()=>eh,isFormatXMLElementFn:()=>eu});var n,i,o,s,a,l,u,h=function(e,t){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function c(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}h(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var d=function(){return(d=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function f(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create;var p=("function"==typeof SuppressedError&&SuppressedError,r(8913));function m(e){return e.type===i.literal}function g(e){return e.type===i.number}function v(e){return e.type===i.date}function y(e){return e.type===i.time}function _(e){return e.type===i.select}function b(e){return e.type===i.plural}function E(e){return e.type===i.tag}function A(e){return!!(e&&"object"==typeof e&&e.type===o.number)}function T(e){return!!(e&&"object"==typeof e&&e.type===o.dateTime)}(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(i||(i={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(o||(o={}));var S=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,P=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,w=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,x=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,M=/^(@+)?(\+|#+)?[rs]?$/g,I=/(\*)(0+)|(#+)(0+)|(0+)/g,H=/^(0+)$/;function L(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(M,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function B(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function C(e){return B(e)||{}}var R={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},O=new RegExp("^".concat(S.source,"*")),N=new RegExp("".concat(S.source,"*$"));function D(e,t){return{start:e,end:t}}var k=!!String.prototype.startsWith&&"_a".startsWith("a",1),F=!!String.fromCodePoint,V=!!Object.fromEntries,U=!!String.prototype.codePointAt,G=!!String.prototype.trimStart,j=!!String.prototype.trimEnd,W=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&9007199254740991>=Math.abs(e)},X=!0;try{var Z=J("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");X=(null===(s=Z.exec("a"))||void 0===s?void 0:s[0])==="a"}catch(e){X=!1}var z=k?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},$=F?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,o=0;i>o;){if((e=t[o++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},Y=V?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],i=n[0],o=n[1];t[i]=o}return t},q=U?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:(i-55296<<10)+(r-56320)+65536}},K=G?function(e){return e.trimStart()}:function(e){return e.replace(O,"")},Q=j?function(e){return e.trimEnd()}:function(e){return e.replace(N,"")};function J(e,t){return new RegExp(e,t)}if(X){var ee=J("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");a=function(e,t){var r;return ee.lastIndex=t,null!==(r=ee.exec(e)[1])&&void 0!==r?r:""}}else a=function(e,t){for(var r=[];;){var n,i=q(e,t);if(void 0===i||en(i)||(n=i)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(i),t+=i>=65536?2:1}return $.apply(void 0,r)};var et=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var o=[];!this.isEOF();){var s=this.char();if(123===s){var a=this.parseArgument(e,r);if(a.err)return a;o.push(a.val)}else if(125===s&&e>0)break;else if(35===s&&("plural"===t||"selectordinal"===t)){var l=this.clonePosition();this.bump(),o.push({type:i.pound,location:D(l,this.clonePosition())})}else if(60!==s||this.ignoreTag||47!==this.peek()){if(60===s&&!this.ignoreTag&&er(this.peek()||0)){var a=this.parseTag(e,t);if(a.err)return a;o.push(a.val)}else{var a=this.parseLiteral(e,t);if(a.err)return a;o.push(a.val)}}else{if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,D(this.clonePosition(),this.clonePosition()));break}}return{val:o,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<".concat(o,"/>"),location:D(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,D(r,this.clonePosition()));var s=this.parseMessage(e+1,t,!0);if(s.err)return s;var a=s.val,l=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,D(r,this.clonePosition()));if(this.isEOF()||!er(this.char()))return this.error(n.INVALID_TAG,D(l,this.clonePosition()));var u=this.clonePosition();return o!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,D(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:i.tag,value:o,children:a,location:D(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,D(l,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var o=this.tryParseQuote(t);if(o){n+=o;continue}var s=this.tryParseUnquoted(e,t);if(s){n+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){n+=a;continue}break}var l=D(r,this.clonePosition());return{val:{type:i.literal,value:n,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(er(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r){if(39===this.peek())t.push(39),this.bump();else{this.bump();break}}else t.push(r);this.bump()}return $.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),$(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,D(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(n.MALFORMED_ARGUMENT,D(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:o,location:D(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));return this.parseArgumentOptions(e,t,o,r);default:return this.error(n.MALFORMED_ARGUMENT,D(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=a(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:D(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,s){var a,l=this.clonePosition(),u=this.parseIdentifierIfPossible().value,h=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,D(l,h));case"number":case"date":case"time":this.bumpSpace();var c=null;if(this.bumpIf(",")){this.bumpSpace();var f=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=Q(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,D(this.clonePosition(),this.clonePosition()));c={style:m,styleLocation:D(f,this.clonePosition())}}var g=this.tryParseArgumentClose(s);if(g.err)return g;var v=D(s,this.clonePosition());if(c&&z(null==c?void 0:c.style,"::",0)){var y,_=K(c.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(_,c.styleLocation);if(p.err)return p;return{val:{type:i.number,value:r,location:v,style:p.val},err:null}}if(0===_.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,v);var b=_;this.locale&&(b=function(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if("j"===i){for(var o=0;n+1<e.length&&e.charAt(n+1)===i;)o++,n++;var s=1+(1&o),a=o<2?1:3+(o>>1),l=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(R[t||""]||R[n||""]||R["".concat(n,"-001")]||R["001"])[0]}(t);for(("H"==l||"k"==l)&&(a=0);a-- >0;)r+="a";for(;s-- >0;)r=l+r}else"J"===i?r+="H":r+=i}return r}(_,this.locale));var m={type:o.dateTime,pattern:b,location:c.styleLocation,parsedOptions:this.shouldParseSkeletons?(y={},b.replace(P,function(e){var t=e.length;switch(e[0]){case"G":y.era=4===t?"long":5===t?"narrow":"short";break;case"y":y.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":y.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":y.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":y.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"a":y.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":y.hourCycle="h12",y.hour=["numeric","2-digit"][t-1];break;case"H":y.hourCycle="h23",y.hour=["numeric","2-digit"][t-1];break;case"K":y.hourCycle="h11",y.hour=["numeric","2-digit"][t-1];break;case"k":y.hourCycle="h24",y.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":y.minute=["numeric","2-digit"][t-1];break;case"s":y.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":y.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),y):{}};return{val:{type:"date"===u?i.date:i.time,value:r,location:v,style:m},err:null}}return{val:{type:"number"===u?i.number:"date"===u?i.date:i.time,value:r,location:v,style:null!==(a=null==c?void 0:c.style)&&void 0!==a?a:null},err:null};case"plural":case"selectordinal":case"select":var E=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,D(E,d({},E)));this.bumpSpace();var A=this.parseIdentifierIfPossible(),T=0;if("select"!==u&&"offset"===A.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,D(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),A=this.parseIdentifierIfPossible(),T=p.val}var S=this.tryParsePluralOrSelectOptions(e,u,t,A);if(S.err)return S;var g=this.tryParseArgumentClose(s);if(g.err)return g;var w=D(s,this.clonePosition());if("select"===u)return{val:{type:i.select,value:r,options:Y(S.val),location:w},err:null};return{val:{type:i.plural,value:r,options:Y(S.val),offset:T,pluralType:"plural"===u?"cardinal":"ordinal",location:w},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,D(l,h))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,D(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(w).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var i=t[n].split("/");if(0===i.length)throw Error("Invalid number skeleton");for(var o=i[0],s=i.slice(1),a=0;a<s.length;a++)if(0===s[a].length)throw Error("Invalid number skeleton");r.push({stem:o,options:s})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:o.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=d(d(d({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return d(d({},e),B(t)||{})},{}));continue;case"engineering":t=d(d(d({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return d(d({},e),B(t)||{})},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(I,function(e,r,n,i,o,s){if(r)t.minimumIntegerDigits=n.length;else if(i&&o)throw Error("We currently do not support maximum integer digits");else if(s)throw Error("We currently do not support exact integer digits");return""});continue}if(H.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(x.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(x,function(e,r,n,i,o,s){return"*"===n?t.minimumFractionDigits=r.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&s?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+s.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var i=n.options[0];"w"===i?t=d(d({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=d(d({},t),L(i)));continue}if(M.test(n.stem)){t=d(d({},t),L(n.stem));continue}var o=B(n.stem);o&&(t=d(d({},t),o));var s=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!H.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);s&&(t=d(d({},t),s))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,i){for(var o,s=!1,a=[],l=new Set,u=i.value,h=i.location;;){if(0===u.length){var c=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;h=D(c,this.clonePosition()),u=this.message.slice(c.offset,this.offset())}else break}if(l.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,h);"other"===u&&(s=!0),this.bumpSpace();var f=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,D(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(f);if(m.err)return m;a.push([u,{value:p.val,location:D(f,this.clonePosition())}]),l.add(u),this.bumpSpace(),u=(o=this.parseIdentifierIfPossible()).value,h=o.location}return 0===a.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,D(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(n.MISSING_OTHER_CLAUSE,D(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,o=0;!this.isEOF();){var s=this.char();if(s>=48&&s<=57)i=!0,o=10*o+(s-48),this.bump();else break}var a=D(n,this.clonePosition());return i?W(o*=r)?{val:o,err:null}:this.error(t,a):this.error(e,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=q(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(z(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&en(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function er(e){return e>=97&&e<=122||e>=65&&e<=90}function en(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function ei(e,t){void 0===t&&(t={});var r=new et(e,t=d({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var i=SyntaxError(n[r.err.kind]);throw i.location=r.err.location,i.originalMessage=r.err.message,i}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,_(t)||b(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else g(t)&&A(t.style)?delete t.style.location:(v(t)||y(t))&&T(t.style)?delete t.style.location:E(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(l||(l={}));var eo=function(e){function t(t,r,n){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=n,i}return c(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),es=function(e){function t(t,r,n,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),l.INVALID_VALUE,i)||this}return c(t,e),t}(eo),ea=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),l.INVALID_VALUE,n)||this}return c(t,e),t}(eo),el=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),l.MISSING_VALUE,r)||this}return c(t,e),t}(eo);function eu(e){return"function"==typeof e}function eh(e,t,r,n,o,s,a){if(1===e.length&&m(e[0]))return[{type:u.literal,value:e[0].value}];for(var h=[],c=0;c<e.length;c++){var d=e[c];if(m(d)){h.push({type:u.literal,value:d.value});continue}if(d.type===i.pound){"number"==typeof s&&h.push({type:u.literal,value:r.getNumberFormat(t).format(s)});continue}var f=d.value;if(!(o&&f in o))throw new el(f,a);var p=o[f];if(d.type===i.argument){p&&"string"!=typeof p&&"number"!=typeof p||(p="string"==typeof p||"number"==typeof p?String(p):""),h.push({type:"string"==typeof p?u.literal:u.object,value:p});continue}if(v(d)){var S="string"==typeof d.style?n.date[d.style]:T(d.style)?d.style.parsedOptions:void 0;h.push({type:u.literal,value:r.getDateTimeFormat(t,S).format(p)});continue}if(y(d)){var S="string"==typeof d.style?n.time[d.style]:T(d.style)?d.style.parsedOptions:n.time.medium;h.push({type:u.literal,value:r.getDateTimeFormat(t,S).format(p)});continue}if(g(d)){var S="string"==typeof d.style?n.number[d.style]:A(d.style)?d.style.parsedOptions:void 0;S&&S.scale&&(p*=S.scale||1),h.push({type:u.literal,value:r.getNumberFormat(t,S).format(p)});continue}if(E(d)){var P=d.children,w=d.value,x=o[w];if(!eu(x))throw new ea(w,"function",a);var M=x(eh(P,t,r,n,o,s).map(function(e){return e.value}));Array.isArray(M)||(M=[M]),h.push.apply(h,M.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(_(d)){var I=d.options[p]||d.options.other;if(!I)throw new es(d.value,p,Object.keys(d.options),a);h.push.apply(h,eh(I.value,t,r,n,o));continue}if(b(d)){var I=d.options["=".concat(p)];if(!I){if(!Intl.PluralRules)throw new eo('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',l.MISSING_INTL_API,a);var H=r.getPluralRules(t,{type:d.pluralType}).select(p-(d.offset||0));I=d.options[H]||d.options.other}if(!I)throw new es(d.value,p,Object.keys(d.options),a);h.push.apply(h,eh(I.value,t,r,n,o,p-(d.offset||0)));continue}}return h.length<2?h:h.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}function ec(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var ed=function(){function e(t,r,n,i){void 0===r&&(r=e.defaultLocale);var o,s,a=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=a.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return eh(a.ast,a.locales,a.formatters,a.formats,e,void 0,a.message)},this.resolvedOptions=function(){var e;return{locale:(null===(e=a.resolvedLocale)||void 0===e?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(a.locales)[0]}},this.getAst=function(){return a.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var l=i||{},h=(l.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}(l,["formatters"]));this.ast=e.__parse(t,d(d({},h),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(o=e.formats,n?Object.keys(o).reduce(function(e,t){var r,i;return e[t]=(r=o[t],(i=n[t])?d(d(d({},r||{}),i||{}),Object.keys(r).reduce(function(e,t){return e[t]=d(d({},r[t]),i[t]||{}),e},{})):r),e},d({},o)):o),this.formatters=i&&i.formatters||(void 0===(s=this.formatterCache)&&(s={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,f([void 0],t,!1)))},{cache:ec(s.number),strategy:p.strategies.variadic}),getDateTimeFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,f([void 0],t,!1)))},{cache:ec(s.dateTime),strategy:p.strategies.variadic}),getPluralRules:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,f([void 0],t,!1)))},{cache:ec(s.pluralRules),strategy:p.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=ei,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ef=ed},3170:(e,t,r)=>{"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{default:()=>a});var i=r(7577),o=r.n(i),s=r(776);function a(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return o().createElement(s.IntlProvider,n({locale:t},r))}},2167:(e,t)=>{"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}Object.defineProperty(t,"__esModule",{value:!0}),t.extends=r},3844:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(3378),i=r(7916),o=r(397),s=r(8119);t.useFormatter=n.useFormatter,t.useTranslations=n.useTranslations,t.useLocale=i.default,t.NextIntlClientProvider=o.default,Object.keys(s).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}})})},3378:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(8119);function i(e,t){return function(){try{return t(...arguments)}catch(e){throw Error(void 0)}}}let o=i(0,n.useTranslations),s=i(0,n.useFormatter);t.useFormatter=s,t.useTranslations=o,Object.keys(n).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}})})},7916:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(5047),i=r(3015),o=r(9237);t.default=function(){let e;let t=n.useParams();try{e=i.useLocale()}catch(r){if("string"!=typeof(null==t?void 0:t[o.LOCALE_SEGMENT_NAME]))throw r;e=t[o.LOCALE_SEGMENT_NAME]}return e}},397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(2167),i=r(7577),o=r(1669),s=function(e){return e&&e.__esModule?e:{default:e}}(i);t.default=function(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return s.default.createElement(o.IntlProvider,n.extends({locale:t},r))}},9237:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HEADER_LOCALE_NAME="X-NEXT-INTL-LOCALE",t.LOCALE_SEGMENT_NAME="locale"},6931:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var n=r(4064),i=r.n(n)},9374:(e,t)=>{"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DOMAttributeNames:function(){return n},default:function(){return s},isEqualNode:function(){return o}});let n={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function i(e){let{type:t,props:r}=e,i=document.createElement(t);for(let e in r){if(!r.hasOwnProperty(e)||"children"===e||"dangerouslySetInnerHTML"===e||void 0===r[e])continue;let o=n[e]||e.toLowerCase();"script"===t&&("async"===o||"defer"===o||"noModule"===o)?i[o]=!!r[e]:i.setAttribute(o,r[e])}let{children:o,dangerouslySetInnerHTML:s}=r;return s?i.innerHTML=s.__html||"":o&&(i.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):""),i}function o(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let r=t.getAttribute("nonce");if(r&&!e.getAttribute("nonce")){let n=t.cloneNode(!0);return n.setAttribute("nonce",""),n.nonce=r,r===e.nonce&&e.isEqualNode(n)}}return e.isEqualNode(t)}function s(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let r=t[e.type]||[];r.push(e),t[e.type]=r});let n=t.title?t.title[0]:null,i="";if(n){let{children:e}=n.props;i="string"==typeof e?e:Array.isArray(e)?e.join(""):""}i!==document.title&&(document.title=i),["meta","base","link","style","script"].forEach(e=>{r(e,t[e]||[])})}}}r=(e,t)=>{let r=document.getElementsByTagName("head")[0],n=r.querySelector("meta[name=next-head-count]"),s=Number(n.content),a=[];for(let t=0,r=n.previousElementSibling;t<s;t++,r=(null==r?void 0:r.previousElementSibling)||null){var l;(null==r?void 0:null==(l=r.tagName)?void 0:l.toLowerCase())===e&&a.push(r)}let u=t.map(i).filter(e=>{for(let t=0,r=a.length;t<r;t++)if(o(a[t],e))return a.splice(t,1),!1;return!0});a.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),u.forEach(e=>r.insertBefore(e,n)),n.content=(s-a.length+u.length).toString()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4064:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return _},handleClientScriptLoad:function(){return g},initScriptLoader:function(){return v}});let n=r(1174),i=r(8374),o=r(326),s=n._(r(962)),a=i._(r(7577)),l=r(1157),u=r(9374),h=r(956),c=new Map,d=new Set,f=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],p=e=>{if(s.default.preinit){e.forEach(e=>{s.default.preinit(e,{as:"style"})});return}},m=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:i=null,dangerouslySetInnerHTML:o,children:s="",strategy:a="afterInteractive",onError:l,stylesheets:h}=e,m=r||t;if(m&&d.has(m))return;if(c.has(t)){d.add(m),c.get(t).then(n,l);return}let g=()=>{i&&i(),d.add(m)},v=document.createElement("script"),y=new Promise((e,t)=>{v.addEventListener("load",function(t){e(),n&&n.call(this,t),g()}),v.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});for(let[r,n]of(o?(v.innerHTML=o.__html||"",g()):s?(v.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):"",g()):t&&(v.src=t,c.set(t,y)),Object.entries(e))){if(void 0===n||f.includes(r))continue;let e=u.DOMAttributeNames[r]||r.toLowerCase();v.setAttribute(e,n)}"worker"===a&&v.setAttribute("type","text/partytown"),v.setAttribute("data-nscript",a),h&&p(h),document.body.appendChild(v)};function g(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,h.requestIdleCallback)(()=>m(e))}):m(e)}function v(e){e.forEach(g),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");d.add(t)})}function y(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:i=null,strategy:u="afterInteractive",onError:c,stylesheets:f,...p}=e,{updateScripts:g,scripts:v,getIsSsr:y,appDir:_,nonce:b}=(0,a.useContext)(l.HeadManagerContext),E=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=t||r;E.current||(i&&e&&d.has(e)&&i(),E.current=!0)},[i,t,r]);let A=(0,a.useRef)(!1);if((0,a.useEffect)(()=>{!A.current&&("afterInteractive"===u?m(e):"lazyOnload"===u&&("complete"===document.readyState?(0,h.requestIdleCallback)(()=>m(e)):window.addEventListener("load",()=>{(0,h.requestIdleCallback)(()=>m(e))})),A.current=!0)},[e,u]),("beforeInteractive"===u||"worker"===u)&&(g?(v[u]=(v[u]||[]).concat([{id:t,src:r,onLoad:n,onReady:i,onError:c,...p}]),g(v)):y&&y()?d.add(t||r):y&&!y()&&m(e)),_){if(f&&f.forEach(e=>{s.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)return r?(s.default.preload(r,p.integrity?{as:"script",integrity:p.integrity,nonce:b,crossOrigin:p.crossOrigin}:{as:"script",nonce:b,crossOrigin:p.crossOrigin}),(0,o.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...p,id:t}])+")"}})):(p.dangerouslySetInnerHTML&&(p.children=p.dangerouslySetInnerHTML.__html,delete p.dangerouslySetInnerHTML),(0,o.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...p,id:t}])+")"}}));"afterInteractive"===u&&r&&s.default.preload(r,p.integrity?{as:"script",integrity:p.integrity,nonce:b,crossOrigin:p.crossOrigin}:{as:"script",nonce:b,crossOrigin:p.crossOrigin})}return null}Object.defineProperty(y,"__nextScript",{value:!0});let _=y;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1157:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.HeadManagerContext},1669:(e,t,r)=>{"use strict";e.exports=r(776)},3015:(e,t,r)=>{"use strict";e.exports=r(9471)},8119:(e,t,r)=>{"use strict";e.exports=r(5948)},1695:(e,t,r)=>{"use strict";let n=r(7577).createContext(void 0);t.IntlContext=n},776:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(7577),i=r(6118),o=r(1695);r(8913);var s=function(e){return e&&e.__esModule?e:{default:e}}(n);t.IntlProvider=function(e){let{children:t,defaultTranslationValues:r,formats:a,getMessageFallback:l,locale:u,messages:h,now:c,onError:d,timeZone:f}=e,p=n.useMemo(()=>i.createCache(),[u]),m=n.useMemo(()=>i.createIntlFormatters(p),[p]),g=n.useMemo(()=>({...i.initializeConfig({locale:u,defaultTranslationValues:r,formats:a,getMessageFallback:l,messages:h,now:c,onError:d,timeZone:f}),formatters:m,cache:p}),[p,r,a,m,l,u,h,c,d,f]);return s.default.createElement(o.IntlContext.Provider,{value:g},t)}},9422:(e,t,r)=>{"use strict";var n=r(7577),i=r(1695);function o(){let e=n.useContext(i.IntlContext);if(!e)throw Error(void 0);return e}t.useIntlContext=o,t.useLocale=function(){return o().locale}},9471:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(9422);r(7577),r(1695),t.useLocale=n.useLocale},1153:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(2729),i=r(6118);r(2638),r(7577),r(8913),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t._createCache=i.createCache,t._createIntlFormatters=i.createIntlFormatters,t.initializeConfig=i.initializeConfig,t.createTranslator=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),getMessageFallback:o=i.defaultGetMessageFallback,messages:s,namespace:a,onError:l=i.defaultOnError,...u}=e;return function(e,t){let{messages:r,namespace:i,...o}=e;return r=r["!"],i=n.resolveNamespace(i,"!"),n.createBaseTranslator({...o,messages:r,namespace:i})}({...u,onError:l,cache:t,formatters:r,getMessageFallback:o,messages:{"!":s},namespace:a?"!.".concat(a):"!"},0)}},2729:(e,t,r)=>{"use strict";var n=r(2638),i=r(7577),o=r(6118),s=function(e){return e&&e.__esModule?e:{default:e}}(n);function a(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let l=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class u extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),a(this,"code",void 0),a(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function h(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function c(e,t,r,n){let i=o.joinPath(n,r);if(!t)throw Error(i);let s=t;return r.split(".").forEach(t=>{let r=s[t];if(null==t||null==r)throw Error(i+" (".concat(e,")"));s=r}),s}let d=365/12*86400,f={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=u,t.IntlErrorCode=l,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:o.defaultOnError;try{if(!t)throw Error(void 0);let n=r?c(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new u(l.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:a,getMessageFallback:d=o.defaultGetMessageFallback,locale:f,messagesOrError:p,namespace:m,onError:g,timeZone:v}=e,y=p instanceof u;function _(e,t,r){let n=new u(t,r);return g(n),d({error:n,key:e,namespace:m})}function b(e,u,g){let b,E;if(y)return d({error:p,key:e,namespace:m});try{b=c(f,p,e,m)}catch(t){return _(e,l.MISSING_MESSAGE,t.message)}if("object"==typeof b){let t;return _(e,Array.isArray(b)?l.INVALID_MESSAGE:l.INSUFFICIENT_PATH,t)}let A=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(b,u);if(A)return A;a.getMessageFormat||(a.getMessageFormat=o.memoFn(function(){return new s.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:a,...arguments.length<=3?void 0:arguments[3]})},t.message));try{E=a.getMessageFormat(b,f,function(e,t){let r=t?{...e,dateTime:h(e.dateTime,t)}:e,n=s.default.formats.date,i=t?h(n,t):n,o=s.default.formats.time,a=t?h(o,t):o;return{...r,date:{...i,...r.dateTime},time:{...a,...r.dateTime}}}({...n,...g},v),{formatters:{...a,getDateTimeFormat:(e,t)=>a.getDateTimeFormat(e,{timeZone:v,...t})}})}catch(t){return _(e,l.INVALID_MESSAGE,t.message)}try{let e=E.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,o=0,s=e[r];n="function"==typeof s?e=>{let t=s(e);return i.isValidElement(t)?i.cloneElement(t,{key:r+o++}):t}:s,t[r]=n}),t}({...r,...u}));if(null==e)throw Error(void 0);return i.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return _(e,l.FORMATTING_ERROR,t.message)}}function E(e,t,r){let n=b(e,t,r);return"string"!=typeof n?_(e,l.INVALID_MESSAGE,void 0):n}return E.rich=b,E.markup=(e,t,r)=>{let n=b(e,t,r);if("string"!=typeof n){let t=new u(l.FORMATTING_ERROR,void 0);return g(t),d({error:t,key:e,namespace:m})}return n},E.raw=e=>{if(y)return d({error:p,key:e,namespace:m});try{return c(f,p,e,m)}catch(t){return _(e,l.MISSING_MESSAGE,t.message)}},E.has=e=>{if(y)return!1;try{return c(f,p,e,m),!0}catch(e){return!1}},E}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),formats:n,locale:i,now:s,onError:a=o.defaultOnError,timeZone:h}=e;function c(e){var t;return null!==(t=e)&&void 0!==t&&t.timeZone||(h?e={...e,timeZone:h}:a(new u(l.ENVIRONMENT_FALLBACK,void 0))),e}function p(e,t,r,n){let i;try{i=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new u(l.MISSING_FORMAT,void 0);throw a(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(i)}catch(e){return a(new u(l.FORMATTING_ERROR,e.message)),n()}}function m(e,t){return p(t,null==n?void 0:n.dateTime,t=>(t=c(t),r.getDateTimeFormat(i,t).format(e)),()=>String(e))}function g(){return s||(a(new u(l.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:m,number:function(e,t){return p(t,null==n?void 0:n.number,t=>r.getNumberFormat(i,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let o,s;let a={};t instanceof Date||"number"==typeof t?o=new Date(t):t&&(o=null!=t.now?new Date(t.now):g(),s=t.unit,a.style=t.style,a.numberingSystem=t.numberingSystem),o||(o=g());let l=(new Date(e).getTime()-o.getTime())/1e3;s||(s=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<d?"week":t<31536e3?"month":"year"}(l)),a.numeric="second"===s?"auto":"always";let u=(n=s,Math.round(l/f[n]));return r.getRelativeTimeFormat(i,a).format(u,s)}catch(t){return a(new u(l.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let o=[],s=new Map,a=0;for(let t of e){let e;"object"==typeof t?(e=String(a),s.set(e,t)):e=String(t),o.push(e),a++}return p(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(i,e).formatToParts(o).map(e=>"literal"===e.type?e.value:s.get(e.value)||e.value);return s.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,o){return p(o,null==n?void 0:n.dateTime,n=>(n=c(n),r.getDateTimeFormat(i,n).formatRange(e,t)),()=>[m(e),m(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},5948:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(2729),i=r(1153),o=r(6118),s=r(776),a=r(5965),l=r(9422);r(2638),r(7577),r(8913),r(1695),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t.createTranslator=i.createTranslator,t._createCache=o.createCache,t._createIntlFormatters=o.createIntlFormatters,t.initializeConfig=o.initializeConfig,t.IntlProvider=s.IntlProvider,t.useFormatter=a.useFormatter,t.useMessages=a.useMessages,t.useNow=a.useNow,t.useTimeZone=a.useTimeZone,t.useTranslations=a.useTranslations,t.useLocale=l.useLocale},6118:(e,t,r)=>{"use strict";var n=r(8913);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function o(e){return i(e.namespace,e.key)}function s(e){console.error(e)}function a(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function l(e,t){return a(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:l(Intl.DateTimeFormat,e.dateTime),getNumberFormat:l(Intl.NumberFormat,e.number),getPluralRules:l(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:l(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:l(Intl.ListFormat,e.list),getDisplayNames:l(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=o,t.defaultOnError=s,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...i}=e;return{...i,messages:r,onError:n||s,getMessageFallback:t||o}},t.joinPath=i,t.memoFn=a},5965:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(776),i=r(9422),o=r(7577),s=r(2729);r(6118),r(8913),r(1695),r(2638);let a=!1,l="undefined"==typeof window;t.IntlProvider=n.IntlProvider,t.useLocale=i.useLocale,t.useFormatter=function(){let{formats:e,formatters:t,locale:r,now:n,onError:a,timeZone:l}=i.useIntlContext();return o.useMemo(()=>s.createFormatter({formats:e,locale:r,now:n,onError:a,timeZone:l,_formatters:t}),[e,t,n,r,a,l])},t.useMessages=function(){let e=i.useIntlContext();if(!e.messages)throw Error(void 0);return e.messages},t.useNow=function(e){let t=null==e?void 0:e.updateInterval,{now:r}=i.useIntlContext(),[n,s]=o.useState(r||new Date);return o.useEffect(()=>{if(!t)return;let e=setInterval(()=>{s(new Date)},t);return()=>{clearInterval(e)}},[r,t]),null==t&&r?r:n},t.useTimeZone=function(){return i.useIntlContext().timeZone},t.useTranslations=function(e){return function(e,t,r){let{cache:n,defaultTranslationValues:u,formats:h,formatters:c,getMessageFallback:d,locale:f,onError:p,timeZone:m}=i.useIntlContext(),g=e["!"],v=s.resolveNamespace(t,"!");return m||a||!l||(a=!0,p(new s.IntlError(s.IntlErrorCode.ENVIRONMENT_FALLBACK,void 0))),o.useMemo(()=>s.createBaseTranslator({cache:n,formatters:c,getMessageFallback:d,messages:g,defaultTranslationValues:u,namespace:v,onError:p,formats:h,locale:f,timeZone:m}),[n,c,d,g,u,v,p,h,f,m])}({"!":i.useIntlContext().messages},e?"!.".concat(e):"!",0)}},5442:(e,t,r)=>{"use strict";var n=r(7577),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,s=n.useEffect,a=n.useLayoutEffect,l=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var h="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),i=n[0].inst,h=n[1];return a(function(){i.value=r,i.getSnapshot=t,u(i)&&h({inst:i})},[e,r,t]),s(function(){return u(i)&&h({inst:i}),e(function(){u(i)&&h({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:h},9251:(e,t,r)=>{"use strict";var n=r(7577),i=r(4095),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=i.useSyncExternalStore,a=n.useRef,l=n.useEffect,u=n.useMemo,h=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var c=a(null);if(null===c.current){var d={hasValue:!1,value:null};c.current=d}else d=c.current;var f=s(e,(c=u(function(){function e(e){if(!l){if(l=!0,s=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return a=t}return a=e}if(t=a,o(s,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(s=e,t):(s=e,a=r)}var s,a,l=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,i]))[0],c[1]);return l(function(){d.hasValue=!0,d.value=f},[f]),h(f),f}},4095:(e,t,r)=>{"use strict";e.exports=r(5442)},1508:(e,t,r)=>{"use strict";e.exports=r(9251)},8995:(e,t,r)=>{"use strict";function n(e,t){var r=t&&t.cache?t.cache:l,n=t&&t.serializer?t.serializer:s;return(t&&t.strategy?t.strategy:function(e,t){var r,n,s=1===e.length?i:o;return r=t.cache.create(),n=t.serializer,s.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function i(e,t,r,n){var i=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),o=t.get(i);return void 0===o&&(o=e.call(this,n),t.set(i,o)),o}function o(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),o=t.get(i);return void 0===o&&(o=e.apply(this,n),t.set(i,o)),o}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>u});var s=function(){return JSON.stringify(arguments)},a=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),l={create:function(){return new a}},u={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)}}},5544:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ErrorCode:()=>l,FormatError:()=>eo,IntlMessageFormat:()=>ed,InvalidValueError:()=>es,InvalidValueTypeError:()=>ea,MissingValueError:()=>el,PART_TYPE:()=>u,default:()=>ef,formatToParts:()=>eh,isFormatXMLElementFn:()=>eu});var n,i,o,s,a,l,u,h=function(e,t){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function c(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}h(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var d=function(){return(d=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function f(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create;var p=("function"==typeof SuppressedError&&SuppressedError,r(8995));function m(e){return e.type===i.literal}function g(e){return e.type===i.number}function v(e){return e.type===i.date}function y(e){return e.type===i.time}function _(e){return e.type===i.select}function b(e){return e.type===i.plural}function E(e){return e.type===i.tag}function A(e){return!!(e&&"object"==typeof e&&e.type===o.number)}function T(e){return!!(e&&"object"==typeof e&&e.type===o.dateTime)}(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(i||(i={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(o||(o={}));var S=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,P=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,w=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,x=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,M=/^(@+)?(\+|#+)?[rs]?$/g,I=/(\*)(0+)|(#+)(0+)|(0+)/g,H=/^(0+)$/;function L(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(M,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function B(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function C(e){return B(e)||{}}var R={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},O=new RegExp("^".concat(S.source,"*")),N=new RegExp("".concat(S.source,"*$"));function D(e,t){return{start:e,end:t}}var k=!!String.prototype.startsWith&&"_a".startsWith("a",1),F=!!String.fromCodePoint,V=!!Object.fromEntries,U=!!String.prototype.codePointAt,G=!!String.prototype.trimStart,j=!!String.prototype.trimEnd,W=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&9007199254740991>=Math.abs(e)},X=!0;try{var Z=J("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");X=(null===(s=Z.exec("a"))||void 0===s?void 0:s[0])==="a"}catch(e){X=!1}var z=k?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},$=F?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,o=0;i>o;){if((e=t[o++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},Y=V?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],i=n[0],o=n[1];t[i]=o}return t},q=U?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:(i-55296<<10)+(r-56320)+65536}},K=G?function(e){return e.trimStart()}:function(e){return e.replace(O,"")},Q=j?function(e){return e.trimEnd()}:function(e){return e.replace(N,"")};function J(e,t){return new RegExp(e,t)}if(X){var ee=J("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");a=function(e,t){var r;return ee.lastIndex=t,null!==(r=ee.exec(e)[1])&&void 0!==r?r:""}}else a=function(e,t){for(var r=[];;){var n,i=q(e,t);if(void 0===i||en(i)||(n=i)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(i),t+=i>=65536?2:1}return $.apply(void 0,r)};var et=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var o=[];!this.isEOF();){var s=this.char();if(123===s){var a=this.parseArgument(e,r);if(a.err)return a;o.push(a.val)}else if(125===s&&e>0)break;else if(35===s&&("plural"===t||"selectordinal"===t)){var l=this.clonePosition();this.bump(),o.push({type:i.pound,location:D(l,this.clonePosition())})}else if(60!==s||this.ignoreTag||47!==this.peek()){if(60===s&&!this.ignoreTag&&er(this.peek()||0)){var a=this.parseTag(e,t);if(a.err)return a;o.push(a.val)}else{var a=this.parseLiteral(e,t);if(a.err)return a;o.push(a.val)}}else{if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,D(this.clonePosition(),this.clonePosition()));break}}return{val:o,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<".concat(o,"/>"),location:D(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,D(r,this.clonePosition()));var s=this.parseMessage(e+1,t,!0);if(s.err)return s;var a=s.val,l=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,D(r,this.clonePosition()));if(this.isEOF()||!er(this.char()))return this.error(n.INVALID_TAG,D(l,this.clonePosition()));var u=this.clonePosition();return o!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,D(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:i.tag,value:o,children:a,location:D(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,D(l,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var o=this.tryParseQuote(t);if(o){n+=o;continue}var s=this.tryParseUnquoted(e,t);if(s){n+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){n+=a;continue}break}var l=D(r,this.clonePosition());return{val:{type:i.literal,value:n,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(er(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r){if(39===this.peek())t.push(39),this.bump();else{this.bump();break}}else t.push(r);this.bump()}return $.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),$(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,D(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(n.MALFORMED_ARGUMENT,D(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:o,location:D(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));return this.parseArgumentOptions(e,t,o,r);default:return this.error(n.MALFORMED_ARGUMENT,D(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=a(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:D(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,s){var a,l=this.clonePosition(),u=this.parseIdentifierIfPossible().value,h=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,D(l,h));case"number":case"date":case"time":this.bumpSpace();var c=null;if(this.bumpIf(",")){this.bumpSpace();var f=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=Q(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,D(this.clonePosition(),this.clonePosition()));c={style:m,styleLocation:D(f,this.clonePosition())}}var g=this.tryParseArgumentClose(s);if(g.err)return g;var v=D(s,this.clonePosition());if(c&&z(null==c?void 0:c.style,"::",0)){var y,_=K(c.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(_,c.styleLocation);if(p.err)return p;return{val:{type:i.number,value:r,location:v,style:p.val},err:null}}if(0===_.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,v);var b=_;this.locale&&(b=function(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if("j"===i){for(var o=0;n+1<e.length&&e.charAt(n+1)===i;)o++,n++;var s=1+(1&o),a=o<2?1:3+(o>>1),l=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(R[t||""]||R[n||""]||R["".concat(n,"-001")]||R["001"])[0]}(t);for(("H"==l||"k"==l)&&(a=0);a-- >0;)r+="a";for(;s-- >0;)r=l+r}else"J"===i?r+="H":r+=i}return r}(_,this.locale));var m={type:o.dateTime,pattern:b,location:c.styleLocation,parsedOptions:this.shouldParseSkeletons?(y={},b.replace(P,function(e){var t=e.length;switch(e[0]){case"G":y.era=4===t?"long":5===t?"narrow":"short";break;case"y":y.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":y.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":y.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":y.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"a":y.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":y.hourCycle="h12",y.hour=["numeric","2-digit"][t-1];break;case"H":y.hourCycle="h23",y.hour=["numeric","2-digit"][t-1];break;case"K":y.hourCycle="h11",y.hour=["numeric","2-digit"][t-1];break;case"k":y.hourCycle="h24",y.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":y.minute=["numeric","2-digit"][t-1];break;case"s":y.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":y.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),y):{}};return{val:{type:"date"===u?i.date:i.time,value:r,location:v,style:m},err:null}}return{val:{type:"number"===u?i.number:"date"===u?i.date:i.time,value:r,location:v,style:null!==(a=null==c?void 0:c.style)&&void 0!==a?a:null},err:null};case"plural":case"selectordinal":case"select":var E=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,D(E,d({},E)));this.bumpSpace();var A=this.parseIdentifierIfPossible(),T=0;if("select"!==u&&"offset"===A.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,D(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),A=this.parseIdentifierIfPossible(),T=p.val}var S=this.tryParsePluralOrSelectOptions(e,u,t,A);if(S.err)return S;var g=this.tryParseArgumentClose(s);if(g.err)return g;var w=D(s,this.clonePosition());if("select"===u)return{val:{type:i.select,value:r,options:Y(S.val),location:w},err:null};return{val:{type:i.plural,value:r,options:Y(S.val),offset:T,pluralType:"plural"===u?"cardinal":"ordinal",location:w},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,D(l,h))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,D(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(w).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var i=t[n].split("/");if(0===i.length)throw Error("Invalid number skeleton");for(var o=i[0],s=i.slice(1),a=0;a<s.length;a++)if(0===s[a].length)throw Error("Invalid number skeleton");r.push({stem:o,options:s})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:o.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=d(d(d({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return d(d({},e),B(t)||{})},{}));continue;case"engineering":t=d(d(d({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return d(d({},e),B(t)||{})},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(I,function(e,r,n,i,o,s){if(r)t.minimumIntegerDigits=n.length;else if(i&&o)throw Error("We currently do not support maximum integer digits");else if(s)throw Error("We currently do not support exact integer digits");return""});continue}if(H.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(x.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(x,function(e,r,n,i,o,s){return"*"===n?t.minimumFractionDigits=r.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&s?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+s.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var i=n.options[0];"w"===i?t=d(d({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=d(d({},t),L(i)));continue}if(M.test(n.stem)){t=d(d({},t),L(n.stem));continue}var o=B(n.stem);o&&(t=d(d({},t),o));var s=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!H.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);s&&(t=d(d({},t),s))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,i){for(var o,s=!1,a=[],l=new Set,u=i.value,h=i.location;;){if(0===u.length){var c=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;h=D(c,this.clonePosition()),u=this.message.slice(c.offset,this.offset())}else break}if(l.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,h);"other"===u&&(s=!0),this.bumpSpace();var f=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,D(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(f);if(m.err)return m;a.push([u,{value:p.val,location:D(f,this.clonePosition())}]),l.add(u),this.bumpSpace(),u=(o=this.parseIdentifierIfPossible()).value,h=o.location}return 0===a.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,D(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(n.MISSING_OTHER_CLAUSE,D(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,o=0;!this.isEOF();){var s=this.char();if(s>=48&&s<=57)i=!0,o=10*o+(s-48),this.bump();else break}var a=D(n,this.clonePosition());return i?W(o*=r)?{val:o,err:null}:this.error(t,a):this.error(e,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=q(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(z(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&en(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function er(e){return e>=97&&e<=122||e>=65&&e<=90}function en(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function ei(e,t){void 0===t&&(t={});var r=new et(e,t=d({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var i=SyntaxError(n[r.err.kind]);throw i.location=r.err.location,i.originalMessage=r.err.message,i}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,_(t)||b(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else g(t)&&A(t.style)?delete t.style.location:(v(t)||y(t))&&T(t.style)?delete t.style.location:E(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(l||(l={}));var eo=function(e){function t(t,r,n){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=n,i}return c(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),es=function(e){function t(t,r,n,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),l.INVALID_VALUE,i)||this}return c(t,e),t}(eo),ea=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),l.INVALID_VALUE,n)||this}return c(t,e),t}(eo),el=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),l.MISSING_VALUE,r)||this}return c(t,e),t}(eo);function eu(e){return"function"==typeof e}function eh(e,t,r,n,o,s,a){if(1===e.length&&m(e[0]))return[{type:u.literal,value:e[0].value}];for(var h=[],c=0;c<e.length;c++){var d=e[c];if(m(d)){h.push({type:u.literal,value:d.value});continue}if(d.type===i.pound){"number"==typeof s&&h.push({type:u.literal,value:r.getNumberFormat(t).format(s)});continue}var f=d.value;if(!(o&&f in o))throw new el(f,a);var p=o[f];if(d.type===i.argument){p&&"string"!=typeof p&&"number"!=typeof p||(p="string"==typeof p||"number"==typeof p?String(p):""),h.push({type:"string"==typeof p?u.literal:u.object,value:p});continue}if(v(d)){var S="string"==typeof d.style?n.date[d.style]:T(d.style)?d.style.parsedOptions:void 0;h.push({type:u.literal,value:r.getDateTimeFormat(t,S).format(p)});continue}if(y(d)){var S="string"==typeof d.style?n.time[d.style]:T(d.style)?d.style.parsedOptions:n.time.medium;h.push({type:u.literal,value:r.getDateTimeFormat(t,S).format(p)});continue}if(g(d)){var S="string"==typeof d.style?n.number[d.style]:A(d.style)?d.style.parsedOptions:void 0;S&&S.scale&&(p*=S.scale||1),h.push({type:u.literal,value:r.getNumberFormat(t,S).format(p)});continue}if(E(d)){var P=d.children,w=d.value,x=o[w];if(!eu(x))throw new ea(w,"function",a);var M=x(eh(P,t,r,n,o,s).map(function(e){return e.value}));Array.isArray(M)||(M=[M]),h.push.apply(h,M.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(_(d)){var I=d.options[p]||d.options.other;if(!I)throw new es(d.value,p,Object.keys(d.options),a);h.push.apply(h,eh(I.value,t,r,n,o));continue}if(b(d)){var I=d.options["=".concat(p)];if(!I){if(!Intl.PluralRules)throw new eo('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',l.MISSING_INTL_API,a);var H=r.getPluralRules(t,{type:d.pluralType}).select(p-(d.offset||0));I=d.options[H]||d.options.other}if(!I)throw new es(d.value,p,Object.keys(d.options),a);h.push.apply(h,eh(I.value,t,r,n,o,p-(d.offset||0)));continue}}return h.length<2?h:h.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}function ec(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var ed=function(){function e(t,r,n,i){void 0===r&&(r=e.defaultLocale);var o,s,a=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=a.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return eh(a.ast,a.locales,a.formatters,a.formats,e,void 0,a.message)},this.resolvedOptions=function(){var e;return{locale:(null===(e=a.resolvedLocale)||void 0===e?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(a.locales)[0]}},this.getAst=function(){return a.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var l=i||{},h=(l.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}(l,["formatters"]));this.ast=e.__parse(t,d(d({},h),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(o=e.formats,n?Object.keys(o).reduce(function(e,t){var r,i;return e[t]=(r=o[t],(i=n[t])?d(d(d({},r||{}),i||{}),Object.keys(r).reduce(function(e,t){return e[t]=d(d({},r[t]),i[t]||{}),e},{})):r),e},d({},o)):o),this.formatters=i&&i.formatters||(void 0===(s=this.formatterCache)&&(s={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,f([void 0],t,!1)))},{cache:ec(s.number),strategy:p.strategies.variadic}),getDateTimeFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,f([void 0],t,!1)))},{cache:ec(s.dateTime),strategy:p.strategies.variadic}),getPluralRules:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,f([void 0],t,!1)))},{cache:ec(s.pluralRules),strategy:p.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=ei,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ef=ed},3186:(e,t,r)=>{"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{Z:()=>f});var i=r(1159),o=r.n(i);let s=(0,r(8570).createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js#default`);var a=r(9243);let l=(0,i.cache)(async function(){return Promise.resolve((await (0,a.Z)()).locale)}),u=(0,i.cache)(async function(e){return(await (0,a.Z)(e)).now});async function h(e){return u(null==e?void 0:e.locale)}let c=(0,i.cache)(async function(e){return(await (0,a.Z)(e)).timeZone});async function d(e){return c(null==e?void 0:e.locale)}async function f(e){let{locale:t,now:r,timeZone:i,...a}=e;return o().createElement(s,n({locale:null!=t?t:await l(),now:null!=r?r:await h(),timeZone:null!=i?i:await d()},a))}},55:(e,t,r)=>{"use strict";r.d(t,{V:()=>i,t:()=>o});let n=(0,r(1159).cache)(function(){return{locale:void 0}});function i(){return n().locale}function o(e){n().locale=e}},9243:(e,t,r)=>{"use strict";r.d(t,{Z:()=>A});var n=r(8585),i=r(1159),o=r(7872);function s(e){return"function"==typeof e.then}var a=r(8757);let l="X-NEXT-INTL-LOCALE";var u=r(55);let h=(0,i.cache)(async function(){let e=(0,a.headers)();return s(e)?await e:e}),c=(0,i.cache)(async function(){let e;try{e=(await h()).get(l)||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function d(){return(0,u.V)()||await c()}let f=(0,i.cache)(function(){let e;try{e=(0,a.headers)().get(l)}catch(e){throw e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest?Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e}):e}return e||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),(0,n.notFound)()),e});var p=r(4288);let m=!1,g=!1,v=(0,i.cache)(function(){return new Date}),y=(0,i.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),_=(0,i.cache)(async function(e,t){if("function"!=typeof e)throw Error("Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n");let r={get locale(){return g||(console.warn("\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),g=!0),t||(0,u.V)()||f()},get requestLocale(){return t?Promise.resolve(t):d()}},i=e(r);s(i)&&(i=await i);let o=i.locale;return o||(m||(console.error("\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\n\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),m=!0),(o=await r.requestLocale)||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),(0,n.notFound)())),{...i,locale:o,now:i.now||v(),timeZone:i.timeZone||y()}}),b=(0,i.cache)(o.PW),E=(0,i.cache)(o.PI),A=(0,i.cache)(async function(e){let t=await _(p.Z,e);return{...(0,o.tC)(t),_formatters:b(E())}})},5031:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(1159),i=r(9243);let o=(0,n.cache)(async function(e){return function(e){if(!e.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return e.messages}(await (0,i.Z)(e))});async function s(e){return o(null==e?void 0:e.locale)}},107:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{Z:()=>n})},8952:(e,t,r)=>{"use strict";var n=r(5060);t.R=n.default},5060:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return e}},8585:(e,t,r)=>{"use strict";var n=r(1085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},3085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return o}});let n=r(5869),i=r(6278);class o{get isEnabled(){return this._provider.isEnabled}enable(){let e=n.staticGenerationAsyncStorage.getStore();return e&&(0,i.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=n.staticGenerationAsyncStorage.getStore();return e&&(0,i.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return d},draftMode:function(){return f},headers:function(){return c}});let n=r(8996),i=r(3047),o=r(2044),s=r(2934),a=r(3085),l=r(6278),u=r(5869),h=r(4580);function c(){let e="headers",t=u.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return i.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,h.getExpectedRequestStore)(e).headers}function d(){let e="cookies",t=u.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return n.RequestCookiesAdapter.seal(new o.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,h.getExpectedRequestStore)(e),i=s.actionAsyncStorage.getStore();return(null==i?void 0:i.isAction)||(null==i?void 0:i.isAppRoute)?r.mutableCookies:r.cookies}function f(){let e=(0,h.getExpectedRequestStore)("draftMode");return new a.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return s},RedirectType:function(){return n.RedirectType},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(3953),i=r(6399);class o extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class s extends URLSearchParams{append(){throw new o}delete(){throw new o}set(){throw new o}sort(){throw new o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return i},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return p},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return d},isRedirectError:function(){return c},permanentRedirect:function(){return h},redirect:function(){return u}});let i=r(4580),o=r(2934),s=r(8586),a="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=s.RedirectStatusCode.TemporaryRedirect);let n=Error(a);n.digest=a+";"+t+";"+e+";"+r+";";let o=i.requestAsyncStorage.getStore();return o&&(n.mutableCookies=o.mutableCookies),n}function u(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.TemporaryRedirect)}function h(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.PermanentRedirect)}function c(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,i]=e.digest.split(";",4),o=Number(i);return t===a&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(o)&&o in s.RedirectStatusCode}function d(e){return c(e)?e.digest.split(";",3)[2]:null}function f(e){if(!c(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!c(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9925:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,i],...o]=a(e),{domain:s,expires:l,httponly:c,maxage:d,path:f,samesite:p,secure:m,partitioned:g,priority:v}=Object.fromEntries(o.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(i),domain:s,...l&&{expires:new Date(l)},...c&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:f,...p&&{sameSite:u.includes(t=(t=p).toLowerCase())?t:void 0},...m&&{secure:!0},...v&&{priority:h.includes(r=(r=v).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(o,{RequestCookies:()=>c,ResponseCookies:()=>d,parseCookie:()=>a,parseSetCookie:()=>l,stringifyCookie:()=>s}),e.exports=((e,o,s,a)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let l of n(o))i.call(e,l)||l===s||t(e,l,{get:()=>o[l],enumerable:!(a=r(o,l))||a.enumerable});return e})(t({},"__esModule",{value:!0}),o);var u=["strict","lax","none"],h=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,o,s=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,o=!1;l();)if(","===(r=e.charAt(a))){for(n=a,a+=1,l(),i=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(o=!0,a=i,s.push(e.substring(t,n)),t=a):a=n+1}else a+=1;(!o||a>=e.length)&&s.push(e.substring(t,e.length))}return s}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},3047:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return o},ReadonlyHeadersError:function(){return i}});let n=r(8238);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class o extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,i);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==s)return n.ReflectAdapter.get(t,s,i)},set(t,r,i,o){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,i,o);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);return n.ReflectAdapter.set(t,a??r,i,o)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==o&&n.ReflectAdapter.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===o||n.ReflectAdapter.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},8996:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return c},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return a},appendMutableCookies:function(){return h},getModifiedCookieValues:function(){return u}});let n=r(2044),i=r(8238),o=r(5869);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new s}}class a{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function h(e,t){let r=u(t);if(0===r.length)return!1;let i=new n.ResponseCookies(e),o=i.getAll();for(let e of r)i.set(e);for(let e of o)i.set(e);return!0}class c{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let s=[],a=new Set,u=()=>{let e=o.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),s=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of s){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return s;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{u()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{u()}};default:return i.ReflectAdapter.get(e,t,r)}}})}}},2044:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies},stringifyCookie:function(){return n.stringifyCookie}});let n=r(9925)},7872:(e,t,r)=>{"use strict";var n=r(8381),i=r(6798);r(5544),r(1159),r(8995),n.IntlError,n.IntlErrorCode,n.createFormatter,t.PI=i.createCache,t.PW=i.createIntlFormatters,t.tC=i.initializeConfig,t.eX=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),getMessageFallback:o=i.defaultGetMessageFallback,messages:s,namespace:a,onError:l=i.defaultOnError,...u}=e;return function(e,t){let{messages:r,namespace:i,...o}=e;return r=r["!"],i=n.resolveNamespace(i,"!"),n.createBaseTranslator({...o,messages:r,namespace:i})}({...u,onError:l,cache:t,formatters:r,getMessageFallback:o,messages:{"!":s},namespace:a?"!.".concat(a):"!"},0)}},8381:(e,t,r)=>{"use strict";var n=r(5544),i=r(1159),o=r(6798),s=function(e){return e&&e.__esModule?e:{default:e}}(n);function a(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let l=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class u extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),a(this,"code",void 0),a(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function h(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function c(e,t,r,n){let i=o.joinPath(n,r);if(!t)throw Error(i);let s=t;return r.split(".").forEach(t=>{let r=s[t];if(null==t||null==r)throw Error(i+" (".concat(e,")"));s=r}),s}let d=365/12*86400,f={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=u,t.IntlErrorCode=l,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:o.defaultOnError;try{if(!t)throw Error(void 0);let n=r?c(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new u(l.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:a,getMessageFallback:d=o.defaultGetMessageFallback,locale:f,messagesOrError:p,namespace:m,onError:g,timeZone:v}=e,y=p instanceof u;function _(e,t,r){let n=new u(t,r);return g(n),d({error:n,key:e,namespace:m})}function b(e,u,g){let b,E;if(y)return d({error:p,key:e,namespace:m});try{b=c(f,p,e,m)}catch(t){return _(e,l.MISSING_MESSAGE,t.message)}if("object"==typeof b){let t;return _(e,Array.isArray(b)?l.INVALID_MESSAGE:l.INSUFFICIENT_PATH,t)}let A=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(b,u);if(A)return A;a.getMessageFormat||(a.getMessageFormat=o.memoFn(function(){return new s.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:a,...arguments.length<=3?void 0:arguments[3]})},t.message));try{E=a.getMessageFormat(b,f,function(e,t){let r=t?{...e,dateTime:h(e.dateTime,t)}:e,n=s.default.formats.date,i=t?h(n,t):n,o=s.default.formats.time,a=t?h(o,t):o;return{...r,date:{...i,...r.dateTime},time:{...a,...r.dateTime}}}({...n,...g},v),{formatters:{...a,getDateTimeFormat:(e,t)=>a.getDateTimeFormat(e,{timeZone:v,...t})}})}catch(t){return _(e,l.INVALID_MESSAGE,t.message)}try{let e=E.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,o=0,s=e[r];n="function"==typeof s?e=>{let t=s(e);return i.isValidElement(t)?i.cloneElement(t,{key:r+o++}):t}:s,t[r]=n}),t}({...r,...u}));if(null==e)throw Error(void 0);return i.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return _(e,l.FORMATTING_ERROR,t.message)}}function E(e,t,r){let n=b(e,t,r);return"string"!=typeof n?_(e,l.INVALID_MESSAGE,void 0):n}return E.rich=b,E.markup=(e,t,r)=>{let n=b(e,t,r);if("string"!=typeof n){let t=new u(l.FORMATTING_ERROR,void 0);return g(t),d({error:t,key:e,namespace:m})}return n},E.raw=e=>{if(y)return d({error:p,key:e,namespace:m});try{return c(f,p,e,m)}catch(t){return _(e,l.MISSING_MESSAGE,t.message)}},E.has=e=>{if(y)return!1;try{return c(f,p,e,m),!0}catch(e){return!1}},E}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),formats:n,locale:i,now:s,onError:a=o.defaultOnError,timeZone:h}=e;function c(e){var t;return null!==(t=e)&&void 0!==t&&t.timeZone||(h?e={...e,timeZone:h}:a(new u(l.ENVIRONMENT_FALLBACK,void 0))),e}function p(e,t,r,n){let i;try{i=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new u(l.MISSING_FORMAT,void 0);throw a(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(i)}catch(e){return a(new u(l.FORMATTING_ERROR,e.message)),n()}}function m(e,t){return p(t,null==n?void 0:n.dateTime,t=>(t=c(t),r.getDateTimeFormat(i,t).format(e)),()=>String(e))}function g(){return s||(a(new u(l.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:m,number:function(e,t){return p(t,null==n?void 0:n.number,t=>r.getNumberFormat(i,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let o,s;let a={};t instanceof Date||"number"==typeof t?o=new Date(t):t&&(o=null!=t.now?new Date(t.now):g(),s=t.unit,a.style=t.style,a.numberingSystem=t.numberingSystem),o||(o=g());let l=(new Date(e).getTime()-o.getTime())/1e3;s||(s=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<d?"week":t<31536e3?"month":"year"}(l)),a.numeric="second"===s?"auto":"always";let u=(n=s,Math.round(l/f[n]));return r.getRelativeTimeFormat(i,a).format(u,s)}catch(t){return a(new u(l.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let o=[],s=new Map,a=0;for(let t of e){let e;"object"==typeof t?(e=String(a),s.set(e,t)):e=String(t),o.push(e),a++}return p(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(i,e).formatToParts(o).map(e=>"literal"===e.type?e.value:s.get(e.value)||e.value);return s.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,o){return p(o,null==n?void 0:n.dateTime,n=>(n=c(n),r.getDateTimeFormat(i,n).formatRange(e,t)),()=>[m(e),m(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},6798:(e,t,r)=>{"use strict";var n=r(8995);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function o(e){return i(e.namespace,e.key)}function s(e){console.error(e)}function a(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function l(e,t){return a(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:l(Intl.DateTimeFormat,e.dateTime),getNumberFormat:l(Intl.NumberFormat,e.number),getPluralRules:l(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:l(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:l(Intl.ListFormat,e.list),getDisplayNames:l(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=o,t.defaultOnError=s,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...i}=e;return{...i,messages:r,onError:n||s,getMessageFallback:t||o}},t.joinPath=i,t.memoFn=a},6421:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(7577);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},2791:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(7577);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))})},4867:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(7577);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))})},2:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(7577);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"}))})},2505:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(7577);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"}))})},2591:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(7577);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},1135:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n)}return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{W:()=>n})},6462:(e,t,r)=>{"use strict";r.d(t,{M:()=>g});var n=r(7577),i=r(2482);function o(){let e=(0,n.useRef)(!1);return(0,i.L)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var s=r(805),a=r(295),l=r(4749);class u extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:e,isPresent:t}){let r=(0,n.useId)(),i=(0,n.useRef)(null),o=(0,n.useRef)({width:0,height:0,top:0,left:0});return(0,n.useInsertionEffect)(()=>{let{width:e,height:n,top:s,left:a}=o.current;if(t||!i.current||!e||!n)return;i.current.dataset.motionPopId=r;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            top: ${s}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[t]),n.createElement(u,{isPresent:t,childRef:i,sizeRef:o},n.cloneElement(e,{ref:i}))}let c=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:o,presenceAffectsLayout:s,mode:u})=>{let c=(0,l.h)(d),f=(0,n.useId)(),p=(0,n.useMemo)(()=>({id:f,initial:t,isPresent:r,custom:o,onExitComplete:e=>{for(let t of(c.set(e,!0),c.values()))if(!t)return;i&&i()},register:e=>(c.set(e,!1),()=>c.delete(e))}),s?void 0:[r]);return(0,n.useMemo)(()=>{c.forEach((e,t)=>c.set(t,!1))},[r]),n.useEffect(()=>{r||c.size||!i||i()},[r]),"popLayout"===u&&(e=n.createElement(h,{isPresent:r},e)),n.createElement(a.O.Provider,{value:p},e)};function d(){return new Map}var f=r(339),p=r(4673);let m=e=>e.key||"",g=({children:e,custom:t,initial:r=!0,onExitComplete:a,exitBeforeEnter:l,presenceAffectsLayout:u=!0,mode:h="sync"})=>{(0,p.k)(!l,"Replace exitBeforeEnter with mode='wait'");let d=(0,n.useContext)(f.p).forceRender||function(){let e=o(),[t,r]=(0,n.useState)(0),i=(0,n.useCallback)(()=>{e.current&&r(t+1)},[t]);return[(0,n.useCallback)(()=>s.Wi.postRender(i),[i]),t]}()[0],g=o(),v=function(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}(e),y=v,_=(0,n.useRef)(new Map).current,b=(0,n.useRef)(y),E=(0,n.useRef)(new Map).current,A=(0,n.useRef)(!0);if((0,i.L)(()=>{A.current=!1,function(e,t){e.forEach(e=>{let r=m(e);t.set(r,e)})}(v,E),b.current=y}),A.current)return n.createElement(n.Fragment,null,y.map(e=>n.createElement(c,{key:m(e),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:u,mode:h},e)));y=[...y];let T=b.current.map(m),S=v.map(m),P=T.length;for(let e=0;e<P;e++){let t=T[e];-1!==S.indexOf(t)||_.has(t)||_.set(t,void 0)}return"wait"===h&&_.size&&(y=[]),_.forEach((e,r)=>{if(-1!==S.indexOf(r))return;let i=E.get(r);if(!i)return;let o=T.indexOf(r),s=e;s||(s=n.createElement(c,{key:m(i),isPresent:!1,onExitComplete:()=>{_.delete(r);let e=Array.from(E.keys()).filter(e=>!S.includes(e));if(e.forEach(e=>E.delete(e)),b.current=v.filter(t=>{let n=m(t);return n===r||e.includes(n)}),!_.size){if(!1===g.current)return;d(),a&&a()}},custom:t,presenceAffectsLayout:u,mode:h},i),_.set(r,s)),y.splice(o,0,s)}),y=y.map(e=>{let t=e.key;return _.has(t)?e:n.createElement(c,{key:m(e),isPresent:!0,presenceAffectsLayout:u,mode:h},e)}),n.createElement(n.Fragment,null,_.size?y:y.map(e=>(0,n.cloneElement)(e)))}},339:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});let n=(0,r(7577).createContext)({})},3965:(e,t,r)=>{"use strict";r.d(t,{_:()=>n});let n=(0,r(7577).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},295:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});let n=(0,r(7577).createContext)(null)},805:(e,t,r)=>{"use strict";r.d(t,{Pn:()=>a,Wi:()=>s,frameData:()=>l,S6:()=>u});var n=r(4380);class i{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let o=["prepare","read","update","preRender","render","postRender"],{schedule:s,cancel:a,state:l,steps:u}=function(e,t){let r=!1,n=!0,s={delta:0,timestamp:0,isProcessing:!1},a=o.reduce((e,t)=>(e[t]=function(e){let t=new i,r=new i,n=0,o=!1,s=!1,a=new WeakSet,l={schedule:(e,i=!1,s=!1)=>{let l=s&&o,u=l?t:r;return i&&a.add(e),u.add(e)&&l&&o&&(n=t.order.length),e},cancel:e=>{r.remove(e),a.delete(e)},process:i=>{if(o){s=!0;return}if(o=!0,[t,r]=[r,t],r.clear(),n=t.order.length)for(let r=0;r<n;r++){let n=t.order[r];n(i),a.has(n)&&(l.schedule(n),e())}o=!1,s&&(s=!1,l.process(i))}};return l}(()=>r=!0),e),{}),l=e=>a[e].process(s),u=()=>{let i=performance.now();r=!1,s.delta=n?1e3/60:Math.max(Math.min(i-s.timestamp,40),1),s.timestamp=i,s.isProcessing=!0,o.forEach(l),s.isProcessing=!1,r&&t&&(n=!1,e(u))},h=()=>{r=!0,n=!0,s.isProcessing||e(u)};return{schedule:o.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,i=!1)=>(r||h(),n.schedule(e,t,i)),e},{}),cancel:e=>o.forEach(t=>a[t].cancel(e)),state:s,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.Z,!0)},2350:(e,t,r)=>{"use strict";r.d(t,{E:()=>n3});var n=r(7577),i=r(3965);let o=(0,n.createContext)({});var s=r(295),a=r(2482);let l=(0,n.createContext)({strict:!1}),u=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),h="data-"+u("framerAppearId");function c(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function d(e){return"string"==typeof e||Array.isArray(e)}function f(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let p=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],m=["initial",...p];function g(e){return f(e.animate)||m.some(t=>d(e[t]))}function v(e){return!!(g(e)||e.variants)}function y(e){return Array.isArray(e)?e.join(" "):e}let _={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},b={};for(let e in _)b[e]={isEnabled:t=>_[e].some(e=>!!t[e])};var E=r(8263),A=r(339);let T=(0,n.createContext)({}),S=Symbol.for("motionComponentSymbol"),P=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function w(e){if("string"!=typeof e||e.includes("-"));else if(P.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let x={},M=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],I=new Set(M);function H(e,{layout:t,layoutId:r}){return I.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!x[e]||"opacity"===e)}let L=e=>!!(e&&e.getVelocity),B={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},C=M.length;var R=r(8543);let O=(e,t)=>t&&"number"==typeof e?t.transform(e):e;var N=r(7255),D=r(7162);let k={...N.Rx,transform:Math.round},F={borderWidth:D.px,borderTopWidth:D.px,borderRightWidth:D.px,borderBottomWidth:D.px,borderLeftWidth:D.px,borderRadius:D.px,radius:D.px,borderTopLeftRadius:D.px,borderTopRightRadius:D.px,borderBottomRightRadius:D.px,borderBottomLeftRadius:D.px,width:D.px,maxWidth:D.px,height:D.px,maxHeight:D.px,size:D.px,top:D.px,right:D.px,bottom:D.px,left:D.px,padding:D.px,paddingTop:D.px,paddingRight:D.px,paddingBottom:D.px,paddingLeft:D.px,margin:D.px,marginTop:D.px,marginRight:D.px,marginBottom:D.px,marginLeft:D.px,rotate:D.RW,rotateX:D.RW,rotateY:D.RW,rotateZ:D.RW,scale:N.bA,scaleX:N.bA,scaleY:N.bA,scaleZ:N.bA,skew:D.RW,skewX:D.RW,skewY:D.RW,distance:D.px,translateX:D.px,translateY:D.px,translateZ:D.px,x:D.px,y:D.px,z:D.px,perspective:D.px,transformPerspective:D.px,opacity:N.Fq,originX:D.$C,originY:D.$C,originZ:D.px,zIndex:k,fillOpacity:N.Fq,strokeOpacity:N.Fq,numOctaves:k};function V(e,t,r,n){let{style:i,vars:o,transform:s,transformOrigin:a}=e,l=!1,u=!1,h=!0;for(let e in t){let r=t[e];if((0,R.f9)(e)){o[e]=r;continue}let n=F[e],c=O(r,n);if(I.has(e)){if(l=!0,s[e]=c,!h)continue;r!==(n.default||0)&&(h=!1)}else e.startsWith("origin")?(u=!0,a[e]=c):i[e]=c}if(!t.transform&&(l||n?i.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:r=!0},n,i){let o="";for(let t=0;t<C;t++){let r=M[t];if(void 0!==e[r]){let t=B[r]||r;o+=`${t}(${e[r]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,n?"":o):r&&n&&(o="none"),o}(e.transform,r,h,n):i.transform&&(i.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:r=0}=a;i.transformOrigin=`${e} ${t} ${r}`}}let U=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function G(e,t,r){for(let n in t)L(t[n])||H(n,r)||(e[n]=t[n])}let j=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function W(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||j.has(e)}let X=e=>!W(e);try{!function(e){e&&(X=t=>t.startsWith("on")?!W(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}function Z(e,t,r){return"string"==typeof e?e:D.px.transform(t+r*e)}let z={offset:"stroke-dashoffset",array:"stroke-dasharray"},$={offset:"strokeDashoffset",array:"strokeDasharray"};function Y(e,{attrX:t,attrY:r,attrScale:n,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},h,c,d){if(V(e,u,h,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:f,style:p,dimensions:m}=e;f.transform&&(m&&(p.transform=f.transform),delete f.transform),m&&(void 0!==i||void 0!==o||p.transform)&&(p.transformOrigin=function(e,t,r){let n=Z(t,e.x,e.width),i=Z(r,e.y,e.height);return`${n} ${i}`}(m,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==t&&(f.x=t),void 0!==r&&(f.y=r),void 0!==n&&(f.scale=n),void 0!==s&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?z:$;e[o.offset]=D.px.transform(-n);let s=D.px.transform(t),a=D.px.transform(r);e[o.array]=`${s} ${a}`}(f,s,a,l,!1)}let q=()=>({...U(),attrs:{}}),K=e=>"string"==typeof e&&"svg"===e.toLowerCase();function Q(e,{style:t,vars:r},n,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(o,r[o])}let J=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ee(e,t,r,n){for(let r in Q(e,t,void 0,n),t.attrs)e.setAttribute(J.has(r)?r:u(r),t.attrs[r])}function et(e,t){let{style:r}=e,n={};for(let i in r)(L(r[i])||t.style&&L(t.style[i])||H(i,e))&&(n[i]=r[i]);return n}function er(e,t){let r=et(e,t);for(let n in e)(L(e[n])||L(t[n]))&&(r[-1!==M.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}function en(e,t,r,n={},i={}){return"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,i)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,i)),t}var ei=r(4749);let eo=e=>Array.isArray(e),es=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),ea=e=>eo(e)?e[e.length-1]||0:e;function el(e){let t=L(e)?e.get():e;return es(t)?t.toValue():t}let eu=e=>(t,r)=>{let i=(0,n.useContext)(o),a=(0,n.useContext)(s.O),l=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},n,i,o){let s={latestValues:function(e,t,r,n){let i={},o=n(e,{});for(let e in o)i[e]=el(o[e]);let{initial:s,animate:a}=e,l=g(e),u=v(e);t&&u&&!l&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===a&&(a=t.animate));let h=!!r&&!1===r.initial,c=(h=h||!1===s)?a:s;return c&&"boolean"!=typeof c&&!f(c)&&(Array.isArray(c)?c:[c]).forEach(t=>{let r=en(e,t);if(!r)return;let{transitionEnd:n,transition:o,...s}=r;for(let e in s){let t=s[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(i[e]=t)}for(let e in n)i[e]=n[e]}),i}(n,i,o,e),renderState:t()};return r&&(s.mount=e=>r(n,e,s)),s})(e,t,i,a);return r?l():(0,ei.h)(l)};var eh=r(805);let ec={useVisualState:eu({scrapeMotionValuesFromProps:er,createRenderState:q,onMount:(e,t,{renderState:r,latestValues:n})=>{eh.Wi.read(()=>{try{r.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),eh.Wi.render(()=>{Y(r,n,{enableHardwareAcceleration:!1},K(t.tagName),e.transformTemplate),ee(t,r)})}})},ed={useVisualState:eu({scrapeMotionValuesFromProps:et,createRenderState:U})};function ef(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let ep=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function em(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let eg=e=>t=>ep(t)&&e(t,em(t));function ev(e,t,r,n){return ef(e,t,eg(r),n)}var ey=r(9022);function e_(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let eb=e_("dragHorizontal"),eE=e_("dragVertical");function eA(e){let t=!1;if("y"===e)t=eE();else if("x"===e)t=eb();else{let e=eb(),r=eE();e&&r?t=()=>{e(),r()}:(e&&e(),r&&r())}return t}function eT(){let e=eA(!0);return!e||(e(),!1)}class eS{constructor(e){this.isMounted=!1,this.node=e}update(){}}function eP(e,t){let r="onHover"+(t?"Start":"End");return ev(e.current,"pointer"+(t?"enter":"leave"),(n,i)=>{if("touch"===n.pointerType||eT())return;let o=e.getProps();e.animationState&&o.whileHover&&e.animationState.setActive("whileHover",t),o[r]&&eh.Wi.update(()=>o[r](n,i))},{passive:!e.getProps()[r]})}class ew extends eS{mount(){this.unmount=(0,ey.z)(eP(this.node,!0),eP(this.node,!1))}unmount(){}}class ex extends eS{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,ey.z)(ef(this.node.current,"focus",()=>this.onFocus()),ef(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let eM=(e,t)=>!!t&&(e===t||eM(e,t.parentElement));var eI=r(4380);function eH(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,em(r))}class eL extends eS{constructor(){super(...arguments),this.removeStartListeners=eI.Z,this.removeEndListeners=eI.Z,this.removeAccessibleListeners=eI.Z,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),n=ev(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:n,globalTapTarget:i}=this.node.getProps();eh.Wi.update(()=>{i||eM(this.node.current,e.target)?r&&r(e,t):n&&n(e,t)})},{passive:!(r.onTap||r.onPointerUp)}),i=ev(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=(0,ey.z)(n,i),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=ef(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=ef(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&eH("up",(e,t)=>{let{onTap:r}=this.node.getProps();r&&eh.Wi.update(()=>r(e,t))})}),eH("down",(e,t)=>{this.startPress(e,t)}))}),t=ef(this.node.current,"blur",()=>{this.isPressing&&eH("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=(0,ey.z)(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:r,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&eh.Wi.update(()=>r(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!eT()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&eh.Wi.update(()=>r(e,t))}mount(){let e=this.node.getProps(),t=ev(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=ef(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=(0,ey.z)(t,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let eB=new WeakMap,eC=new WeakMap,eR=e=>{let t=eB.get(e.target);t&&t(e)},eO=e=>{e.forEach(eR)},eN={some:0,all:1};class eD extends eS{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:eN[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;eC.has(r)||eC.set(r,{});let n=eC.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(eO,{root:e,...t})),n[i]}(t);return eB.set(e,r),n.observe(e),()=>{eB.delete(e),n.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=t?r:n;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}function ek(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function eF(e,t,r){let n=e.getProps();return en(n,t,void 0!==r?r:n.custom,function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.getVelocity()),t}(e))}var eV=r(4673);let eU=e=>1e3*e,eG=e=>e/1e3,ej={current:!1},eW=e=>Array.isArray(e)&&"number"==typeof e[0],eX=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,eZ={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eX([0,.65,.55,1]),circOut:eX([.55,0,1,.45]),backIn:eX([.31,.01,.66,-.59]),backOut:eX([.33,1.53,.69,.99])},ez=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function e$(e,t,r,n){if(e===t&&r===n)return eI.Z;let i=t=>(function(e,t,r,n,i){let o,s;let a=0;do(o=ez(s=t+(r-t)/2,n,i)-e)>0?r=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,r);return e=>0===e||1===e?e:ez(i(e),t,n)}let eY=e$(.42,0,1,1),eq=e$(0,0,.58,1),eK=e$(.42,0,.58,1),eQ=e=>Array.isArray(e)&&"number"!=typeof e[0],eJ=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e0=e=>t=>1-e(1-t),e1=e=>1-Math.sin(Math.acos(e)),e2=e0(e1),e5=eJ(e1),e8=e$(.33,1.53,.69,.99),e3=e0(e8),e6=eJ(e3),e9={linear:eI.Z,easeIn:eY,easeInOut:eK,easeOut:eq,circIn:e1,circInOut:e5,circOut:e2,backIn:e3,backInOut:e6,backOut:e8,anticipate:e=>(e*=2)<1?.5*e3(e):.5*(2-Math.pow(2,-10*(e-1)))},e7=e=>{if(Array.isArray(e)){(0,eV.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return e$(t,r,n,i)}return"string"==typeof e?((0,eV.k)(void 0!==e9[e],`Invalid easing type '${e}'`),e9[e]):e};var e4=r(8448),te=r(6331),tt=r(5018);function tr({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=eQ(n)?n.map(e7):e7(n),o={done:!1,value:t[0]},s=(r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=(0,tt.Y)(0,t,n);e.push((0,te.C)(r,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),a=(0,e4.s)(s,t,{ease:Array.isArray(i)?i:t.map(()=>i||eK).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=a(t),o.done=t>=e,o)}}var tn=r(8702);function ti(e,t,r){let n=Math.max(t-5,0);return(0,tn.R)(r-e(n),t-n)}var to=r(2361);function ts(e,t){return e*Math.sqrt(1-t*t)}let ta=["duration","bounce"],tl=["stiffness","damping","mass"];function tu(e,t){return t.some(t=>void 0!==e[t])}function th({keyframes:e,restDelta:t,restSpeed:r,...n}){let i;let o=e[0],s=e[e.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:f}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!tu(e,tl)&&tu(e,ta)){let r=function({duration:e=800,bounce:t=.25,velocity:r=0,mass:n=1}){let i,o;(0,eV.K)(e<=eU(10),"Spring duration must be 10 seconds or less");let s=1-t;s=(0,to.u)(.05,1,s),e=(0,to.u)(.01,10,eG(e)),s<1?(i=t=>{let n=t*s,i=n*e;return .001-(n-r)/ts(t,s)*Math.exp(-i)},o=t=>{let n=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=ts(Math.pow(t,2),s);return(n*r+r-o)*Math.exp(-n)*(-i(t)+.001>0?-1:1)/a}):(i=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),o=t=>e*e*(r-t)*Math.exp(-t*e));let a=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,o,5/e);if(e=eU(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(a,2)*n;return{stiffness:t,damping:2*s*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:1}).isResolvedFromDuration=!0}return t}({...n,velocity:-eG(n.velocity||0)}),p=d||0,m=u/(2*Math.sqrt(l*h)),g=s-o,v=eG(Math.sqrt(l/h)),y=5>Math.abs(g);if(r||(r=y?.01:2),t||(t=y?.005:.5),m<1){let e=ts(v,m);i=t=>s-Math.exp(-m*v*t)*((p+m*v*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===m)i=e=>s-Math.exp(-v*e)*(g+(p+v*g)*e);else{let e=v*Math.sqrt(m*m-1);i=t=>{let r=Math.exp(-m*v*t),n=Math.min(e*t,300);return s-r*((p+m*v*g)*Math.sinh(n)+e*g*Math.cosh(n))/e}}return{calculatedDuration:f&&c||null,next:e=>{let n=i(e);if(f)a.done=e>=c;else{let o=p;0!==e&&(o=m<1?ti(i,e,n):0);let l=Math.abs(o)<=r,u=Math.abs(s-n)<=t;a.done=l&&u}return a.value=a.done?s:n,a}}}function tc({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let f=e[0],p={done:!1,value:f},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l?a:Math.abs(a-e)<Math.abs(l-e)?a:l,v=r*t,y=f+v,_=void 0===s?y:s(y);_!==y&&(v=_-f);let b=e=>-v*Math.exp(-e/n),E=e=>_+b(e),A=e=>{let t=b(e),r=E(e);p.done=Math.abs(t)<=u,p.value=p.done?_:r},T=e=>{m(p.value)&&(c=e,d=th({keyframes:[p.value,g(p.value)],velocity:ti(E,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==c||(t=!0,A(e),T(e)),void 0!==c&&e>c)?d.next(e-c):(t||A(e),p)}}}let td=e=>{let t=({timestamp:t})=>e(t);return{start:()=>eh.Wi.update(t,!0),stop:()=>(0,eh.Pn)(t),now:()=>eh.frameData.isProcessing?eh.frameData.timestamp:performance.now()}};function tf(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}let tp={decay:tc,inertia:tc,tween:tr,keyframes:tr,spring:th};function tm({autoplay:e=!0,delay:t=0,driver:r=td,keyframes:n,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:h,onUpdate:c,...d}){let f,p,m,g,v,y=1,_=!1,b=()=>{p=new Promise(e=>{f=e})};b();let E=tp[i]||tr;E!==tr&&"number"!=typeof n[0]&&(g=(0,e4.s)([0,100],n,{clamp:!1}),n=[0,100]);let A=E({...d,keyframes:n});"mirror"===a&&(v=E({...d,keyframes:[...n].reverse(),velocity:-(d.velocity||0)}));let T="idle",S=null,P=null,w=null;null===A.calculatedDuration&&o&&(A.calculatedDuration=tf(A));let{calculatedDuration:x}=A,M=1/0,I=1/0;null!==x&&(I=(M=x+s)*(o+1)-s);let H=0,L=e=>{if(null===P)return;y>0&&(P=Math.min(P,e)),y<0&&(P=Math.min(e-I/y,P));let r=(H=null!==S?S:Math.round(e-P)*y)-t*(y>=0?1:-1),i=y>=0?r<0:r>I;H=Math.max(r,0),"finished"===T&&null===S&&(H=I);let l=H,u=A;if(o){let e=Math.min(H,I)/M,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,o+1))%2&&("reverse"===a?(r=1-r,s&&(r-=s/M)):"mirror"===a&&(u=v)),l=(0,to.u)(0,1,r)*M}let h=i?{done:!1,value:n[0]}:u.next(l);g&&(h.value=g(h.value));let{done:d}=h;i||null===x||(d=y>=0?H>=I:H<=0);let f=null===S&&("finished"===T||"running"===T&&d);return c&&c(h.value),f&&R(),h},B=()=>{m&&m.stop(),m=void 0},C=()=>{T="idle",B(),f(),b(),P=w=null},R=()=>{T="finished",h&&h(),B(),f()},O=()=>{if(_)return;m||(m=r(L));let e=m.now();l&&l(),null!==S?P=e-S:P&&"finished"!==T||(P=e),"finished"===T&&b(),w=P,S=null,T="running",m.start()};e&&O();let N={then:(e,t)=>p.then(e,t),get time(){return eG(H)},set time(newTime){H=newTime=eU(newTime),null===S&&m&&0!==y?P=m.now()-newTime/y:S=newTime},get duration(){return eG(null===A.calculatedDuration?tf(A):A.calculatedDuration)},get speed(){return y},set speed(newSpeed){if(newSpeed===y||!m)return;y=newSpeed,N.time=eG(H)},get state(){return T},play:O,pause:()=>{T="paused",S=H},stop:()=>{_=!0,"idle"!==T&&(T="idle",u&&u(),C())},cancel:()=>{null!==w&&L(w),C()},complete:()=>{T="finished"},sample:e=>(P=0,L(e))};return N}let tg=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),tv=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ty=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&eZ[t]||eW(t)||Array.isArray(t)&&t.every(e))}(t.ease),t_={type:"spring",stiffness:500,damping:25,restSpeed:10},tb=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),tE={type:"keyframes",duration:.8},tA={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tT=(e,{keyframes:t})=>t.length>2?tE:I.has(e)?e.startsWith("scale")?tb(t[1]):t_:tA;var tS=r(282);let tP=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tS.P.test(t)||"0"===t)&&!t.startsWith("url("));var tw=r(5423);let tx=new Set(["brightness","contrast","saturate","opacity"]);function tM(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(tw.KP)||[];if(!n)return e;let i=r.replace(n,""),o=tx.has(t)?1:0;return n!==r&&(o*=100),t+"("+o+i+")"}let tI=/([a-z-]*)\(.*?\)/g,tH={...tS.P,getAnimatableNone:e=>{let t=e.match(tI);return t?t.map(tM).join(" "):e}};var tL=r(236);let tB={...F,color:tL.$,backgroundColor:tL.$,outlineColor:tL.$,fill:tL.$,stroke:tL.$,borderColor:tL.$,borderTopColor:tL.$,borderRightColor:tL.$,borderBottomColor:tL.$,borderLeftColor:tL.$,filter:tH,WebkitFilter:tH},tC=e=>tB[e];function tR(e,t){let r=tC(e);return r!==tH&&(r=tS.P),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let tO=e=>/^0[^.\s]+$/.test(e);function tN(e,t){return e[t]||e.default||e}let tD={skipAnimations:!1},tk=(e,t,r,n={})=>i=>{let o=tN(n,e)||{},s=o.delay||n.delay||0,{elapsed:a=0}=n;a-=eU(s);let l=function(e,t,r,n){let i,o;let s=tP(t,r);i=Array.isArray(r)?[...r]:[null,r];let a=void 0!==n.from?n.from:e.get(),l=[];for(let e=0;e<i.length;e++){var u;null===i[e]&&(i[e]=0===e?a:i[e-1]),("number"==typeof(u=i[e])?0===u:null!==u?"none"===u||"0"===u||tO(u):void 0)&&l.push(e),"string"==typeof i[e]&&"none"!==i[e]&&"0"!==i[e]&&(o=i[e])}if(s&&l.length&&o)for(let e=0;e<l.length;e++)i[l[e]]=tR(t,o);return i}(t,e,r,o),u=l[0],h=l[l.length-1],c=tP(e,u),d=tP(e,h);(0,eV.K)(c===d,`You are trying to animate ${e} from "${u}" to "${h}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${h} via the \`style\` property.`);let f={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&(f={...f,...tT(e,f)}),f.duration&&(f.duration=eU(f.duration)),f.repeatDelay&&(f.repeatDelay=eU(f.repeatDelay)),!c||!d||ej.current||!1===o.type||tD.skipAnimations)return function({keyframes:e,delay:t,onUpdate:r,onComplete:n}){let i=()=>(r&&r(e[e.length-1]),n&&n(),{time:0,speed:1,duration:0,play:eI.Z,pause:eI.Z,stop:eI.Z,then:e=>(e(),Promise.resolve()),cancel:eI.Z,complete:eI.Z});return t?tm({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}(ej.current?{...f,delay:0}:f);if(!n.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let r=function(e,t,{onUpdate:r,onComplete:n,...i}){let o,s;if(!(tg()&&tv.has(t)&&!i.repeatDelay&&"mirror"!==i.repeatType&&0!==i.damping&&"inertia"!==i.type))return!1;let a=!1,l=!1,u=()=>{s=new Promise(e=>{o=e})};u();let{keyframes:h,duration:c=300,ease:d,times:f}=i;if(ty(t,i)){let e=tm({...i,repeat:0,delay:0}),t={done:!1,value:h[0]},r=[],n=0;for(;!t.done&&n<2e4;)t=e.sample(n),r.push(t.value),n+=10;f=void 0,h=r,c=n-10,d="linear"}let p=function(e,t,r,{delay:n=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){let u={[t]:r};l&&(u.offset=l);let h=function e(t){if(t)return eW(t)?eX(t):Array.isArray(t)?t.map(e):eZ[t]}(a);return Array.isArray(h)&&(u.easing=h),e.animate(u,{delay:n,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(e.owner.current,t,h,{...i,duration:c,ease:d,times:f}),m=()=>{l=!1,p.cancel()},g=()=>{l=!0,eh.Wi.update(m),o(),u()};return p.onfinish=()=>{l||(e.set(function(e,{repeat:t,repeatType:r="loop"}){let n=t&&"loop"!==r&&t%2==1?0:e.length-1;return e[n]}(h,i)),n&&n(),g())},{then:(e,t)=>s.then(e,t),attachTimeline:e=>(p.timeline=e,p.onfinish=null,eI.Z),get time(){return eG(p.currentTime||0)},set time(newTime){p.currentTime=eU(newTime)},get speed(){return p.playbackRate},set speed(newSpeed){p.playbackRate=newSpeed},get duration(){return eG(c)},play:()=>{a||(p.play(),(0,eh.Pn)(m))},pause:()=>p.pause(),stop:()=>{if(a=!0,"idle"===p.playState)return;let{currentTime:t}=p;if(t){let r=tm({...i,autoplay:!1});e.setWithVelocity(r.sample(t-10).value,r.sample(t).value,10)}g()},complete:()=>{l||p.finish()},cancel:g}}(t,e,f);if(r)return r}return tm(f)};function tF(e){return!!(L(e)&&e.add)}let tV=e=>/^\-?\d*\.?\d+$/.test(e);var tU=r(4840);let tG=e=>t=>t.test(e),tj=[N.Rx,D.px,D.aQ,D.RW,D.vw,D.vh,{test:e=>"auto"===e,parse:e=>e}],tW=e=>tj.find(tG(e)),tX=[...tj,tL.$,tS.P],tZ=e=>tX.find(tG(e));function tz(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...a}=e.makeTargetAnimatable(t),l=e.getValue("willChange");n&&(o=n);let u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(let t in a){let n=e.getValue(t),i=a[t];if(!n||void 0===i||c&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(c,t))continue;let s={delay:r,elapsed:0,...tN(o||{},t)};if(window.HandoffAppearAnimations){let r=e.getProps()[h];if(r){let e=window.HandoffAppearAnimations(r,t,n,eh.Wi);null!==e&&(s.elapsed=e,s.isHandoff=!0)}}let d=!s.isHandoff&&!function(e,t){let r=e.get();if(!Array.isArray(t))return r!==t;for(let e=0;e<t.length;e++)if(t[e]!==r)return!0}(n,i);if("spring"===s.type&&(n.getVelocity()||s.velocity)&&(d=!1),n.animation&&(d=!1),d)continue;n.start(tk(t,n,i,e.shouldReduceMotion&&I.has(t)?{type:!1}:s));let f=n.animation;tF(l)&&(l.add(t),f.then(()=>l.remove(t))),u.push(f)}return s&&Promise.all(u).then(()=>{s&&function(e,t){let r=eF(e,t),{transitionEnd:n={},transition:i={},...o}=r?e.makeTargetAnimatable(r,!1):{};for(let t in o={...o,...n}){let r=ea(o[t]);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,(0,tU.BX)(r))}}(e,s)}),u}function t$(e,t,r={}){let n=eF(e,t,r.custom),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let o=n?()=>Promise.all(tz(e,n,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t,r=0,n=0,i=1,o){let s=[],a=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>a-e*n;return Array.from(e.variantChildren).sort(tY).forEach((e,n)=>{e.notify("AnimationStart",t),s.push(t$(e,t,{...o,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,o+n,s,a,r)}:()=>Promise.resolve(),{when:a}=i;if(!a)return Promise.all([o(),s(r.delay)]);{let[e,t]="beforeChildren"===a?[o,s]:[s,o];return e().then(()=>t())}}function tY(e,t){return e.sortNodePosition(t)}let tq=[...p].reverse(),tK=p.length;function tQ(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class tJ extends eS{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>t$(e,t,r)));else if("string"==typeof t)n=t$(e,t,r);else{let i="function"==typeof t?eF(e,t,r.custom):t;n=Promise.all(tz(e,i,r))}return n.then(()=>e.notify("AnimationComplete",t))})(e,t,r))),r={animate:tQ(!0),whileInView:tQ(),whileHover:tQ(),whileTap:tQ(),whileDrag:tQ(),whileFocus:tQ(),exit:tQ()},n=!0,i=(t,r)=>{let n=eF(e,r);if(n){let{transition:e,transitionEnd:r,...i}=n;t={...t,...i,...r}}return t};function o(o,s){let a=e.getProps(),l=e.getVariantContext(!0)||{},u=[],h=new Set,c={},p=1/0;for(let t=0;t<tK;t++){var m;let g=tq[t],v=r[g],y=void 0!==a[g]?a[g]:l[g],_=d(y),b=g===s?v.isActive:null;!1===b&&(p=t);let E=y===l[g]&&y!==a[g]&&_;if(E&&n&&e.manuallyAnimateOnMount&&(E=!1),v.protectedKeys={...c},!v.isActive&&null===b||!y&&!v.prevProp||f(y)||"boolean"==typeof y)continue;let A=(m=v.prevProp,("string"==typeof y?y!==m:!!Array.isArray(y)&&!ek(y,m))||g===s&&v.isActive&&!E&&_||t>p&&_),T=!1,S=Array.isArray(y)?y:[y],P=S.reduce(i,{});!1===b&&(P={});let{prevResolvedValues:w={}}=v,x={...w,...P},M=e=>{A=!0,h.has(e)&&(T=!0,h.delete(e)),v.needsAnimating[e]=!0};for(let e in x){let t=P[e],r=w[e];if(!c.hasOwnProperty(e))(eo(t)&&eo(r)?ek(t,r):t===r)?void 0!==t&&h.has(e)?M(e):v.protectedKeys[e]=!0:void 0!==t?M(e):h.add(e)}v.prevProp=y,v.prevResolvedValues=P,v.isActive&&(c={...c,...P}),n&&e.blockInitialAnimation&&(A=!1),A&&(!E||T)&&u.push(...S.map(e=>({animation:e,options:{type:g,...o}})))}if(h.size){let t={};h.forEach(r=>{let n=e.getBaseTarget(r);void 0!==n&&(t[r]=n)}),u.push({animation:t})}let g=!!u.length;return n&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(g=!1),n=!1,g?t(u):Promise.resolve()}return{animateChanges:o,setActive:function(t,n,i){var s;if(r[t].isActive===n)return Promise.resolve();null===(s=e.variantChildren)||void 0===s||s.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,n)}),r[t].isActive=n;let a=o(i,t);for(let e in r)r[e].protectedKeys={};return a},setAnimateFunction:function(r){t=r(e)},getState:()=>r}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),f(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}let t0=0;class t1 extends eS{constructor(){super(...arguments),this.id=t0++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:r}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let i=this.node.animationState.setActive("exit",!e,{custom:null!=r?r:this.node.getProps().custom});t&&!e&&i.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let t2=(e,t)=>Math.abs(e-t);class t5{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=t6(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(t2(e.x,t.x)**2+t2(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=eh.frameData;this.history.push({...n,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=t8(t,this.transformPagePoint),eh.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=t6("pointercancel"===e.type?this.lastMoveEventInfo:t8(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,o),n&&n(e,o)},!ep(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let o=t8(em(e),this.transformPagePoint),{point:s}=o,{timestamp:a}=eh.frameData;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,t6(o,this.history)),this.removeListeners=(0,ey.z)(ev(this.contextWindow,"pointermove",this.handlePointerMove),ev(this.contextWindow,"pointerup",this.handlePointerUp),ev(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,eh.Pn)(this.updatePoint)}}function t8(e,t){return t?{point:t(e.point)}:e}function t3(e,t){return{x:e.x-t.x,y:e.y-t.y}}function t6({point:e},t){return{point:e,delta:t3(e,t9(t)),offset:t3(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=t9(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>eU(.1)));)r--;if(!n)return{x:0,y:0};let o=eG(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,0)}}function t9(e){return e[e.length-1]}function t7(e){return e.max-e.min}function t4(e,t=0,r=.01){return Math.abs(e-t)<=r}function re(e,t,r,n=.5){e.origin=n,e.originPoint=(0,te.C)(t.min,t.max,e.origin),e.scale=t7(r)/t7(t),(t4(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=(0,te.C)(r.min,r.max,e.origin)-e.originPoint,(t4(e.translate)||isNaN(e.translate))&&(e.translate=0)}function rt(e,t,r,n){re(e.x,t.x,r.x,n?n.originX:void 0),re(e.y,t.y,r.y,n?n.originY:void 0)}function rr(e,t,r){e.min=r.min+t.min,e.max=e.min+t7(t)}function rn(e,t,r){e.min=t.min-r.min,e.max=e.min+t7(t)}function ri(e,t,r){rn(e.x,t.x,r.x),rn(e.y,t.y,r.y)}function ro(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function rs(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function ra(e,t,r){return{min:rl(e,t),max:rl(e,r)}}function rl(e,t){return"number"==typeof e?e:e[t]||0}let ru=()=>({translate:0,scale:1,origin:0,originPoint:0}),rh=()=>({x:ru(),y:ru()}),rc=()=>({min:0,max:0}),rd=()=>({x:rc(),y:rc()});function rf(e){return[e("x"),e("y")]}function rp({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function rm(e){return void 0===e||1===e}function rg({scale:e,scaleX:t,scaleY:r}){return!rm(e)||!rm(t)||!rm(r)}function rv(e){return rg(e)||ry(e)||e.z||e.rotate||e.rotateX||e.rotateY}function ry(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function r_(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function rb(e,t=0,r=1,n,i){e.min=r_(e.min,t,r,n,i),e.max=r_(e.max,t,r,n,i)}function rE(e,{x:t,y:r}){rb(e.x,t.translate,t.scale,t.originPoint),rb(e.y,r.translate,r.scale,r.originPoint)}function rA(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function rT(e,t){e.min=e.min+t,e.max=e.max+t}function rS(e,t,[r,n,i]){let o=void 0!==t[i]?t[i]:.5,s=(0,te.C)(e.min,e.max,o);rb(e,t[r],t[n],s,t.scale)}let rP=["x","scaleX","originX"],rw=["y","scaleY","originY"];function rx(e,t){rS(e.x,t,rP),rS(e.y,t,rw)}function rM(e,t){return rp(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let rI=({current:e})=>e?e.ownerDocument.defaultView:null,rH=new WeakMap;class rL{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rd(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new t5(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(em(e,"page").point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=eA(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rf(e=>{let t=this.getAxisMotionValue(e).get()||0;if(D.aQ.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];if(n){let e=t7(n);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),i&&eh.Wi.update(()=>i(e,t),!1,!0);let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:s}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>rf(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:rI(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&eh.Wi.update(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!rB(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?(0,te.C)(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?(0,te.C)(r,e,n.max):Math.min(e,r)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&c(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:ro(e.x,r,i),y:ro(e.y,t,n)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:ra(e,"left","right"),y:ra(e,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rf(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!c(t))return!1;let n=t.current;(0,eV.k)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,r){let n=rM(e,r),{scroll:i}=t;return i&&(rT(n.x,i.offset.x),rT(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),s={x:rs((e=i.layout.layoutBox).x,o.x),y:rs(e.y,o.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=rp(e))}return s}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(rf(s=>{if(!rB(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[s]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return r.start(tk(e,r,0,t))}stopAnimation(){rf(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rf(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){rf(t=>{let{drag:r}=this.getProps();if(!rB(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[t];i.set(e[t]-(0,te.C)(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!c(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rf(e=>{let t=this.getAxisMotionValue(e);if(t){let r=t.get();n[e]=function(e,t){let r=.5,n=t7(e),i=t7(t);return i>n?r=(0,tt.Y)(t.min,t.max-n,e.min):n>i&&(r=(0,tt.Y)(e.min,e.max-i,t.min)),(0,to.u)(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rf(t=>{if(!rB(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];r.set((0,te.C)(i,o,n[t]))})}addListeners(){if(!this.visualElement.current)return;rH.set(this.visualElement,this);let e=ev(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();c(e)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),t();let i=ef(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rf(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function rB(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class rC extends eS{constructor(e){super(e),this.removeGroupControls=eI.Z,this.removeListeners=eI.Z,this.controls=new rL(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eI.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let rR=e=>(t,r)=>{e&&eh.Wi.update(()=>e(t,r))};class rO extends eS{constructor(){super(...arguments),this.removePointerDownListener=eI.Z}onPointerDown(e){this.session=new t5(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rI(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:rR(e),onStart:rR(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&eh.Wi.update(()=>n(e,t))}}}mount(){this.removePointerDownListener=ev(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let rN={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rD(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let rk={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!D.px.test(e))return e;e=parseFloat(e)}let r=rD(e,t.target.x),n=rD(e,t.target.y);return`${r}% ${n}%`}};class rF extends n.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;Object.assign(x,rU),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rN.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,n||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent===i||(i?o.promote():o.relegate()||eh.Wi.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rV(e){let[t,r]=function(){let e=(0,n.useContext)(s.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:r,register:i}=e,o=(0,n.useId)();return!t&&r?[!1,()=>r&&r(o)]:[!0]}(),i=(0,n.useContext)(A.p);return n.createElement(rF,{...e,layoutGroup:i,switchLayoutGroup:(0,n.useContext)(T),isPresent:t,safeToRemove:r})}let rU={borderRadius:{...rk,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rk,borderTopRightRadius:rk,borderBottomLeftRadius:rk,borderBottomRightRadius:rk,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=tS.P.parse(e);if(n.length>5)return e;let i=tS.P.createTransformer(e),o="number"!=typeof n[0]?1:0,s=r.x.scale*t.x,a=r.y.scale*t.y;n[0+o]/=s,n[1+o]/=a;let l=(0,te.C)(s,a,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}};var rG=r(777);let rj=["TopLeft","TopRight","BottomLeft","BottomRight"],rW=rj.length,rX=e=>"string"==typeof e?parseFloat(e):e,rZ=e=>"number"==typeof e||D.px.test(e);function rz(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let r$=rq(0,.5,e2),rY=rq(.5,.95,eI.Z);function rq(e,t,r){return n=>n<e?0:n>t?1:r((0,tt.Y)(e,t,n))}function rK(e,t){e.min=t.min,e.max=t.max}function rQ(e,t){rK(e.x,t.x),rK(e.y,t.y)}function rJ(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function r0(e,t,[r,n,i],o,s){!function(e,t=0,r=1,n=.5,i,o=e,s=e){if(D.aQ.test(t)&&(t=parseFloat(t),t=(0,te.C)(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=(0,te.C)(o.min,o.max,n);e===o&&(a-=t),e.min=rJ(e.min,t,r,a,i),e.max=rJ(e.max,t,r,a,i)}(e,t[r],t[n],t[i],t.scale,o,s)}let r1=["x","scaleX","originX"],r2=["y","scaleY","originY"];function r5(e,t,r,n){r0(e.x,t,r1,r?r.x:void 0,n?n.x:void 0),r0(e.y,t,r2,r?r.y:void 0,n?n.y:void 0)}function r8(e){return 0===e.translate&&1===e.scale}function r3(e){return r8(e.x)&&r8(e.y)}function r6(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function r9(e){return t7(e.x)/t7(e.y)}var r7=r(2840);class r4{constructor(){this.members=[]}add(e){(0,r7.y4)(this.members,e),e.scheduleRender()}remove(e){if((0,r7.cl)(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function ne(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(n=`translate3d(${i}px, ${o}px, 0) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:e,rotateX:t,rotateY:i}=r;e&&(n+=`rotate(${e}deg) `),t&&(n+=`rotateX(${t}deg) `),i&&(n+=`rotateY(${i}deg) `)}let s=e.x.scale*t.x,a=e.y.scale*t.y;return(1!==s||1!==a)&&(n+=`scale(${s}, ${a})`),n||"none"}let nt=(e,t)=>e.depth-t.depth;class nr{constructor(){this.children=[],this.isDirty=!1}add(e){(0,r7.y4)(this.children,e),this.isDirty=!0}remove(e){(0,r7.cl)(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nt),this.isDirty=!1,this.children.forEach(e)}}let nn=["","X","Y","Z"],ni={visibility:"hidden"},no=0,ns={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function na({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=null==t?void 0:t()){this.id=no++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ns.totalNodes=ns.resolvedTargetDeltas=ns.recalculatedProjection=0,this.nodes.forEach(nh),this.nodes.forEach(nv),this.nodes.forEach(ny),this.nodes.forEach(nc),window.MotionDebug&&window.MotionDebug.record(ns)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nr)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new rG.L),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(i||n)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=performance.now(),n=({timestamp:t})=>{let i=t-r;i>=250&&((0,eh.Pn)(n),e(i-250))};return eh.Wi.read(n,!0),()=>(0,eh.Pn)(n)}(n,0),rN.hasAnimatedSinceResize&&(rN.hasAnimatedSinceResize=!1,this.nodes.forEach(ng))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&o&&(n||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||o.getDefaultTransition()||nS,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!r6(this.targetLayout,n)||r,u=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...tN(i,"layout"),onPlay:s,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||ng(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,eh.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n_),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nf);return}this.isUpdating||this.nodes.forEach(np),this.isUpdating=!1,this.nodes.forEach(nm),this.nodes.forEach(nl),this.nodes.forEach(nu),this.clearAllSnapshots();let e=performance.now();eh.frameData.delta=(0,to.u)(0,1e3/60,e-eh.frameData.timestamp),eh.frameData.timestamp=e,eh.frameData.isProcessing=!0,eh.S6.update.process(eh.frameData),eh.S6.preRender.process(eh.frameData),eh.S6.render.process(eh.frameData),eh.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(nd),this.sharedNodes.forEach(nb)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eh.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eh.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rd(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:n(this.instance),offset:r(this.instance)})}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!r3(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;e&&(t||rv(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),nx((t=n).x),nx(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return rd();let t=e.measureViewportBox(),{scroll:r}=this.root;return r&&(rT(t.x,r.offset.x),rT(t.y,r.offset.y)),t}removeElementScroll(e){let t=rd();rQ(t,e);for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:o}=n;if(n!==this.root&&i&&o.layoutScroll){if(i.isRoot){rQ(t,e);let{scroll:r}=this.root;r&&(rT(t.x,-r.offset.x),rT(t.y,-r.offset.y))}rT(t.x,i.offset.x),rT(t.y,i.offset.y)}}return t}applyTransform(e,t=!1){let r=rd();rQ(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rx(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rv(n.latestValues)&&rx(r,n.latestValues)}return rv(this.latestValues)&&rx(r,this.latestValues),r}removeTransform(e){let t=rd();rQ(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!rv(r.latestValues))continue;rg(r.latestValues)&&r.updateSnapshot();let n=rd();rQ(n,r.measurePageBox()),r5(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rv(this.latestValues)&&r5(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eh.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,n,i;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o;if(!(e||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=eh.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rd(),this.relativeTargetOrigin=rd(),ri(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rQ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=rd(),this.targetWithTransforms=rd()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,i=this.relativeParent.target,rr(r.x,n.x,i.x),rr(r.y,n.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rQ(this.target,this.layout.layoutBox),rE(this.target,this.targetDelta)):rQ(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rd(),this.relativeTargetOrigin=rd(),ri(this.relativeTargetOrigin,this.target,e.target),rQ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ns.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rg(this.parent.latestValues)||ry(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===eh.frameData.timestamp&&(n=!1),n)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;rQ(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;(function(e,t,r,n=!1){let i,o;let s=r.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=r[a]).projectionDelta;let s=i.instance;(!s||!s.style||"contents"!==s.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rx(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,rE(e,o)),n&&rv(i.latestValues)&&rx(e,i.latestValues))}t.x=rA(t.x),t.y=rA(t.y)}})(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=rh(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=rh(),this.projectionDeltaWithTransform=rh());let u=this.projectionTransform;rt(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=ne(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==s||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),ns.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},s=rh();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=rd(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nT));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(nE(s.x,e.x,n),nE(s.y,e.y,n),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p;ri(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,nA(f.x,p.x,a.x,n),nA(f.y,p.y,a.y,n),r&&(u=this.relativeTarget,d=r,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),r||(r=rd()),rQ(r,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,r,n,i,o){i?(e.opacity=(0,te.C)(0,void 0!==r.opacity?r.opacity:1,r$(n)),e.opacityExit=(0,te.C)(void 0!==t.opacity?t.opacity:1,0,rY(n))):o&&(e.opacity=(0,te.C)(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let i=0;i<rW;i++){let o=`border${rj[i]}Radius`,s=rz(t,o),a=rz(r,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||rZ(s)===rZ(a)?(e[o]=Math.max((0,te.C)(rX(s),rX(a),n),0),(D.aQ.test(a)||D.aQ.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||r.rotate)&&(e.rotate=(0,te.C)(t.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,eh.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eh.Wi.update(()=>{rN.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){let n=L(0)?0:(0,tU.BX)(0);return n.start(tk("",n,1e3,r)),n.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&nM(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rd();let t=t7(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=t7(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}rQ(t,r),rx(t,i),rt(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new r4),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(t=!0),!t)return;let n={};for(let t=0;t<nn.length;t++){let i="rotate"+nn[t];r[i]&&(n[i]=r[i],e.setStaticValue(i,0))}for(let t in e.render(),n)e.setStaticValue(t,n[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ni;let n={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=el(null==e?void 0:e.pointerEvents)||"",n.transform=i?i(this.latestValues,""):"none",n;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=el(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!rv(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),n.transform=ne(this.projectionDeltaWithTransform,this.treeScale,s),i&&(n.transform=i(s,n.transform));let{x:a,y:l}=this.projectionDelta;for(let e in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?n.opacity=o===this?null!==(r=null!==(t=s.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:n.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,x){if(void 0===s[e])continue;let{correct:t,applyTo:r}=x[e],i="none"===n.transform?s[e]:t(s[e],o);if(r){let e=r.length;for(let t=0;t<e;t++)n[r[t]]=i}else n[e]=i}return this.options.layoutId&&(n.pointerEvents=o===this?el(null==e?void 0:e.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(nf),this.root.sharedNodes.clear()}}}function nl(e){e.updateLayout()}function nu(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:n}=e.layout,{animationType:i}=e.options,o=r.source!==e.layout.source;"size"===i?rf(e=>{let n=o?r.measuredBox[e]:r.layoutBox[e],i=t7(n);n.min=t[e].min,n.max=n.min+i}):nM(i,r.layoutBox,t)&&rf(n=>{let i=o?r.measuredBox[n]:r.layoutBox[n],s=t7(t[n]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+s)});let s=rh();rt(s,t,r.layoutBox);let a=rh();o?rt(a,e.applyTransform(n,!0),r.measuredBox):rt(a,t,r.layoutBox);let l=!r3(s),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let s=rd();ri(s,r.layoutBox,i.layoutBox);let a=rd();ri(a,t,o.layoutBox),r6(s,a)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nh(e){ns.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nc(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nd(e){e.clearSnapshot()}function nf(e){e.clearMeasurements()}function np(e){e.isLayoutDirty=!1}function nm(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function ng(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nv(e){e.resolveTargetDelta()}function ny(e){e.calcProjection()}function n_(e){e.resetRotation()}function nb(e){e.removeLeadSnapshot()}function nE(e,t,r){e.translate=(0,te.C)(t.translate,0,r),e.scale=(0,te.C)(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function nA(e,t,r,n){e.min=(0,te.C)(t.min,r.min,n),e.max=(0,te.C)(t.max,r.max,n)}function nT(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let nS={duration:.45,ease:[.4,0,.1,1]},nP=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),nw=nP("applewebkit/")&&!nP("chrome/")?Math.round:eI.Z;function nx(e){e.min=nw(e.min),e.max=nw(e.max)}function nM(e,t,r){return"position"===e||"preserve-aspect"===e&&!t4(r9(t),r9(r),.2)}let nI=na({attachResizeListener:(e,t)=>ef(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nH={current:void 0},nL=na({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!nH.current){let e=new nI({});e.mount(window),e.setOptions({layoutScroll:!0}),nH.current=e}return nH.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),nB=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nC(e,t,r=1){(0,eV.k)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,i]=function(e){let t=nB.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]}(e);if(!n)return;let o=window.getComputedStyle(t).getPropertyValue(n);if(o){let e=o.trim();return tV(e)?parseFloat(e):e}return(0,R.tm)(i)?nC(i,t,r+1):i}let nR=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nO=e=>nR.has(e),nN=e=>Object.keys(e).some(nO),nD=e=>e===N.Rx||e===D.px,nk=(e,t)=>parseFloat(e.split(", ")[t]),nF=(e,t)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/);if(i)return nk(i[1],t);{let t=n.match(/^matrix\((.+)\)$/);return t?nk(t[1],e):0}},nV=new Set(["x","y","z"]),nU=M.filter(e=>!nV.has(e)),nG={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:nF(4,13),y:nF(5,14)};nG.translateX=nG.x,nG.translateY=nG.y;let nj=(e,t,r)=>{let n=t.measureViewportBox(),i=getComputedStyle(t.current),{display:o}=i,s={};"none"===o&&t.setStaticValue("display",e.display||"block"),r.forEach(e=>{s[e]=nG[e](n,i)}),t.render();let a=t.measureViewportBox();return r.forEach(r=>{let n=t.getValue(r);n&&n.jump(s[r]),e[r]=nG[r](a,i)}),e},nW=(e,t,r={},n={})=>{t={...t},n={...n};let i=Object.keys(t).filter(nO),o=[],s=!1,a=[];if(i.forEach(i=>{let l;let u=e.getValue(i);if(!e.hasValue(i))return;let h=r[i],c=tW(h),d=t[i];if(eo(d)){let e=d.length,t=null===d[0]?1:0;c=tW(h=d[t]);for(let r=t;r<e&&null!==d[r];r++)l?(0,eV.k)(tW(d[r])===l,"All keyframes must be of the same type"):(l=tW(d[r]),(0,eV.k)(l===c||nD(c)&&nD(l),"Keyframes must be of the same dimension as the current value"))}else l=tW(d);if(c!==l){if(nD(c)&&nD(l)){let e=u.get();"string"==typeof e&&u.set(parseFloat(e)),"string"==typeof d?t[i]=parseFloat(d):Array.isArray(d)&&l===D.px&&(t[i]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):t[i]=c.transform(d):(s||(o=function(e){let t=[];return nU.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),s=!0),a.push(i),n[i]=void 0!==n[i]?n[i]:t[i],u.jump(d))}}),!a.length)return{target:t,transitionEnd:n};{let r=a.indexOf("height")>=0?window.pageYOffset:null,i=nj(t,e,a);return o.length&&o.forEach(([t,r])=>{e.getValue(t).set(r)}),e.render(),E.j&&null!==r&&window.scrollTo({top:r}),{target:i,transitionEnd:n}}},nX=(e,t,r,n)=>{let i=function(e,{...t},r){let n=e.current;if(!(n instanceof Element))return{target:t,transitionEnd:r};for(let i in r&&(r={...r}),e.values.forEach(e=>{let t=e.get();if(!(0,R.tm)(t))return;let r=nC(t,n);r&&e.set(r)}),t){let e=t[i];if(!(0,R.tm)(e))continue;let o=nC(e,n);o&&(t[i]=o,r||(r={}),void 0===r[i]&&(r[i]=e))}return{target:t,transitionEnd:r}}(e,t,n);return function(e,t,r,n){return nN(t)?nW(e,t,r,n):{target:t,transitionEnd:n}}(e,t=i.target,r,n=i.transitionEnd)},nZ={current:null},nz={current:!1},n$=new WeakMap,nY=Object.keys(b),nq=nY.length,nK=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],nQ=m.length;class nJ{constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>eh.Wi.render(this.render,!1,!0);let{latestValues:s,renderState:a}=i;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=a,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.isControllingVariants=g(t),this.isVariantNode=v(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(t,{});for(let e in u){let t=u[e];void 0!==s[e]&&L(t)&&(t.set(s[e],!1),tF(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,n$.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),nz.current||function(){if(nz.current=!0,E.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>nZ.current=e.matches;e.addListener(t),t()}else nZ.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nZ.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in n$.delete(this.current),this.projection&&this.projection.unmount(),(0,eh.Pn)(this.notifyUpdate),(0,eh.Pn)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let r=I.has(e),n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eh.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{n(),i()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},r,n,i){let o,s;for(let e=0;e<nq;e++){let r=nY[e],{isEnabled:n,Feature:i,ProjectionNode:a,MeasureLayout:l}=b[r];a&&(o=a),n(t)&&(!this.features[r]&&i&&(this.features[r]=new i(this)),l&&(s=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:r,drag:n,dragConstraints:s,layoutScroll:a,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:r,alwaysMeasureLayout:!!n||s&&c(s),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:i,layoutScroll:a,layoutRoot:l})}return s}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rd()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<nK.length;t++){let r=nK[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){let{willChange:n}=t;for(let i in t){let o=t[i],s=r[i];if(L(o))e.addValue(i,o),tF(n)&&n.add(i);else if(L(s))e.addValue(i,(0,tU.BX)(o,{owner:e})),tF(n)&&n.remove(i);else if(s!==o){if(e.hasValue(i)){let t=e.getValue(i);t.hasAnimated||t.set(o)}else{let t=e.getStaticValue(i);e.addValue(i,(0,tU.BX)(void 0!==t?t:o,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<nQ;e++){let r=m[e],n=this.props[r];(d(n)||!1===n)&&(t[r]=n)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=(0,tU.BX)(t,{owner:this}),this.addValue(e,r)),r}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:r}=this.props,n="string"==typeof r||"object"==typeof r?null===(t=en(this.props,r))||void 0===t?void 0:t[e]:void 0;if(r&&void 0!==n)return n;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||L(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new rG.L),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class n0 extends nJ{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...r},{transformValues:n},i){let o=function(e,t,r){let n={};for(let i in e){let e=function(e,t){if(t)return(t[e]||t.default||t).from}(i,t);if(void 0!==e)n[i]=e;else{let e=r.getValue(i);e&&(n[i]=e.get())}}return n}(r,e||{},this);if(n&&(t&&(t=n(t)),r&&(r=n(r)),o&&(o=n(o))),i){!function(e,t,r){var n,i;let o=Object.keys(t).filter(t=>!e.hasValue(t)),s=o.length;if(s)for(let a=0;a<s;a++){let s=o[a],l=t[s],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(i=null!==(n=r[s])&&void 0!==n?n:e.readValue(s))&&void 0!==i?i:t[s]),null!=u&&("string"==typeof u&&(tV(u)||tO(u))?u=parseFloat(u):!tZ(u)&&tS.P.test(l)&&(u=tR(s,l)),e.addValue(s,(0,tU.BX)(u,{owner:e})),void 0===r[s]&&(r[s]=u),null!==u&&e.setBaseTarget(s,u))}}(this,r,o);let e=nX(this,r,o,t);t=e.transitionEnd,r=e.target}return{transition:e,transitionEnd:t,...r}}}class n1 extends n0{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(I.has(t)){let e=tC(t);return e&&e.default||0}{let r=window.getComputedStyle(e),n=((0,R.f9)(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return rM(e,t)}build(e,t,r,n){V(e,t,r,n.transformTemplate)}scrapeMotionValuesFromProps(e,t){return et(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,r,n){Q(e,t,r,n)}}class n2 extends n0{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(I.has(t)){let e=tC(t);return e&&e.default||0}return t=J.has(t)?t:u(t),e.getAttribute(t)}measureInstanceViewportBox(){return rd()}scrapeMotionValuesFromProps(e,t){return er(e,t)}build(e,t,r,n){Y(e,t,r,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,r,n){ee(e,t,r,n)}mount(e){this.isSVGTag=K(e.tagName),super.mount(e)}}let n5=(e,t)=>w(e)?new n2(t,{enableHardwareAcceleration:!1}):new n1(t,{enableHardwareAcceleration:!0}),n8={animation:{Feature:tJ},exit:{Feature:t1},inView:{Feature:eD},tap:{Feature:eL},focus:{Feature:ex},hover:{Feature:ew},pan:{Feature:rO},drag:{Feature:rC,ProjectionNode:nL,MeasureLayout:rV},layout:{ProjectionNode:nL,MeasureLayout:rV}},n3=function(e){function t(t,r={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:u,Component:f}){e&&function(e){for(let t in e)b[t]={...b[t],...e[t]}}(e);let p=(0,n.forwardRef)(function(p,m){var v;let _;let b={...(0,n.useContext)(i._),...p,layoutId:function({layoutId:e}){let t=(0,n.useContext)(A.p).id;return t&&void 0!==e?t+"-"+e:e}(p)},{isStatic:S}=b,P=function(e){let{initial:t,animate:r}=function(e,t){if(g(e)){let{initial:t,animate:r}=e;return{initial:!1===t||d(t)?t:void 0,animate:d(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,n.useContext)(o));return(0,n.useMemo)(()=>({initial:t,animate:r}),[y(t),y(r)])}(p),w=u(p,S);if(!S&&E.j){P.visualElement=function(e,t,r,u){let{visualElement:c}=(0,n.useContext)(o),d=(0,n.useContext)(l),f=(0,n.useContext)(s.O),p=(0,n.useContext)(i._).reducedMotion,m=(0,n.useRef)();u=u||d.renderer,!m.current&&u&&(m.current=u(e,{visualState:t,parent:c,props:r,presenceContext:f,blockInitialAnimation:!!f&&!1===f.initial,reducedMotionConfig:p}));let g=m.current;(0,n.useInsertionEffect)(()=>{g&&g.update(r,f)});let v=(0,n.useRef)(!!(r[h]&&!window.HandoffComplete));return(0,a.L)(()=>{g&&(g.render(),v.current&&g.animationState&&g.animationState.animateChanges())}),g}(f,w,b,t);let r=(0,n.useContext)(T),u=(0,n.useContext)(l).strict;P.visualElement&&(_=P.visualElement.loadFeatures(b,u,e,r))}return n.createElement(o.Provider,{value:P},_&&P.visualElement?n.createElement(_,{visualElement:P.visualElement,...b}):null,r(f,p,(v=P.visualElement,(0,n.useCallback)(e=>{e&&w.mount&&w.mount(e),v&&(e?v.mount(e):v.unmount()),m&&("function"==typeof m?m(e):c(m)&&(m.current=e))},[v])),w,S,P.visualElement))});return p[S]=f,p}(e(t,r))}if("undefined"==typeof Proxy)return t;let r=new Map;return new Proxy(t,{get:(e,n)=>(r.has(n)||r.set(n,t(n)),r.get(n))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},r,i){return{...w(e)?ec:ed,preloadedFeatures:r,useRender:function(e=!1){return(t,r,i,{latestValues:o},s)=>{let a=(w(t)?function(e,t,r,i){let o=(0,n.useMemo)(()=>{let r=q();return Y(r,t,{enableHardwareAcceleration:!1},K(i),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};G(t,e.style,e),o.style={...t,...o.style}}return o}:function(e,t,r){let i={},o=function(e,t,r){let i=e.style||{},o={};return G(o,i,e),Object.assign(o,function({transformTemplate:e},t,r){return(0,n.useMemo)(()=>{let n=U();return V(n,t,{enableHardwareAcceleration:!r},e),Object.assign({},n.vars,n.style)},[t])}(e,t,r)),e.transformValues?e.transformValues(o):o}(e,t,r);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=o,i})(r,o,s,t),l={...function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(X(i)||!0===r&&W(i)||!t&&!W(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),...a,ref:i},{children:u}=r,h=(0,n.useMemo)(()=>L(u)?u.get():u,[u]);return(0,n.createElement)(t,{...l,children:h})}}(t),createVisualElement:i,Component:e}})(e,t,n8,n5))},8543:(e,t,r)=>{"use strict";r.d(t,{Xp:()=>s,f9:()=>i,tm:()=>o});let n=e=>t=>"string"==typeof t&&t.startsWith(e),i=n("--"),o=n("var(--"),s=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g},2840:(e,t,r)=>{"use strict";function n(e,t){-1===e.indexOf(t)&&e.push(t)}function i(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}r.d(t,{cl:()=>i,y4:()=>n})},2361:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});let n=(e,t,r)=>Math.min(Math.max(r,e),t)},4673:(e,t,r)=>{"use strict";r.d(t,{K:()=>i,k:()=>o});var n=r(4380);let i=n.Z,o=n.Z},8448:(e,t,r)=>{"use strict";r.d(t,{s:()=>w});var n=r(4673),i=r(236),o=r(2361),s=r(6331);function a(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}var l=r(9963),u=r(8185),h=r(2924);let c=(e,t,r)=>{let n=e*e;return Math.sqrt(Math.max(0,r*(t*t-n)+n))},d=[l.$,u.m,h.J],f=e=>d.find(t=>t.test(e));function p(e){let t=f(e);(0,n.k)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===h.J&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,o=0,s=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,l=2*r-n;i=a(l,n,e+1/3),o=a(l,n,e),s=a(l,n,e-1/3)}else i=o=s=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:n}}(r)),r}let m=(e,t)=>{let r=p(e),n=p(t),i={...r};return e=>(i.red=c(r.red,n.red,e),i.green=c(r.green,n.green,e),i.blue=c(r.blue,n.blue,e),i.alpha=(0,s.C)(r.alpha,n.alpha,e),u.m.transform(i))};var g=r(9022),v=r(282);let y=(e,t)=>r=>`${r>0?t:e}`;function _(e,t){return"number"==typeof e?r=>(0,s.C)(e,t,r):i.$.test(e)?m(e,t):e.startsWith("var(")?y(e,t):A(e,t)}let b=(e,t)=>{let r=[...e],n=r.length,i=e.map((e,r)=>_(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}},E=(e,t)=>{let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=_(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}},A=(e,t)=>{let r=v.P.createTransformer(t),i=(0,v.V)(e),o=(0,v.V)(t);return i.numVars===o.numVars&&i.numColors===o.numColors&&i.numNumbers>=o.numNumbers?(0,g.z)(b(i.values,o.values),r):((0,n.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),y(e,t))};var T=r(5018),S=r(4380);let P=(e,t)=>r=>(0,s.C)(e,t,r);function w(e,t,{clamp:r=!0,ease:s,mixer:a}={}){let l=e.length;if((0,n.k)(l===t.length,"Both input and output ranges must be the same length"),1===l)return()=>t[0];e[0]>e[l-1]&&(e=[...e].reverse(),t=[...t].reverse());let u=function(e,t,r){let n=[],o=r||function(e){if("number"==typeof e);else if("string"==typeof e)return i.$.test(e)?m:A;else if(Array.isArray(e))return b;else if("object"==typeof e)return E;return P}(e[0]),s=e.length-1;for(let r=0;r<s;r++){let i=o(e[r],e[r+1]);if(t){let e=Array.isArray(t)?t[r]||S.Z:t;i=(0,g.z)(e,i)}n.push(i)}return n}(t,s,a),h=u.length,c=t=>{let r=0;if(h>1)for(;r<e.length-2&&!(t<e[r+1]);r++);let n=(0,T.Y)(e[r],e[r+1],t);return u[r](n)};return r?t=>c((0,o.u)(e[0],e[l-1],t)):c}},8263:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});let n="undefined"!=typeof document},6331:(e,t,r)=>{"use strict";r.d(t,{C:()=>n});let n=(e,t,r)=>-r*e+r*t+e},4380:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=e=>e},9022:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});let n=(e,t)=>r=>t(e(r)),i=(...e)=>e.reduce(n)},5018:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n});let n=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n}},777:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var n=r(2840);class i{constructor(){this.subscriptions=[]}add(e){return(0,n.y4)(this.subscriptions,e),()=>(0,n.cl)(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},4749:(e,t,r)=>{"use strict";r.d(t,{h:()=>i});var n=r(7577);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2482:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var n=r(7577);let i=r(8263).j?n.useLayoutEffect:n.useEffect},8702:(e,t,r)=>{"use strict";function n(e,t){return t?1e3/t*e:0}r.d(t,{R:()=>n})},4840:(e,t,r)=>{"use strict";r.d(t,{BX:()=>u,S1:()=>a});var n=r(777),i=r(8702),o=r(805);let s=e=>!isNaN(parseFloat(e)),a={current:void 0};class l{constructor(e,t={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:r,timestamp:n}=o.frameData;this.lastUpdated!==n&&(this.timeDelta=r,this.lastUpdated=n,o.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>o.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=s(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new n.L);let r=this.events[e].add(t);return"change"===e?()=>{r(),o.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return a.current&&a.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?(0,i.R)(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function u(e,t){return new l(e,t)}},9963:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(8185);let i={test:(0,r(3996).i)("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:n.m.transform}},2924:(e,t,r)=>{"use strict";r.d(t,{J:()=>a});var n=r(7255),i=r(7162),o=r(5423),s=r(3996);let a={test:(0,s.i)("hsl","hue"),parse:(0,s.d)("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:s=1})=>"hsla("+Math.round(e)+", "+i.aQ.transform((0,o.Nw)(t))+", "+i.aQ.transform((0,o.Nw)(r))+", "+(0,o.Nw)(n.Fq.transform(s))+")"}},236:(e,t,r)=>{"use strict";r.d(t,{$:()=>a});var n=r(5423),i=r(9963),o=r(2924),s=r(8185);let a={test:e=>s.m.test(e)||i.$.test(e)||o.J.test(e),parse:e=>s.m.test(e)?s.m.parse(e):o.J.test(e)?o.J.parse(e):i.$.parse(e),transform:e=>(0,n.HD)(e)?e:e.hasOwnProperty("red")?s.m.transform(e):o.J.transform(e)}},8185:(e,t,r)=>{"use strict";r.d(t,{m:()=>u});var n=r(2361),i=r(7255),o=r(5423),s=r(3996);let a=e=>(0,n.u)(0,255,e),l={...i.Rx,transform:e=>Math.round(a(e))},u={test:(0,s.i)("rgb","red"),parse:(0,s.d)("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+l.transform(e)+", "+l.transform(t)+", "+l.transform(r)+", "+(0,o.Nw)(i.Fq.transform(n))+")"}},3996:(e,t,r)=>{"use strict";r.d(t,{d:()=>o,i:()=>i});var n=r(5423);let i=(e,t)=>r=>!!((0,n.HD)(r)&&n.mj.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),o=(e,t,r)=>i=>{if(!(0,n.HD)(i))return i;let[o,s,a,l]=i.match(n.KP);return{[e]:parseFloat(o),[t]:parseFloat(s),[r]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},282:(e,t,r)=>{"use strict";r.d(t,{P:()=>g,V:()=>d});var n=r(8543),i=r(4380),o=r(236),s=r(7255),a=r(5423);let l={regex:n.Xp,countKey:"Vars",token:"${v}",parse:i.Z},u={regex:a.dA,countKey:"Colors",token:"${c}",parse:o.$.parse},h={regex:a.KP,countKey:"Numbers",token:"${n}",parse:s.Rx.parse};function c(e,{regex:t,countKey:r,token:n,parse:i}){let o=e.tokenised.match(t);o&&(e["num"+r]=o.length,e.tokenised=e.tokenised.replace(t,n),e.values.push(...o.map(i)))}function d(e){let t=e.toString(),r={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&c(r,l),c(r,u),c(r,h),r}function f(e){return d(e).values}function p(e){let{values:t,numColors:r,numVars:n,tokenised:i}=d(e),s=t.length;return e=>{let t=i;for(let i=0;i<s;i++)t=i<n?t.replace(l.token,e[i]):i<n+r?t.replace(u.token,o.$.transform(e[i])):t.replace(h.token,(0,a.Nw)(e[i]));return t}}let m=e=>"number"==typeof e?0:e,g={test:function(e){var t,r;return isNaN(e)&&(0,a.HD)(e)&&((null===(t=e.match(a.KP))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(a.dA))||void 0===r?void 0:r.length)||0)>0},parse:f,createTransformer:p,getAnimatableNone:function(e){let t=f(e);return p(e)(t.map(m))}}},7255:(e,t,r)=>{"use strict";r.d(t,{Fq:()=>o,Rx:()=>i,bA:()=>s});var n=r(2361);let i={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},o={...i,transform:e=>(0,n.u)(0,1,e)},s={...i,default:1}},7162:(e,t,r)=>{"use strict";r.d(t,{$C:()=>h,RW:()=>o,aQ:()=>s,px:()=>a,vh:()=>l,vw:()=>u});var n=r(5423);let i=e=>({test:t=>(0,n.HD)(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),o=i("deg"),s=i("%"),a=i("px"),l=i("vh"),u=i("vw"),h={...s,parse:e=>s.parse(e)/100,transform:e=>s.transform(100*e)}},5423:(e,t,r)=>{"use strict";r.d(t,{HD:()=>a,KP:()=>i,Nw:()=>n,dA:()=>o,mj:()=>s});let n=e=>Math.round(1e5*e)/1e5,i=/(-)?([\d]*\.?[\d])+/g,o=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,s=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function a(e){return"string"==typeof e}},3519:(e,t,r)=>{"use strict";r.d(t,{c:()=>a});var n=r(7577),i=r(4840),o=r(3965),s=r(4749);function a(e){let t=(0,s.h)(()=>(0,i.BX)(e)),{isStatic:r}=(0,n.useContext)(o._);if(r){let[,t]=(0,n.useState)(e)}return t}},7290:(e,t,r)=>{"use strict";r.d(t,{H:()=>d});var n=r(8448);let i=e=>e&&"object"==typeof e&&e.mix,o=e=>i(e)?e.mix:void 0;var s=r(3519),a=r(2482),l=r(805);function u(e,t){let r=(0,s.c)(t()),n=()=>r.set(t());return n(),(0,a.L)(()=>{let t=()=>l.Wi.update(n,!1,!0),r=e.map(e=>e.on("change",t));return()=>{r.forEach(e=>e()),(0,l.Pn)(n)}}),r}var h=r(4749),c=r(4840);function d(e,t,r,i){if("function"==typeof e)return function(e){c.S1.current=[],e();let t=u(c.S1.current,e);return c.S1.current=void 0,t}(e);let s="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),r=t?0:-1,i=e[0+r],s=e[1+r],a=e[2+r],l=e[3+r],u=(0,n.s)(s,a,{mixer:o(a[0]),...l});return t?u(i):u}(t,r,i);return Array.isArray(e)?f(e,s):f([e],([e])=>s(e))}function f(e,t){let r=(0,h.h)(()=>[]);return u(e,()=>{r.length=0;let n=e.length;for(let t=0;t<n;t++)r[t]=e[t].get();return t(r)})}},277:(e,t,r)=>{"use strict";var n,i,o,s,a=null,l=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},u=function(){var e=l();return e&&e.activationStart||0},h=null,c=function(e){"hidden"===document.visibilityState&&h>-1&&(h="visibilitychange"===e.type?e.timeStamp:0,d())},d=function(){removeEventListener("visibilitychange",c,!0),removeEventListener("prerenderingchange",c,!0)},f={passive:!0,capture:!0},p=new Date,m=function(e,t){n||(n=t,i=e,o=new Date,y(removeEventListener),g())},g=function(){if(i>=0&&i<o-p){var e={entryType:"first-input",name:n.type,target:n.target,cancelable:n.cancelable,startTime:n.timeStamp,processingStart:n.timeStamp+i};s.forEach(function(t){t(e)}),s=[]}},v=function(e){if(e.cancelable){var t,r,n,i=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?(t=function(){m(i,e),n()},r=function(){n()},n=function(){removeEventListener("pointerup",t,f),removeEventListener("pointercancel",r,f)},addEventListener("pointerup",t,f),addEventListener("pointercancel",r,f)):m(i,e)}},y=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,v,f)})},_=0,b=null,E=0},551:(e,t,r)=>{"use strict";r.d(t,{Ue:()=>d});let n=e=>{let t;let r=new Set,n=(e,n)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=n?n:"object"!=typeof i||null===i)?i:Object.assign({},t,i),r.forEach(r=>r(t,e))}},i=()=>t,o={setState:n,getState:i,getInitialState:()=>s,subscribe:e=>(r.add(e),()=>r.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),r.clear()}},s=t=e(n,i,o);return o},i=e=>e?n(e):n;var o=r(7577),s=r(1508);let{useDebugValue:a}=o,{useSyncExternalStoreWithSelector:l}=s,u=!1,h=e=>e,c=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?i(e):e,r=(e,r)=>(function(e,t=h,r){r&&!u&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),u=!0);let n=l(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return a(n),n})(t,e,r);return Object.assign(r,t),r},d=e=>e?c(e):c},5251:(e,t,r)=>{"use strict";r.d(t,{tJ:()=>s});let n=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>n(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>n(t)(e)}}},i=(e,t)=>(r,i,o)=>{let s,a,l={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},u=!1,h=new Set,c=new Set;try{s=l.getStorage()}catch(e){}if(!s)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),r(...e)},i,o);let d=n(l.serialize),f=()=>{let e;let t=d({state:l.partialize({...i()}),version:l.version}).then(e=>s.setItem(l.name,e)).catch(t=>{e=t});if(e)throw e;return t},p=o.setState;o.setState=(e,t)=>{p(e,t),f()};let m=e((...e)=>{r(...e),f()},i,o),g=()=>{var e;if(!s)return;u=!1,h.forEach(e=>e(i()));let t=(null==(e=l.onRehydrateStorage)?void 0:e.call(l,i()))||void 0;return n(s.getItem.bind(s))(l.name).then(e=>{if(e)return l.deserialize(e)}).then(e=>{if(e){if("number"!=typeof e.version||e.version===l.version)return e.state;if(l.migrate)return l.migrate(e.state,e.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}}).then(e=>{var t;return r(a=l.merge(e,null!=(t=i())?t:m),!0),f()}).then(()=>{null==t||t(a,void 0),u=!0,c.forEach(e=>e(a))}).catch(e=>{null==t||t(void 0,e)})};return o.persist={setOptions:e=>{l={...l,...e},e.getStorage&&(s=e.getStorage())},clearStorage:()=>{null==s||s.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>g(),hasHydrated:()=>u,onHydrate:e=>(h.add(e),()=>{h.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},g(),a||m},o=(e,t)=>(r,i,o)=>{let s,a={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let n=e=>null===e?null:JSON.parse(e,void 0),i=null!=(t=r.getItem(e))?t:null;return i instanceof Promise?i.then(n):n(i)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,u=new Set,h=new Set,c=a.storage;if(!c)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),r(...e)},i,o);let d=()=>{let e=a.partialize({...i()});return c.setItem(a.name,{state:e,version:a.version})},f=o.setState;o.setState=(e,t)=>{f(e,t),d()};let p=e((...e)=>{r(...e),d()},i,o);o.getInitialState=()=>p;let m=()=>{var e,t;if(!c)return;l=!1,u.forEach(e=>{var t;return e(null!=(t=i())?t:p)});let o=(null==(t=a.onRehydrateStorage)?void 0:t.call(a,null!=(e=i())?e:p))||void 0;return n(c.getItem.bind(c))(a.name).then(e=>{if(e){if("number"!=typeof e.version||e.version===a.version)return[!1,e.state];if(a.migrate)return[!0,a.migrate(e.state,e.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[n,o]=e;if(r(s=a.merge(o,null!=(t=i())?t:p),!0),n)return d()}).then(()=>{null==o||o(s,void 0),s=i(),l=!0,h.forEach(e=>e(s))}).catch(e=>{null==o||o(void 0,e)})};return o.persist={setOptions:e=>{a={...a,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>m(),hasHydrated:()=>l,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(h.add(e),()=>{h.delete(e)})},a.skipHydration||m(),s||p},s=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?(console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),i(e,t)):o(e,t)}};