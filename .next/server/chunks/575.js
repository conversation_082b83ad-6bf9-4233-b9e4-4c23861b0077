exports.id=575,exports.ids=[575],exports.modules={6952:(e,i,t)=>{Promise.resolve().then(t.bind(t,3170)),Promise.resolve().then(t.bind(t,4323)),Promise.resolve().then(t.bind(t,1182)),Promise.resolve().then(t.bind(t,1545)),Promise.resolve().then(t.bind(t,9291)),Promise.resolve().then(t.bind(t,9910))},1182:(e,i,t)=>{"use strict";t.d(i,{AudioGrid:()=>c});var n=t(326),s=t(7577),a=t(3844),r=t(1135),o=t(4323);function c({audios:e,variant:i="default",columns:t="auto",showSearch:c=!0,showFilter:l=!0,showSort:f=!0,onAudioPlay:d,className:g,emptyMessage:u,loading:p=!1}){let h=(0,a.useTranslations)("common"),[m,v]=(0,s.useState)(""),[y,w]=(0,s.useState)("all"),[b,z]=(0,s.useState)("name"),E=(0,s.useMemo)(()=>Array.from(new Set(e.map(e=>e.category))).sort(),[e]),x=(0,s.useMemo)(()=>{let i=e;if(m.trim()){let e=m.toLowerCase().trim();i=i.filter(i=>Object.values(i.title).some(i=>i.toLowerCase().includes(e))||i.description&&Object.values(i.description).some(i=>i.toLowerCase().includes(e))||i.category.toLowerCase().includes(e)||i.tags?.some(i=>i.toLowerCase().includes(e)))}return"all"!==y&&(i=i.filter(e=>e.category===y)),i.sort((e,i)=>{switch(b){case"name":return(e.title.en||e.title.zh||"").localeCompare(i.title.en||i.title.zh||"");case"category":return e.category.localeCompare(i.category);case"rating":return(i.scientificRating||0)-(e.scientificRating||0);case"recent":return e.id.localeCompare(i.id);default:return 0}}),i},[e,m,y,b]),_=()=>{if("auto"===t)switch(i){case"compact":return"grid-cols-1";case"detailed":return"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";default:return"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"}return({1:"grid-cols-1",2:"grid-cols-1 sm:grid-cols-2",3:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"})[t]};return p?(0,n.jsxs)("div",{className:(0,r.W)("space-y-4",g),children:[(c||l||f)&&(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[c&&n.jsx("div",{className:"flex-1",children:n.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})}),l&&n.jsx("div",{className:"w-full sm:w-48",children:n.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})}),f&&n.jsx("div",{className:"w-full sm:w-48",children:n.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})})]}),n.jsx("div",{className:(0,r.W)("grid gap-4",_()),children:Array.from({length:8}).map((e,i)=>n.jsx("div",{className:"bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse",children:n.jsx("div",{className:"h-48"})},i))})]}):(0,n.jsxs)("div",{className:(0,r.W)("space-y-6",g),children:[(c||l||f)&&(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[c&&n.jsx("div",{className:"flex-1",children:(0,n.jsxs)("div",{className:"relative",children:[n.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:n.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),n.jsx("input",{type:"text",placeholder:h("searchAudios"),value:m,onChange:e=>v(e.target.value),className:(0,r.W)("block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","placeholder-gray-500 dark:placeholder-gray-400","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200")})]})}),l&&n.jsx("div",{className:"w-full sm:w-48",children:(0,n.jsxs)("select",{value:y,onChange:e=>w(e.target.value),className:(0,r.W)("block w-full px-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200"),children:[n.jsx("option",{value:"all",children:h("allCategories")}),E.map(e=>n.jsx("option",{value:e,children:e},e))]})}),f&&n.jsx("div",{className:"w-full sm:w-48",children:(0,n.jsxs)("select",{value:b,onChange:e=>z(e.target.value),className:(0,r.W)("block w-full px-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200"),children:[n.jsx("option",{value:"name",children:h("sortByName")}),n.jsx("option",{value:"category",children:h("sortByCategory")}),n.jsx("option",{value:"rating",children:h("sortByRating")}),n.jsx("option",{value:"recent",children:h("sortByRecent")})]})})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400",children:[n.jsx("span",{children:h("showingResults",{count:x.length,total:e.length})}),m&&n.jsx("button",{onClick:()=>v(""),className:"text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300",children:h("clearSearch")})]}),x.length>0?n.jsx("div",{className:(0,r.W)("grid gap-4",_()),children:x.map(e=>n.jsx(o.AudioCard,{audio:e,variant:i,showCategory:"all"===y,showTags:!0,showDuration:"detailed"===i,showDescription:"detailed"===i,onPlay:d},e.id))}):(0,n.jsxs)("div",{className:"text-center py-12",children:[n.jsx("div",{className:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",children:n.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:n.jsx("path",{d:"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"})})}),n.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:u||h("noAudiosFound")}),n.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:h(m?"tryDifferentSearch":"noAudiosAvailable")}),m&&n.jsx("button",{onClick:()=>v(""),className:(0,r.W)("inline-flex items-center px-4 py-2 border border-transparent","text-sm font-medium rounded-md text-white bg-amber-600","hover:bg-amber-700 focus:outline-none focus:ring-2","focus:ring-offset-2 focus:ring-amber-500 transition-colors"),children:h("clearSearch")})]})]})}},1506:(e,i,t)=>{"use strict";t.d(i,{Z:()=>a});var n=t(1159),s=t(9243);function a(e){return function(e,i){try{return(0,n.use)(i)}catch(i){throw i instanceof TypeError&&i.message.includes("Cannot read properties of null (reading 'use')")?Error("`".concat(e,"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components"),{cause:i}):i}}(e,(0,s.Z)())}},6442:(e,i,t)=>{"use strict";t.d(i,{Z:()=>o});var n=t(1159),s=t(7872),a=(0,n.cache)(function(e,i){return(0,s.eX)({...e,namespace:i})}),r=t(1506);function o(){for(var e=arguments.length,i=Array(e),t=0;t<e;t++)i[t]=arguments[t];let[n]=i;return a((0,r.Z)("useTranslations"),n)}},5013:(e,i,t)=>{"use strict";t.d(i,{Z:()=>r});var n=t(1159),s=t(7872),a=t(9243),r=(0,n.cache)(async function(e){let i,t;"string"==typeof e?i=e:e&&(t=e.locale,i=e.namespace);let n=await (0,a.Z)(t);return(0,s.eX)({...n,namespace:i,messages:n.messages})})},7681:(e,i,t)=>{"use strict";t.d(i,{F:()=>s});var n=t(8570);(0,n.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx#AudioCard`);let s=(0,n.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioGrid.tsx#AudioGrid`)},2078:(e,i,t)=>{"use strict";t.d(i,{Xg:()=>s});var n=t(8570);(0,n.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx#default`),(0,n.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx#default`);let s=(0,n.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/PageLayout.tsx#default`)},2443:(e,i,t)=>{"use strict";t.d(i,{N:()=>n});let n=[{id:"urban_highway",filename:"highway.mp3",category:"urban",title:{en:"Highway",zh:"高速公路"},description:{en:"Steady highway traffic sounds for focus and relaxation",zh:"稳定的高速公路交通声，专注与放松"},tags:["traffic","steady","focus"],duration:3600,scientificRating:8.2,sleepEffectiveness:7.8,focusEffectiveness:8.5},{id:"urban_busy_street",filename:"busy-street.mp3",category:"urban",title:{en:"Busy Street",zh:"繁忙街道"},description:{en:"Urban street ambience with moderate activity",zh:"城市街道环境音，中等活跃度"},tags:["urban","street","ambient"],duration:3600,scientificRating:7.9,sleepEffectiveness:7.2,focusEffectiveness:8.1},{id:"urban_traffic",filename:"traffic.mp3",category:"urban",title:{en:"Traffic",zh:"交通声"},description:{en:"General traffic sounds for urban atmosphere",zh:"一般交通声音，城市氛围"},tags:["traffic","urban","background"],duration:3600,scientificRating:8,sleepEffectiveness:7.5,focusEffectiveness:8.3},{id:"urban_road",filename:"road.mp3",category:"urban",title:{en:"Road",zh:"道路"},description:{en:"Road traffic with consistent rhythm",zh:"道路交通，节奏一致"},tags:["road","consistent","rhythm"],duration:3600,scientificRating:8.1,sleepEffectiveness:7.7,focusEffectiveness:8.4},{id:"urban_fireworks",filename:"fireworks.mp3",category:"urban",title:{en:"Fireworks",zh:"烟花"},description:{en:"Distant fireworks for celebration atmosphere",zh:"远处烟花声，庆祝氛围"},tags:["celebration","distant","festive"],duration:3600,scientificRating:7.5,sleepEffectiveness:6.8,focusEffectiveness:7.2},{id:"urban_ambulance_siren",filename:"ambulance-siren.mp3",category:"urban",title:{en:"Ambulance Siren",zh:"救护车警报"},description:{en:"Distant ambulance siren in urban environment",zh:"城市环境中的远处救护车警报"},tags:["siren","emergency","urban"],duration:3600,scientificRating:6.8,sleepEffectiveness:6,focusEffectiveness:6.5},{id:"urban_crowd",filename:"crowd.mp3",category:"urban",title:{en:"Crowd",zh:"人群"},description:{en:"Distant crowd chatter for social ambience",zh:"远处人群交谈声，社交氛围"},tags:["crowd","social","chatter"],duration:3600,scientificRating:7.6,sleepEffectiveness:7,focusEffectiveness:7.8},{id:"rain_on_leaves",filename:"rain-on-leaves.mp3",category:"rain",title:{en:"Rain on Leaves",zh:"雨打树叶"},description:{en:"Gentle rain falling on leaves for natural relaxation",zh:"轻柔雨声打在树叶上，自然放松"},tags:["gentle","natural","leaves"],duration:3600,scientificRating:9.1,sleepEffectiveness:9.3,focusEffectiveness:8.8},{id:"rain_on_tent",filename:"rain-on-tent.mp3",category:"rain",title:{en:"Rain on Tent",zh:"雨打帐篷"},description:{en:"Cozy rain sounds on tent fabric",zh:"雨声打在帐篷布上，温馨舒适"},tags:["cozy","tent","camping"],duration:3600,scientificRating:8.9,sleepEffectiveness:9.1,focusEffectiveness:8.6},{id:"rain_on_umbrella",filename:"rain-on-umbrella.mp3",category:"rain",title:{en:"Rain on Umbrella",zh:"雨打雨伞"},description:{en:"Rain drops hitting umbrella surface",zh:"雨滴打在雨伞表面"},tags:["umbrella","drops","rhythmic"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.4},{id:"rain_on_car_roof",filename:"rain-on-car-roof.mp3",category:"rain",title:{en:"Rain on Car Roof",zh:"雨打车顶"},description:{en:"Rain falling on car roof for cozy atmosphere",zh:"雨声打在车顶上，温馨氛围"},tags:["car","cozy","shelter"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.5},{id:"rain_on_window",filename:"rain-on-window.mp3",category:"rain",title:{en:"Rain on Window",zh:"雨打窗户"},description:{en:"Rain drops on window glass for indoor comfort",zh:"雨滴打在窗玻璃上，室内舒适感"},tags:["window","indoor","comfort"],duration:3600,scientificRating:9,sleepEffectiveness:9.2,focusEffectiveness:8.7},{id:"rain_heavy_rain",filename:"heavy-rain.mp3",category:"rain",title:{en:"Heavy Rain",zh:"大雨"},description:{en:"Intense rainfall for deep relaxation",zh:"强烈降雨，深度放松"},tags:["intense","powerful","relaxing"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:8.2},{id:"rain_thunder",filename:"thunder.mp3",category:"rain",title:{en:"Thunder",zh:"雷声"},description:{en:"Distant thunder with rain for dramatic atmosphere",zh:"远处雷声伴随雨声，戏剧性氛围"},tags:["thunder","dramatic","distant"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.1,focusEffectiveness:7.8},{id:"rain_light_rain",filename:"light-rain.mp3",category:"rain",title:{en:"Light Rain",zh:"轻雨"},description:{en:"Gentle light rain for peaceful relaxation",zh:"轻柔细雨，宁静放松"},tags:["gentle","soft","peaceful"],duration:3600,scientificRating:9.2,sleepEffectiveness:9.5,focusEffectiveness:8.9},{id:"transport_airplane",filename:"airplane.mp3",category:"transport",title:{en:"Airplane",zh:"飞机"},description:{en:"Steady airplane cabin ambience for travel relaxation",zh:"稳定的飞机客舱环境音，旅行放松"},tags:["airplane","cabin","travel"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.6,focusEffectiveness:8.8},{id:"transport_rowing_boat",filename:"rowing-boat.mp3",category:"transport",title:{en:"Rowing Boat",zh:"划船"},description:{en:"Peaceful rowing boat sounds on calm water",zh:"平静水面上的划船声，宁静祥和"},tags:["rowing","peaceful","water"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.5},{id:"transport_submarine",filename:"submarine.mp3",category:"transport",title:{en:"Submarine",zh:"潜水艇"},description:{en:"Deep underwater submarine ambience",zh:"深海潜水艇环境音"},tags:["submarine","underwater","deep"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8.6},{id:"transport_train",filename:"train.mp3",category:"transport",title:{en:"Train",zh:"火车"},description:{en:"Rhythmic train sounds for steady relaxation",zh:"有节奏的火车声，稳定放松"},tags:["train","rhythmic","steady"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8.9},{id:"transport_sailboat",filename:"sailboat.mp3",category:"transport",title:{en:"Sailboat",zh:"帆船"},description:{en:"Gentle sailboat on calm seas",zh:"平静海面上的帆船"},tags:["sailboat","calm","seas"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.6},{id:"transport_inside_train",filename:"inside-a-train.mp3",category:"transport",title:{en:"Inside a Train",zh:"火车内部"},description:{en:"Interior train ambience for travel comfort",zh:"火车内部环境音，旅行舒适感"},tags:["interior","travel","comfort"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.7},{id:"places_construction_site",filename:"construction-site.mp3",category:"places",title:{en:"Construction Site",zh:"建筑工地"},description:{en:"Distant construction sounds for industrial ambience",zh:"远处建筑声音，工业氛围"},tags:["construction","industrial","distant"],duration:3600,scientificRating:7.2,sleepEffectiveness:6.8,focusEffectiveness:7.5},{id:"places_underwater",filename:"underwater.mp3",category:"places",title:{en:"Underwater",zh:"水下"},description:{en:"Peaceful underwater ambience for deep relaxation",zh:"宁静的水下环境音，深度放松"},tags:["underwater","peaceful","deep"],duration:3600,scientificRating:8.9,sleepEffectiveness:9.1,focusEffectiveness:8.7},{id:"places_airport",filename:"airport.mp3",category:"places",title:{en:"Airport",zh:"机场"},description:{en:"Airport terminal ambience for travel atmosphere",zh:"机场候机厅环境音，旅行氛围"},tags:["airport","terminal","travel"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.4,focusEffectiveness:8.1},{id:"places_church",filename:"church.mp3",category:"places",title:{en:"Church",zh:"教堂"},description:{en:"Peaceful church ambience for meditation",zh:"宁静的教堂环境音，适合冥想"},tags:["church","peaceful","meditation"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.4},{id:"places_temple",filename:"temple.mp3",category:"places",title:{en:"Temple",zh:"寺庙"},description:{en:"Serene temple atmosphere for spiritual relaxation",zh:"宁静的寺庙氛围，精神放松"},tags:["temple","serene","spiritual"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.5},{id:"places_cafe",filename:"cafe.mp3",category:"places",title:{en:"Cafe",zh:"咖啡厅"},description:{en:"Cozy cafe ambience for work and relaxation",zh:"温馨的咖啡厅环境音，工作与放松"},tags:["cafe","cozy","work"],duration:3600,scientificRating:8.3,sleepEffectiveness:7.9,focusEffectiveness:8.7},{id:"nature_waterfall",filename:"waterfall.mp3",category:"nature",title:{en:"Waterfall",zh:"瀑布"},description:{en:"Powerful waterfall sounds for natural white noise",zh:"强劲的瀑布声，天然白噪音"},tags:["waterfall","powerful","white-noise"],duration:3600,scientificRating:9,sleepEffectiveness:9.2,focusEffectiveness:8.8},{id:"nature_walk_on_gravel",filename:"walk-on-gravel.mp3",category:"nature",title:{en:"Walk on Gravel",zh:"砾石路行走"},description:{en:"Footsteps on gravel path for meditative walking",zh:"砾石路上的脚步声，冥想式行走"},tags:["footsteps","gravel","meditative"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.5},{id:"nature_jungle",filename:"jungle.mp3",category:"nature",title:{en:"Jungle",zh:"丛林"},description:{en:"Rich jungle ambience with diverse natural sounds",zh:"丰富的丛林环境音，多样化自然声音"},tags:["jungle","rich","diverse"],duration:3600,scientificRating:8.8,sleepEffectiveness:8.6,focusEffectiveness:8.9},{id:"nature_howling_wind",filename:"howling-wind.mp3",category:"nature",title:{en:"Howling Wind",zh:"呼啸风声"},description:{en:"Strong wind sounds for dramatic natural atmosphere",zh:"强风声音，戏剧性自然氛围"},tags:["wind","strong","dramatic"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.2,focusEffectiveness:8.6},{id:"nature_waves",filename:"waves.mp3",category:"nature",title:{en:"Waves",zh:"海浪"},description:{en:"Gentle ocean waves for coastal relaxation",zh:"轻柔的海浪声，海岸放松"},tags:["waves","ocean","coastal"],duration:3600,scientificRating:9.1,sleepEffectiveness:9.3,focusEffectiveness:8.7},{id:"nature_wind_in_trees",filename:"wind-in-trees.mp3",category:"nature",title:{en:"Wind in Trees",zh:"树林风声"},description:{en:"Gentle wind through trees for forest atmosphere",zh:"轻风穿过树林，森林氛围"},tags:["wind","trees","forest"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:8.8},{id:"nature_wind",filename:"wind.mp3",category:"nature",title:{en:"Wind",zh:"风声"},description:{en:"Pure wind sounds for natural ambience",zh:"纯净的风声，自然环境音"},tags:["wind","pure","natural"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.8,focusEffectiveness:8.6},{id:"nature_campfire",filename:"campfire.mp3",category:"nature",title:{en:"Campfire",zh:"篝火"},description:{en:"Crackling campfire for cozy outdoor atmosphere",zh:"噼啪作响的篝火，温馨户外氛围"},tags:["campfire","crackling","cozy"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.2},{id:"nature_river",filename:"river.mp3",category:"nature",title:{en:"River",zh:"河流"},description:{en:"Flowing river sounds for peaceful relaxation",zh:"流水声，宁静放松"},tags:["river","flowing","peaceful"],duration:3600,scientificRating:9,sleepEffectiveness:9.1,focusEffectiveness:8.9},{id:"nature_droplets",filename:"droplets.mp3",category:"nature",title:{en:"Water Droplets",zh:"水滴"},description:{en:"Gentle water droplets for meditation",zh:"轻柔水滴声，适合冥想"},tags:["droplets","gentle","meditation"],duration:3600,scientificRating:9,sleepEffectiveness:9.3,focusEffectiveness:8.7},{id:"nature_walk_on_leaves",filename:"walk-on-leaves.mp3",category:"nature",title:{en:"Walk on Leaves",zh:"踩落叶"},description:{en:"Footsteps on autumn leaves for seasonal atmosphere",zh:"踩在秋叶上的脚步声，季节氛围"},tags:["footsteps","leaves","autumn"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8.3},{id:"nature_walk_in_snow",filename:"walk-in-snow.mp3",category:"nature",title:{en:"Walk in Snow",zh:"雪地行走"},description:{en:"Footsteps in snow for winter tranquility",zh:"雪地中的脚步声，冬日宁静"},tags:["footsteps","snow","winter"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8.4},{id:"noise_pink_noise",filename:"pink-noise.wav",category:"noise",title:{en:"Pink Noise",zh:"粉色噪音"},description:{en:"Balanced pink noise for sleep and concentration",zh:"平衡的粉色噪音，助眠与专注"},tags:["pink-noise","balanced","sleep"],duration:3600,scientificRating:9.2,sleepEffectiveness:9.4,focusEffectiveness:9.1},{id:"noise_white_noise",filename:"white-noise.wav",category:"noise",title:{en:"White Noise",zh:"白色噪音"},description:{en:"Classic white noise for sound masking",zh:"经典白色噪音，声音遮蔽"},tags:["white-noise","classic","masking"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:9.3},{id:"noise_brown_noise",filename:"brown-noise.wav",category:"noise",title:{en:"Brown Noise",zh:"棕色噪音"},description:{en:"Deep brown noise for focus and relaxation",zh:"深沉的棕色噪音，专注与放松"},tags:["brown-noise","deep","focus"],duration:3600,scientificRating:9,sleepEffectiveness:8.8,focusEffectiveness:9.2},{id:"animals_beehive",filename:"beehive.mp3",category:"animals",title:{en:"Beehive",zh:"蜂巢"},description:{en:"Gentle buzzing of bees in their hive",zh:"蜜蜂在蜂巢中轻柔的嗡嗡声"},tags:["bees","buzzing","gentle"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.5},{id:"animals_birds",filename:"birds.mp3",category:"animals",title:{en:"Birds",zh:"鸟鸣"},description:{en:"Cheerful bird songs for morning atmosphere",zh:"欢快的鸟鸣声，晨间氛围"},tags:["birds","cheerful","morning"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.2,focusEffectiveness:8.8},{id:"animals_dog_barking",filename:"dog-barking.mp3",category:"animals",title:{en:"Dog Barking",zh:"狗叫声"},description:{en:"Distant dog barking for neighborhood ambience",zh:"远处狗叫声，邻里氛围"},tags:["dog","barking","distant"],duration:3600,scientificRating:6.8,sleepEffectiveness:6.2,focusEffectiveness:7.1},{id:"animals_cat_purring",filename:"cat-purring.mp3",category:"animals",title:{en:"Cat Purring",zh:"猫咪呼噜声"},description:{en:"Soothing cat purring for comfort and relaxation",zh:"舒缓的猫咪呼噜声，带来安慰与放松"},tags:["cat","purring","soothing"],duration:3600,scientificRating:8.9,sleepEffectiveness:9.1,focusEffectiveness:8.4},{id:"animals_wolf",filename:"wolf.mp3",category:"animals",title:{en:"Wolf",zh:"狼嚎"},description:{en:"Distant wolf howling for wild atmosphere",zh:"远处狼嚎声，野性氛围"},tags:["wolf","howling","wild"],duration:3600,scientificRating:7.5,sleepEffectiveness:7.2,focusEffectiveness:7.8},{id:"animals_frog",filename:"frog.mp3",category:"animals",title:{en:"Frog",zh:"青蛙"},description:{en:"Peaceful frog sounds for pond atmosphere",zh:"宁静的青蛙声，池塘氛围"},tags:["frog","peaceful","pond"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.5,focusEffectiveness:8.1},{id:"animals_seagulls",filename:"seagulls.mp3",category:"animals",title:{en:"Seagulls",zh:"海鸥"},description:{en:"Seagull calls for coastal atmosphere",zh:"海鸥叫声，海岸氛围"},tags:["seagulls","coastal","calls"],duration:3600,scientificRating:8,sleepEffectiveness:7.8,focusEffectiveness:8.2},{id:"animals_crickets",filename:"crickets.mp3",category:"animals",title:{en:"Crickets",zh:"蟋蟀"},description:{en:"Rhythmic cricket sounds for summer nights",zh:"有节奏的蟋蟀声，夏夜氛围"},tags:["crickets","rhythmic","summer"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.3},{id:"animals_sheep",filename:"sheep.mp3",category:"animals",title:{en:"Sheep",zh:"羊群"},description:{en:"Gentle sheep sounds for pastoral atmosphere",zh:"温和的羊群声，田园氛围"},tags:["sheep","gentle","pastoral"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8},{id:"animals_horse_galopp",filename:"horse-galopp.mp3",category:"animals",title:{en:"Horse Gallop",zh:"马蹄声"},description:{en:"Rhythmic horse galloping for dynamic atmosphere",zh:"有节奏的马蹄声，动感氛围"},tags:["horse","gallop","rhythmic"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.4,focusEffectiveness:8.1},{id:"animals_cows",filename:"cows.mp3",category:"animals",title:{en:"Cows",zh:"牛群"},description:{en:"Peaceful cow sounds for farm atmosphere",zh:"宁静的牛群声，农场氛围"},tags:["cows","peaceful","farm"],duration:3600,scientificRating:8,sleepEffectiveness:8.2,focusEffectiveness:7.9},{id:"animals_owl",filename:"owl.mp3",category:"animals",title:{en:"Owl",zh:"猫头鹰"},description:{en:"Mysterious owl hooting for night atmosphere",zh:"神秘的猫头鹰叫声，夜晚氛围"},tags:["owl","mysterious","night"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.6,focusEffectiveness:8.2},{id:"animals_whale",filename:"whale.mp3",category:"animals",title:{en:"Whale",zh:"鲸鱼"},description:{en:"Deep whale songs for oceanic meditation",zh:"深沉的鲸鱼歌声，海洋冥想"},tags:["whale","deep","oceanic"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.6},{id:"animals_woodpecker",filename:"woodpecker.mp3",category:"animals",title:{en:"Woodpecker",zh:"啄木鸟"},description:{en:"Rhythmic woodpecker sounds for forest atmosphere",zh:"有节奏的啄木鸟声，森林氛围"},tags:["woodpecker","rhythmic","forest"],duration:3600,scientificRating:7.9,sleepEffectiveness:7.7,focusEffectiveness:8.3},{id:"animals_crows",filename:"crows.mp3",category:"animals",title:{en:"Crows",zh:"乌鸦"},description:{en:"Distant crow calls for atmospheric ambience",zh:"远处乌鸦叫声，氛围环境音"},tags:["crows","distant","atmospheric"],duration:3600,scientificRating:7.3,sleepEffectiveness:7,focusEffectiveness:7.6},{id:"animals_chickens",filename:"chickens.mp3",category:"animals",title:{en:"Chickens",zh:"鸡群"},description:{en:"Gentle chicken sounds for farmyard atmosphere",zh:"温和的鸡群声，农家院氛围"},tags:["chickens","gentle","farmyard"],duration:3600,scientificRating:7.7,sleepEffectiveness:7.5,focusEffectiveness:7.9},{id:"things_bubbles",filename:"bubbles.mp3",category:"things",title:{en:"Bubbles",zh:"气泡"},description:{en:"Gentle bubble sounds for playful relaxation",zh:"轻柔的气泡声，轻松愉快的放松"},tags:["bubbles","gentle","playful"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.5,focusEffectiveness:8.1},{id:"things_boiling_water",filename:"boiling-water.mp3",category:"things",title:{en:"Boiling Water",zh:"沸水"},description:{en:"Steady boiling water sounds for kitchen comfort",zh:"稳定的沸水声，厨房舒适感"},tags:["boiling","water","steady"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.4},{id:"things_wind_chimes",filename:"wind-chimes.mp3",category:"things",title:{en:"Wind Chimes",zh:"风铃"},description:{en:"Melodic wind chimes for peaceful atmosphere",zh:"悦耳的风铃声，宁静氛围"},tags:["wind-chimes","melodic","peaceful"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.5},{id:"things_vinyl_effect",filename:"vinyl-effect.mp3",category:"things",title:{en:"Vinyl Effect",zh:"黑胶唱片"},description:{en:"Nostalgic vinyl record crackling for vintage atmosphere",zh:"怀旧的黑胶唱片噼啪声，复古氛围"},tags:["vinyl","nostalgic","vintage"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8.6},{id:"things_tuning_radio",filename:"tuning-radio.mp3",category:"things",title:{en:"Tuning Radio",zh:"调频收音机"},description:{en:"Radio static and tuning for retro ambience",zh:"收音机静电和调频声，复古环境音"},tags:["radio","static","retro"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.6,focusEffectiveness:8},{id:"things_ceiling_fan",filename:"ceiling-fan.mp3",category:"things",title:{en:"Ceiling Fan",zh:"吊扇"},description:{en:"Steady ceiling fan for consistent white noise",zh:"稳定的吊扇声，持续白噪音"},tags:["fan","steady","white-noise"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8.8},{id:"things_washing_machine",filename:"washing-machine.mp3",category:"things",title:{en:"Washing Machine",zh:"洗衣机"},description:{en:"Rhythmic washing machine for household comfort",zh:"有节奏的洗衣机声，家庭舒适感"},tags:["washing-machine","rhythmic","household"],duration:3600,scientificRating:8,sleepEffectiveness:8.2,focusEffectiveness:8.3},{id:"things_clock",filename:"clock.mp3",category:"things",title:{en:"Clock",zh:"时钟"},description:{en:"Steady clock ticking for time meditation",zh:"稳定的时钟滴答声，时间冥想"},tags:["clock","ticking","meditation"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.6,focusEffectiveness:8.7},{id:"things_morse_code",filename:"morse-code.mp3",category:"things",title:{en:"Morse Code",zh:"摩尔斯电码"},description:{en:"Rhythmic morse code for focus and concentration",zh:"有节奏的摩尔斯电码，专注与集中"},tags:["morse-code","rhythmic","focus"],duration:3600,scientificRating:7.9,sleepEffectiveness:7.5,focusEffectiveness:8.4},{id:"things_windshield_wipers",filename:"windshield-wipers.mp3",category:"things",title:{en:"Windshield Wipers",zh:"雨刷器"},description:{en:"Steady windshield wipers for car comfort",zh:"稳定的雨刷器声，车内舒适感"},tags:["wipers","steady","car"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.2},{id:"things_typewriter",filename:"typewriter.mp3",category:"things",title:{en:"Typewriter",zh:"打字机"},description:{en:"Classic typewriter sounds for productive atmosphere",zh:"经典打字机声，高效工作氛围"},tags:["typewriter","classic","productive"],duration:3600,scientificRating:8.3,sleepEffectiveness:7.8,focusEffectiveness:8.9},{id:"things_dryer",filename:"dryer.mp3",category:"things",title:{en:"Dryer",zh:"烘干机"},description:{en:"Steady dryer sounds for household comfort",zh:"稳定的烘干机声，家庭舒适感"},tags:["dryer","steady","household"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8.1},{id:"things_paper",filename:"paper.mp3",category:"things",title:{en:"Paper",zh:"纸张"},description:{en:"Gentle paper rustling for reading atmosphere",zh:"轻柔的纸张沙沙声，阅读氛围"},tags:["paper","rustling","reading"],duration:3600,scientificRating:8,sleepEffectiveness:8.1,focusEffectiveness:8.5},{id:"things_singing_bowl",filename:"singing-bowl.mp3",category:"things",title:{en:"Singing Bowl",zh:"颂钵"},description:{en:"Resonant singing bowl for meditation and healing",zh:"共鸣颂钵声，冥想与疗愈"},tags:["singing-bowl","meditation","healing"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.6},{id:"things_keyboard",filename:"keyboard.mp3",category:"things",title:{en:"Keyboard",zh:"键盘"},description:{en:"Gentle keyboard typing for work ambience",zh:"轻柔的键盘敲击声，工作环境音"},tags:["keyboard","typing","work"],duration:3600,scientificRating:8.1,sleepEffectiveness:7.7,focusEffectiveness:8.6},{id:"things_slide_projector",filename:"slide-projector.mp3",category:"things",title:{en:"Slide Projector",zh:"幻灯机"},description:{en:"Nostalgic slide projector for vintage atmosphere",zh:"怀旧幻灯机声，复古氛围"},tags:["projector","nostalgic","vintage"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.9,focusEffectiveness:8.2}]}};