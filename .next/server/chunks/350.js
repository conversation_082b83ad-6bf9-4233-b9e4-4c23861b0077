exports.id=350,exports.ids=[350],exports.modules={1186:(e,t,i)=>{var s={"./en.json":[2247,247],"./zh.json":[2461,461]};function a(e){if(!i.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],a=t[0];return i.e(t[1]).then(()=>i.t(a,19))}a.keys=()=>Object.keys(s),a.id=1186,e.exports=a},1479:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,2994,23)),Promise.resolve().then(i.t.bind(i,6114,23)),Promise.resolve().then(i.t.bind(i,9727,23)),Promise.resolve().then(i.t.bind(i,9671,23)),Promise.resolve().then(i.t.bind(i,1868,23)),Promise.resolve().then(i.t.bind(i,4759,23))},996:()=>{},3541:(e,t,i)=>{Promise.resolve().then(i.bind(i,3170)),Promise.resolve().then(i.bind(i,4609)),Promise.resolve().then(i.bind(i,632)),Promise.resolve().then(i.bind(i,8801)),Promise.resolve().then(i.bind(i,6709)),Promise.resolve().then(i.bind(i,7068)),Promise.resolve().then(i.bind(i,7828)),Promise.resolve().then(i.bind(i,465)),Promise.resolve().then(i.bind(i,639))},4895:(e,t,i)=>{Promise.resolve().then(i.bind(i,2344))},2544:(e,t,i)=>{Promise.resolve().then(i.bind(i,3846))},2344:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o});var s=i(326),a=i(1308),r=i(434),n=i(5047);function o(){let e=(0,n.usePathname)().startsWith("/zh")?"zh":"en",t=(0,a.Ic)(e,"404");return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-md w-full mx-auto text-center px-4",children:[s.jsx("div",{className:"w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600",children:s.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"})})}),s.jsx("h1",{className:"text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:"404"}),s.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4",children:t.title}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:t.message}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(r.default,{href:"zh"===e?"/zh":"/",className:"inline-block w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors",children:t.action}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[s.jsx(r.default,{href:"zh"===e?"/zh/sounds":"/sounds",className:"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"zh"===e?"浏览音频":"Browse Sounds"}),s.jsx(r.default,{href:"zh"===e?"/zh/mixing":"/mixing",className:"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"zh"===e?"混音器":"Mixer"})]})]}),(0,s.jsxs)("div",{className:"mt-8 text-sm text-gray-500 dark:text-gray-400",children:[s.jsx("p",{children:"zh"===e?"您可能在寻找：":"You might be looking for:"}),(0,s.jsxs)("div",{className:"mt-2 space-x-4",children:[s.jsx(r.default,{href:"zh"===e?"/zh/sounds/rain":"/sounds/rain",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"雨声":"Rain Sounds"}),s.jsx(r.default,{href:"zh"===e?"/zh/sounds/nature":"/sounds/nature",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"自然音":"Nature Sounds"}),s.jsx(r.default,{href:"zh"===e?"/zh/sounds/noise":"/sounds/noise",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"白噪音":"White Noise"})]})]}),(0,s.jsxs)("div",{className:"mt-8 pt-4 border-t border-gray-200 dark:border-gray-700",children:[s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-2",children:"zh"===e?"切换语言：":"Switch language:"}),(0,s.jsxs)("div",{className:"flex justify-center space-x-4",children:["en"!==e&&s.jsx(r.default,{href:"/404",className:"text-indigo-500 hover:text-indigo-600 text-sm",children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),"zh"!==e&&s.jsx(r.default,{href:"/zh/404",className:"text-indigo-500 hover:text-indigo-600 text-sm",children:"\uD83C\uDDE8\uD83C\uDDF3 中文"})]})]})]})})}},3846:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o});var s=i(326),a=i(1308),r=i(434),n=i(5047);function o(){let e=(0,n.usePathname)().startsWith("/zh")?"zh":"en",t=(0,a.Ic)(e,"404");return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-md w-full mx-auto text-center px-4",children:[s.jsx("div",{className:"w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600",children:s.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"})})}),s.jsx("h1",{className:"text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:"404"}),s.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4",children:t.title}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:t.message}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(r.default,{href:"zh"===e?"/zh":"/",className:"inline-block w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors",children:t.action}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[s.jsx(r.default,{href:"zh"===e?"/zh/sounds":"/sounds",className:"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"zh"===e?"浏览音频":"Browse Sounds"}),s.jsx(r.default,{href:"zh"===e?"/":"/zh",className:"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"zh"===e?"English":"中文版"})]})]}),(0,s.jsxs)("div",{className:"mt-8 text-sm text-gray-500 dark:text-gray-400",children:[s.jsx("p",{children:"zh"===e?"您可能在寻找：":"You might be looking for:"}),(0,s.jsxs)("div",{className:"mt-2 space-x-4",children:[s.jsx(r.default,{href:"zh"===e?"/zh/sounds/rain":"/sounds/rain",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"雨声":"Rain Sounds"}),s.jsx(r.default,{href:"zh"===e?"/zh/sounds/nature":"/sounds/nature",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"自然音":"Nature Sounds"}),s.jsx(r.default,{href:"zh"===e?"/zh/mixing":"/mixing",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"混音器":"Audio Mixer"})]})]})]})})}},4609:(e,t,i)=>{"use strict";i.d(t,{AnalyticsProvider:()=>u});var s=i(326),a=i(6931);i(7577);var r=i(5047);let n="G-FKSNVZQTMD";function o(){return((0,r.usePathname)(),(0,r.useSearchParams)(),n)?(0,s.jsxs)(s.Fragment,{children:[s.jsx(a.default,{strategy:"afterInteractive",src:`https://www.googletagmanager.com/gtag/js?id=${n}`}),s.jsx(a.default,{id:"google-analytics",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${n}', {
              page_path: window.location.pathname,
              anonymize_ip: true,
              allow_google_signals: false,
              allow_ad_personalization_signals: false
            });
          `}})]}):null}let l="se2huma822";function c(){return l?s.jsx(a.default,{id:"microsoft-clarity",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:`
          (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
          })(window, document, "clarity", "script", "${l}");
        `}}):null}function d(){return null}function u({children:e}){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(o,{}),s.jsx(c,{}),s.jsx(d,{}),e]})}i(277)},632:(e,t,i)=>{"use strict";i.d(t,{AudioPlayer:()=>u});var s=i(326),a=i(3844),r=i(1135),n=i(7768),o=i(184),l=i(6709),c=i(465),d=i(7068);function u({sound:e,variant:t="full",showProgress:i=!0,showVolume:u=!0,showInfo:m=!0,className:g}){let h=(0,a.useTranslations)("common"),{currentSound:f,favorites:x,addToFavorites:p,removeFromFavorites:y}=(0,o.U)(),{play:v,pause:b,stop:w,setVolume:j,setLoop:k,seek:N,isPlaying:z,isPaused:E,isLoading:P,currentTime:C,duration:D,volume:S,isLooping:_,error:R}=(0,n.x)(),M=e||f,L=!!M&&x.includes(M.id),B=()=>{e&&e.id!==f?.id?v(e):v()},T=()=>{b()},A=(e,t="en")=>e.title[t]||e.title.en;return M?"compact"===t?(0,s.jsxs)("div",{className:(0,r.W)("flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",g),children:[s.jsx(l.PlayButton,{isPlaying:z&&f?.id===M.id,isLoading:P,onPlay:B,onPause:T,size:"sm"}),m&&(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:A(M)}),s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:M.category})]}),u&&s.jsx("div",{className:"w-20",children:s.jsx(c.VolumeControl,{volume:S,onVolumeChange:j,size:"sm",showIcon:!1})})]}):"mini"===t?(0,s.jsxs)("div",{className:(0,r.W)("flex items-center gap-2",g),children:[s.jsx(l.PlayButton,{isPlaying:z&&f?.id===M.id,isLoading:P,onPlay:B,onPause:T,size:"sm",variant:"ghost"}),m&&s.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400 truncate",children:A(M)})]}):(0,s.jsxs)("div",{className:(0,r.W)("bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden",g),children:[R&&s.jsx("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800",children:s.jsx("p",{className:"text-sm text-red-600 dark:text-red-400",children:R})}),m&&s.jsx("div",{className:"p-6 pb-4",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1",children:A(M)}),s.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:((e,t="en")=>e.description?.[t]||e.description?.en||"")(M)}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500 dark:text-gray-500",children:[s.jsx("span",{className:"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full",children:M.category}),M.tags?.map(e=>s.jsx("span",{className:"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full",children:e},e))]})]}),s.jsx("button",{onClick:()=>{M&&(L?y(M.id):p(M.id))},className:(0,r.W)("p-2 rounded-full transition-colors","hover:bg-gray-100 dark:hover:bg-gray-700","focus:outline-none focus:ring-2 focus:ring-amber-500",L?"text-red-500":"text-gray-400"),"aria-label":h(L?"removeFromFavorites":"addToFavorites"),children:s.jsx("svg",{className:"w-5 h-5",fill:L?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})})]})}),i&&s.jsx("div",{className:"px-6 pb-4",children:s.jsx(d.ProgressBar,{currentTime:C,duration:D,onSeek:N,isLoading:P,showTime:!0})}),s.jsx("div",{className:"px-6 pb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("button",{onClick:()=>{k(!_)},className:(0,r.W)("p-2 rounded-full transition-colors","hover:bg-gray-100 dark:hover:bg-gray-700","focus:outline-none focus:ring-2 focus:ring-amber-500",_?"text-amber-500":"text-gray-400"),"aria-label":h(_?"disableLoop":"enableLoop"),title:h(_?"disableLoop":"enableLoop"),children:s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M7 7h10v3l4-4-4-4v3H5v6h2V7zm10 10H7v-3l-4 4 4 4v-3h12v-6h-2v4z"})})})}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx("button",{onClick:()=>{w()},disabled:!z&&!E,className:(0,r.W)("p-2 rounded-full transition-colors","hover:bg-gray-100 dark:hover:bg-gray-700","focus:outline-none focus:ring-2 focus:ring-amber-500","disabled:opacity-50 disabled:cursor-not-allowed","text-gray-600 dark:text-gray-400"),"aria-label":h("stop"),title:h("stop"),children:s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M6 6h12v12H6z"})})}),s.jsx(l.PlayButton,{isPlaying:z&&f?.id===M.id,isLoading:P,onPlay:B,onPause:T,size:"lg"})]}),u&&s.jsx("div",{className:"w-32",children:s.jsx(c.VolumeControl,{volume:S,onVolumeChange:j,size:"md"})})]})})]}):s.jsx("div",{className:(0,r.W)("flex items-center justify-center p-8 text-gray-500 dark:text-gray-400","bg-gray-50 dark:bg-gray-900 rounded-lg border-2 border-dashed border-gray-200 dark:border-gray-700",g),children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600",children:s.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"})})}),s.jsx("p",{className:"text-sm",children:h("noAudioSelected")})]})})}},8801:(e,t,i)=>{"use strict";i.d(t,{AudioPlayerProvider:()=>j});var s=i(326),a=i(7577),r=i(184),n=i(7828),o=i(3844),l=i(7428),c=i(6970);function d({className:e="",variant:t="full",showDisplay:i=!0}){let r=(0,o.useTranslations)("timer"),[n,d]=(0,a.useState)(""),[u,m]=(0,a.useState)(!1),{isActive:g,startTimer:h,stopTimer:f,setPresetTimer:x,setCustomTimer:p}=(0,l.M)(),y=e=>{x(e),m(!1)};return(0,s.jsxs)("div",{className:`sleep-timer ${e}`,children:[i&&g&&s.jsx("div",{className:"mb-6",children:s.jsx(c.S,{variant:t})}),s.jsx("div",{className:"space-y-4",children:g?(0,s.jsxs)("div",{className:"text-center space-y-4",children:[s.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("timerActive")}),(0,s.jsxs)("button",{onClick:()=>{f(),m(!1)},className:"inline-flex items-center space-x-2 px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors",children:[(0,s.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 12a9 9 0 11-18 0 9 9 0 0118 0z"}),s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 10h6v4H9z"})]}),s.jsx("span",{children:r("stopTimer")})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:r("presetDurations")}),s.jsx("div",{className:"grid grid-cols-2 gap-3",children:[15,30,60,120].map(e=>(0,s.jsxs)("button",{onClick:()=>y(e),className:"flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-amber-300 dark:hover:border-amber-600 hover:bg-amber-50 dark:hover:bg-amber-900/10 transition-colors",children:[s.jsx("div",{className:"text-2xl font-bold text-amber-600 dark:text-amber-400",children:e}),s.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:60===e?r("hour"):r("minutes")})]},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("button",{onClick:()=>m(!u),className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),s.jsx("span",{children:r("customDuration")})]}),u&&(0,s.jsxs)("div",{className:"mt-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("input",{type:"number",min:"1",max:"480",value:n,onChange:e=>d(e.target.value),placeholder:"30",className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-amber-500 focus:border-transparent"}),s.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:r("minutes")}),s.jsx("button",{onClick:()=>{let e=parseInt(n);e>0&&e<=480&&(p(e),d(""),m(!1))},disabled:!n||0>=parseInt(n),className:"px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:r("start")})]}),s.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:r("customDurationHint")})]})]})]})})]})}function u({className:e=""}){let t=(0,o.useTranslations)("timer"),{playerUI:i,setTimerPanelVisible:a}=(0,r.U)();return i.showTimerPanel?(0,s.jsxs)("div",{className:`timer-panel ${e}`,children:[s.jsx("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm z-40",onClick:()=>a(!1)}),(0,s.jsxs)("div",{className:"fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 rounded-t-2xl shadow-2xl transform transition-transform duration-300 ease-out",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center",children:s.jsx("svg",{className:"w-5 h-5 text-amber-600 dark:text-amber-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t("sleepTimer")}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:t("sleepTimerDescription")})]})]}),s.jsx("button",{onClick:()=>a(!1),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),s.jsx("div",{className:"p-6 max-h-96 overflow-y-auto",children:s.jsx(d,{variant:"full"})}),s.jsx("div",{className:"px-6 pb-6",children:s.jsx("div",{className:"bg-amber-50 dark:bg-amber-900/10 border border-amber-200 dark:border-amber-800 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("svg",{className:"w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsxs)("div",{className:"text-sm",children:[s.jsx("p",{className:"text-amber-800 dark:text-amber-200 font-medium mb-1",children:t("timerTips")}),(0,s.jsxs)("ul",{className:"text-amber-700 dark:text-amber-300 space-y-1",children:[(0,s.jsxs)("li",{children:["• ",t("tip1")]}),(0,s.jsxs)("li",{children:["• ",t("tip2")]}),(0,s.jsxs)("li",{children:["• ",t("tip3")]})]})]})]})})}),s.jsx("div",{className:"absolute top-2 left-1/2 transform -translate-x-1/2",children:s.jsx("div",{className:"w-8 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"})})]})]}):null}var m=i(6462),g=i(2350),h=i(3519),f=i(7290),x=i(7768);function p({className:e="",onTogglePlay:t}){let i=(0,o.useTranslations)("sleepMode"),{isPlaying:r,play:n,pause:l}=(0,x.x)(),[c,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),p=(0,a.useRef)(null),y=(0,h.c)(0),v=(0,f.H)(y,[0,100],[0,100]),b=(0,f.H)(y,[0,50],[.3,1]),w=(0,f.H)(y,[0,100],[20,120]),j=(0,f.H)(y,[0,100],[0,15]);return s.jsx("div",{className:`pull-string-controller ${e}`,children:(0,s.jsxs)("div",{ref:p,className:"relative w-full h-64 flex flex-col items-center justify-start pt-8",children:[s.jsx("div",{className:"w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full mb-2"}),s.jsx(g.E.div,{className:"w-0.5 bg-gray-400 dark:bg-gray-500 origin-top",style:{height:w,opacity:b}}),(0,s.jsxs)(g.E.div,{drag:"y",dragConstraints:{top:0,bottom:100},dragElastic:.1,dragMomentum:!1,whileDrag:{scale:1.1},onDragStart:()=>{d(!0),m(!1)},onDrag:(e,i)=>{y.get()>=60&&!u&&(m(!0),r?l():n(),"vibrate"in navigator&&navigator.vibrate(50),t?.())},onDragEnd:()=>{d(!1),m(!1),y.set(0)},style:{y,rotate:j},className:"pull-string draggable relative cursor-grab active:cursor-grabbing","data-drag":"true","data-testid":"pull-string",whileHover:{scale:1.05},whileTap:{scale:.95},children:[s.jsx("div",{className:`
            w-16 h-16 rounded-full border-4 border-gray-300 dark:border-gray-600
            bg-white dark:bg-gray-800 shadow-lg
            flex items-center justify-center
            transition-colors duration-200
            ${c?"border-amber-400 dark:border-amber-500":""}
            ${u?"border-green-400 dark:border-green-500":""}
          `,children:s.jsx("div",{className:`
              w-8 h-8 rounded-full
              flex items-center justify-center
              transition-colors duration-200
              ${r?"bg-amber-100 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"}
            `,children:r?s.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M6 4h4v16H6V4zm8 0h4v16h-4V4z"})}):s.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M8 5v14l11-7z"})})})}),c&&s.jsx(g.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"absolute top-20 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:s.jsx("div",{className:"px-3 py-1 bg-black/70 text-white text-xs rounded-full",children:i(v.get()>=60?"releaseToToggle":"pullToToggle")})})]}),(0,s.jsxs)("div",{className:"mt-8 text-center",children:[s.jsx("div",{className:`
            text-sm font-medium transition-colors duration-200
            ${r?"text-amber-600 dark:text-amber-400":"text-gray-500 dark:text-gray-400"}
          `,children:i(r?"playing":"paused")}),s.jsx("div",{className:"text-xs text-gray-400 dark:text-gray-500 mt-2",children:i("pullStringHint")})]})]})})}function y({className:e="",showTimer:t=!0}){let a=(0,o.useTranslations)("sleepMode"),{currentSound:n}=(0,r.U)(),{currentTime:l,duration:d,volume:u,isLooping:m}=(0,x.x)();if(!n)return s.jsx("div",{className:`audio-info-display ${e}`,children:(0,s.jsxs)("div",{className:"text-center text-gray-500 dark:text-gray-400",children:[s.jsx("div",{className:"text-lg font-medium mb-2",children:a("noAudioSelected")}),s.jsx("div",{className:"text-sm",children:a("selectAudioToStart")})]})});let h=e=>`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`,f=d>0?l/d*100:0;return s.jsx(g.E.div,{className:`audio-info-display ${e}`,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsxs)("div",{className:"text-center space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-2xl font-light text-gray-900 dark:text-white mb-2",children:n.title.zh||n.title.en}),n.description&&s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto",children:n.description.zh||n.description.en})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[s.jsx("div",{className:"w-full max-w-sm mx-auto",children:s.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1",children:s.jsx(g.E.div,{className:"bg-amber-400 dark:bg-amber-500 h-1 rounded-full",initial:{width:0},animate:{width:`${f}%`},transition:{duration:.3}})})}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 dark:text-gray-400 max-w-sm mx-auto",children:[s.jsx("span",{children:h(l)}),s.jsx("span",{children:h(d)})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 max-w-xs mx-auto text-sm",children:[(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-gray-500 dark:text-gray-400 mb-1",children:a("volume")}),(0,s.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:[Math.round(100*u),"%"]})]}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-gray-500 dark:text-gray-400 mb-1",children:a("loop")}),s.jsx("div",{className:`font-medium ${m?"text-amber-600 dark:text-amber-400":"text-gray-900 dark:text-white"}`,children:a(m?"on":"off")})]})]}),t&&(0,s.jsxs)("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:[s.jsx(c.S,{variant:"full",showProgress:!0}),s.jsx("div",{className:"mt-3 flex justify-center",children:(0,s.jsxs)("button",{onClick:()=>{let{setTimerPanelVisible:e}=i(184).U.getState();e(!0)},className:"inline-flex items-center space-x-2 px-4 py-2 bg-amber-100 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 rounded-lg hover:bg-amber-200 dark:hover:bg-amber-900/30 transition-colors text-sm","aria-label":"设置睡眠定时器",children:[s.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),s.jsx("span",{children:"设置定时器"})]})})]}),n.category&&s.jsx("div",{className:"pt-2",children:s.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300",children:a(`categories.${n.category}`)})})]})})}function v({className:e=""}){let t=(0,o.useTranslations)("sleepMode"),[i,r]=(0,a.useState)(new Date),[n,l]=(0,a.useState)("");return s.jsx(g.E.div,{className:`ambient-info ${e}`,initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:.5},children:(0,s.jsxs)("div",{className:"space-y-6 text-center",children:[s.jsx("div",{children:s.jsx("h3",{className:"text-lg font-light text-gray-700 dark:text-gray-300",children:(()=>{let e=i.getHours();return e>=5&&e<12?t("greetings.morning"):e>=12&&e<17?t("greetings.afternoon"):e>=17&&e<22?t("greetings.evening"):t("greetings.night")})()})}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("div",{className:"text-3xl font-light text-gray-900 dark:text-white tabular-nums",children:i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",hour12:!1})}),s.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:i.toLocaleDateString([],{weekday:"long",month:"long",day:"numeric"})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 max-w-xs mx-auto text-sm",children:[(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-gray-500 dark:text-gray-400 mb-1",children:t("moonPhase")}),s.jsx("div",{className:"text-gray-900 dark:text-white",children:[t("moonPhases.newMoon")||"New Moon",t("moonPhases.waxingCrescent")||"Waxing Crescent",t("moonPhases.firstQuarter")||"First Quarter",t("moonPhases.waxingGibbous")||"Waxing Gibbous",t("moonPhases.fullMoon")||"Full Moon",t("moonPhases.waningGibbous")||"Waning Gibbous",t("moonPhases.lastQuarter")||"Last Quarter",t("moonPhases.waningCrescent")||"Waning Crescent"][Math.floor(i.getDate()/30*8)%8]})]}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-gray-500 dark:text-gray-400 mb-1",children:t("sleepQuality")}),s.jsx("div",{className:"text-green-600 dark:text-green-400",children:t("optimal")})]})]}),s.jsx("div",{className:"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 max-w-md mx-auto",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 mt-1",children:s.jsx("svg",{className:"w-5 h-5 text-amber-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),(0,s.jsxs)("div",{className:"text-left",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:t("sleepTip")}),s.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:n})]})]})}),s.jsx("div",{className:"text-xs text-gray-400 dark:text-gray-500 max-w-sm mx-auto",children:t("relaxationHint")})]})})}function b({className:e=""}){let t=(0,o.useTranslations)("sleepMode"),{setPlayerMode:i}=(0,r.U)(),[n,l]=(0,a.useState)(!0),[c,d]=(0,a.useState)(Date.now());return s.jsx("div",{className:`sleep-mode-player ${e}`,children:(0,s.jsxs)("div",{className:"fixed inset-0 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900",children:[(0,s.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[s.jsx("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-amber-400 rounded-full blur-3xl"}),s.jsx("div",{className:"absolute bottom-1/4 right-1/4 w-48 h-48 bg-blue-400 rounded-full blur-3xl"})]}),(0,s.jsxs)("div",{className:"relative z-10 h-full flex flex-col",children:[s.jsx(m.M,{children:n&&s.jsx(g.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"absolute top-0 left-0 right-0 z-20 p-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[s.jsx("div",{className:"text-white/70 text-sm",children:t("sleepMode")}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("button",{onClick:()=>{let{setTimerPanelVisible:e}=r.U.getState();e(!0)},className:"p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors","aria-label":"睡眠定时器",title:"睡眠定时器","data-testid":"sleep-timer-button",children:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsx("button",{onClick:()=>{let{setMixingPanelVisible:e}=r.U.getState();e(!0)},className:"p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors","aria-label":"混音控制",title:"混音控制","data-testid":"sleep-mixing-button",children:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"})})}),s.jsx("button",{onClick:()=>{i("standard")},className:"p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors","aria-label":t("exitSleepMode"),children:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})})}),s.jsx("div",{className:"flex-1 flex items-center justify-center p-8",children:s.jsx("div",{className:"w-full max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 items-center",children:[s.jsx("div",{className:"order-2 lg:order-1",children:s.jsx(v,{})}),s.jsx("div",{className:"order-1 lg:order-2 flex justify-center",children:s.jsx(p,{})}),s.jsx("div",{className:"order-3",children:s.jsx(y,{showTimer:!0})})]})})}),s.jsx(m.M,{children:n&&s.jsx(g.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},className:"absolute bottom-0 left-0 right-0 z-20 p-4",children:(0,s.jsxs)("div",{className:"text-center text-white/50 text-sm space-y-2",children:[s.jsx("div",{children:t("pullStringToControl")}),(0,s.jsxs)("div",{className:"text-xs",children:[t("pressEscapeToExit")," • ",t("uiAutoHides")]})]})})})]})]})})}var w=i(3436);function j({children:e,className:t}){let{currentSound:i,playerUI:a,setPlayerVisible:o}=(0,r.U)();return(0,s.jsxs)("div",{className:t,children:[e,"standard"===a.mode&&s.jsx(n.StandardPlayer,{position:a.position,showMixingButton:!0,showSleepModeButton:!0,autoHide:!1}),"sleep"===a.mode&&s.jsx(b,{}),s.jsx(u,{}),s.jsx(w.MixingPanel,{})]})}},6709:(e,t,i)=>{"use strict";i.d(t,{GhostPlayButton:()=>c,LargePlayButton:()=>d,PlayButton:()=>n,PrimaryPlayButton:()=>o,SecondaryPlayButton:()=>l,SmallPlayButton:()=>u});var s=i(326),a=i(3844),r=i(1135);function n({isPlaying:e,isLoading:t,onPlay:i,onPause:n,size:o="md",variant:l="primary",disabled:c=!1,className:d}){let u=(0,a.useTranslations)("common"),m={sm:"w-3 h-3",md:"w-4 h-4",lg:"w-6 h-6"};return(0,s.jsxs)("button",{onClick:s=>{console.log("\uD83C\uDFB5 PlayButton handleClick 被调用:",{isPlaying:e,disabled:c,isLoading:t}),s.stopPropagation(),c||t||(e?(console.log("⏸️ 调用 onPause"),n()):(console.log("▶️ 调用 onPlay"),i()))},disabled:c||t,className:(0,r.W)("relative rounded-full transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","transform hover:scale-105 active:scale-95",{sm:"w-8 h-8 p-1.5",md:"w-12 h-12 p-3",lg:"w-16 h-16 p-4"}[o],{primary:"bg-amber-500 hover:bg-amber-600 text-white shadow-lg hover:shadow-xl",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200",ghost:"bg-transparent hover:bg-gray-100 text-gray-600 dark:hover:bg-gray-800 dark:text-gray-400"}[l],d),"aria-label":u(e?"pause":"play"),title:u(e?"pause":"play"),children:[t&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx("div",{className:(0,r.W)("animate-spin rounded-full border-2 border-current border-t-transparent",m[o])})}),!t&&s.jsx("div",{className:"flex items-center justify-center",children:e?s.jsx("svg",{className:m[o],fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:s.jsx("path",{d:"M6 4h4v16H6V4zm8 0h4v16h-4V4z"})}):s.jsx("svg",{className:(0,r.W)(m[o],"ml-0.5"),fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:s.jsx("path",{d:"M8 5v14l11-7z"})})}),e&&!t&&s.jsx("div",{className:"absolute inset-0 rounded-full animate-ping bg-current opacity-20"})]})}function o(e){return s.jsx(n,{...e,variant:"primary"})}function l(e){return s.jsx(n,{...e,variant:"secondary"})}function c(e){return s.jsx(n,{...e,variant:"ghost"})}function d(e){return s.jsx(n,{...e,size:"lg"})}function u(e){return s.jsx(n,{...e,size:"sm"})}},7068:(e,t,i)=>{"use strict";i.d(t,{ProgressBar:()=>n});var s=i(326),a=i(7577),r=i(1135);function n({currentTime:e,duration:t,onSeek:i,isLoading:n=!1,showTime:o=!0,size:l="md",className:c,disabled:d=!1}){let[u,m]=(0,a.useState)(!1),[g,h]=(0,a.useState)(0),[f,x]=(0,a.useState)(!1),[p,y]=(0,a.useState)(0),v=(0,a.useRef)(null),b=e=>!isFinite(e)||e<0?"0:00":`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`,w=t>0?e/t*100:0,j=u?g:e,k=u?g/t*100:w,N=e=>{if(!v.current||0===t)return;let i=v.current.getBoundingClientRect(),s=Math.max(0,Math.min(1,(e.clientX-i.left)/i.width))*t;h(s),y(s)},z={sm:"h-1",md:"h-2",lg:"h-3"};return(0,s.jsxs)("div",{className:(0,r.W)("w-full",c),children:[o&&(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2 text-sm text-gray-600 dark:text-gray-400",children:[s.jsx("span",{className:"font-mono",children:b(j)}),s.jsx("span",{className:"font-mono",children:b(t)})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{ref:v,className:(0,r.W)("relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer group","focus:outline-none focus:ring-2 focus:ring-amber-500",z[l],d&&"opacity-50 cursor-not-allowed"),onPointerDown:e=>{d||0===t||(m(!0),N(e),e.preventDefault())},onClick:e=>{if(d||0===t||u)return;let s=v.current?.getBoundingClientRect();s&&i(Math.max(0,Math.min(1,(e.clientX-s.left)/s.width))*t)},onMouseMove:e=>{if(!v.current||0===t)return;let i=v.current.getBoundingClientRect();y(Math.max(0,Math.min(1,(e.clientX-i.left)/i.width))*t),x(!0)},onMouseLeave:()=>{u||x(!1)},onKeyDown:s=>{if(d||0===t)return;let a=e,r=.05*t;switch(s.key){case"ArrowLeft":a=Math.max(0,e-r);break;case"ArrowRight":a=Math.min(t,e+r);break;case"Home":a=0;break;case"End":a=t;break;default:return}s.preventDefault(),i(a)},tabIndex:d?-1:0,role:"slider","aria-valuemin":0,"aria-valuemax":t,"aria-valuenow":e,"aria-label":"音频进度",children:[n&&s.jsx("div",{className:"absolute inset-0 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse"}),s.jsx("div",{className:(0,r.W)("absolute left-0 top-0 bg-amber-500 rounded-full transition-all duration-150",z[l]),style:{width:`${Math.max(0,Math.min(100,k))}%`}}),t>0&&s.jsx("div",{className:(0,r.W)("absolute top-1/2 bg-white border-2 border-amber-500 rounded-full shadow-md","transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150","opacity-0 group-hover:opacity-100",u&&"opacity-100 scale-125",{sm:"w-3 h-3",md:"w-4 h-4",lg:"w-5 h-5"}[l]),style:{left:`${Math.max(0,Math.min(100,k))}%`}})]}),f&&t>0&&s.jsx("div",{className:"absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg transform -translate-x-1/2 -top-8",style:{left:`${Math.max(0,Math.min(100,p/t*100))}%`},children:b(p)})]}),t>0&&(0,s.jsxs)("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-500 text-center",children:[Math.round(k),"%"]})]})}},7828:(e,t,i)=>{"use strict";i.d(t,{StandardPlayer:()=>b});var s=i(326);i(7577);var a=i(3844),r=i(1135),n=i(6462),o=i(2350),l=i(184),c=i(7768),d=i(6709),u=i(465),m=i(7068),g=i(6970),h=i(2505),f=i(6421),x=i(2),p=i(4867),y=i(2791),v=i(2591);function b({className:e,position:t="bottom",showMixingButton:i=!0,showSleepModeButton:b=!0,autoHide:w=!1,autoHideDelay:j=5e3}){let k=(0,a.useTranslations)("audioPlayer");(0,a.useTranslations)("common");let N=(0,a.useLocale)(),{currentSound:z,playerUI:E,favorites:P,setPlayerVisible:C,setPlayerMode:D,togglePlayerMinimized:S,setTimerPanelVisible:_,setMixingPanelVisible:R,addToFavorites:M,removeFromFavorites:L}=(0,l.U)(),{play:B,pause:T,stop:A,setVolume:W,seek:U,isPlaying:V,isPaused:I,isLoading:$,currentTime:F,duration:H,volume:G,error:O}=(0,c.x)();return(z&&P.includes(z.id),console.log("\uD83C\uDFB5 StandardPlayer 渲染检查:",{isVisible:E.isVisible,currentSound:z?.title,shouldRender:E.isVisible&&z}),E.isVisible&&z)?(console.log("✅ StandardPlayer 正在渲染"),s.jsx(n.M,{children:s.jsx(o.E.div,{initial:"hidden",animate:"visible",exit:"exit",variants:{hidden:{y:"bottom"===t?100:"top"===t?-100:20,opacity:0},visible:{y:0,opacity:1,transition:{type:"spring",stiffness:300,damping:30}},exit:{y:"bottom"===t?100:"top"===t?-100:20,opacity:0,transition:{duration:.2}}},className:(0,r.W)({bottom:"fixed bottom-0 left-0 right-0 z-50",top:"fixed top-0 left-0 right-0 z-50",floating:"fixed bottom-4 left-2 right-2 sm:left-4 sm:right-4 z-50 max-w-4xl mx-auto"}[t],"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md","border-t border-gray-200 dark:border-gray-700","floating"===t&&"rounded-lg border shadow-xl",e),"data-testid":"standard-player",children:s.jsx(o.E.div,{animate:E.isMinimized?"minimized":"normal",variants:{normal:{height:"auto"},minimized:{height:60,transition:{duration:.3}}},className:"overflow-hidden",children:s.jsx("div",{className:"px-3 sm:px-4 py-3",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 sm:gap-4",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2",children:[s.jsx(d.PlayButton,{isPlaying:V,isLoading:$,onPlay:()=>{z&&B()},onPause:()=>{T()},size:"md",variant:"primary",disabled:!z}),s.jsx("button",{onClick:()=>{A(),C(!1)},disabled:!z,className:(0,r.W)("p-2 rounded-full transition-colors","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-amber-500","disabled:opacity-50 disabled:cursor-not-allowed","text-gray-600 dark:text-gray-400"),"aria-label":k("controls.stop"),title:k("controls.stop"),children:s.jsx(h.Z,{className:"w-5 h-5"})})]}),s.jsx("div",{className:"flex-1 min-w-0",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:"zh"===N?z.title.zh||z.title.en:z.title.en||z.title.zh}),s.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:"zh"===N?z.description?.zh||z.description?.en:z.description?.en||z.description?.zh})]}),s.jsx("div",{className:"ml-2",children:s.jsx(g.S,{variant:"compact",showProgress:!1})})]})})]}),!E.isMinimized&&s.jsx("div",{className:"mt-3",children:s.jsx(m.ProgressBar,{currentTime:F,duration:H,onSeek:e=>{U(e)},isLoading:$,showTime:!0,size:"md",disabled:!z})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2",children:[s.jsx("div",{className:"hidden sm:block",children:s.jsx(u.VolumeControl,{volume:G,onVolumeChange:e=>{W(e)},size:"sm",showIcon:!0,disabled:!z})}),!E.isMinimized&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("button",{onClick:()=>{_(!E.showTimerPanel)},className:(0,r.W)("hidden sm:block p-2 rounded-full transition-colors","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-amber-500",E.showTimerPanel?"text-amber-600 bg-amber-50 dark:bg-amber-900/20":"text-gray-600 dark:text-gray-400"),"aria-label":k("timer.setTimer"),title:k("timer.setTimer"),children:s.jsx(f.Z,{className:"w-5 h-5"})}),i&&s.jsx("button",{onClick:()=>{R(!E.showMixingPanel)},className:(0,r.W)("hidden sm:block p-2 rounded-full transition-colors","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-amber-500",E.showMixingPanel?"text-amber-600 bg-amber-50 dark:bg-amber-900/20":"text-gray-600 dark:text-gray-400"),"aria-label":k("mixing.title"),title:k("mixing.title"),children:s.jsx(x.Z,{className:"w-5 h-5"})}),b&&s.jsx("button",{onClick:()=>{D("sleep")},"data-testid":"sleep-mode-button",className:(0,r.W)("p-2 rounded-full transition-colors","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-amber-500","text-gray-600 dark:text-gray-400"),"aria-label":k("modes.switchToSleep"),title:k("modes.switchToSleep"),children:s.jsx(p.Z,{className:"w-5 h-5"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 ml-2 border-l border-gray-200 dark:border-gray-700 pl-2",children:[s.jsx("button",{onClick:()=>{S()},className:(0,r.W)("p-1.5 rounded-md transition-colors","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-amber-500","text-gray-500 dark:text-gray-400"),"aria-label":k(E.isMinimized?"controls.maximize":"controls.minimize"),title:k(E.isMinimized?"controls.maximize":"controls.minimize"),children:s.jsx(y.Z,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>{A(),C(!1)},className:(0,r.W)("p-1.5 rounded-md transition-colors","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-amber-500","text-gray-500 dark:text-gray-400"),"aria-label":k("controls.close"),title:k("controls.close"),children:s.jsx(v.Z,{className:"w-4 h-4"})})]})]})]})})})})})):(console.log("❌ StandardPlayer 不渲染 - isVisible:",E.isVisible,"currentSound:",!!z),null)}},465:(e,t,i)=>{"use strict";i.d(t,{VolumeControl:()=>o});var s=i(326),a=i(7577),r=i(3844),n=i(1135);function o({volume:e,onVolumeChange:t,orientation:i="horizontal",size:o="md",showIcon:l=!0,showValue:c=!1,className:d,disabled:u=!1}){let m=(0,r.useTranslations)("common"),[g,h]=(0,a.useState)(!1),[f,x]=(0,a.useState)(!1),p=(0,a.useRef)(null),[y,v]=(0,a.useState)(e),b=e=>{t(Math.max(0,Math.min(1,e)))},w=e=>{if(!p.current)return;let t=p.current.getBoundingClientRect();b("horizontal"===i?(e.clientX-t.left)/t.width:1-(e.clientY-t.top)/t.height)};return(0,s.jsxs)("div",{className:(0,n.W)("flex items-center gap-2","vertical"===i&&"flex-col",d),children:[l&&s.jsx("button",{onClick:()=>{u||(e>0?(v(e),b(0)):b(y>0?y:.7))},disabled:u,className:(0,n.W)("p-1 rounded-md transition-colors","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-amber-500","disabled:opacity-50 disabled:cursor-not-allowed",0===e?"text-red-500":"text-gray-600 dark:text-gray-400"),"aria-label":0===e?"取消静音":"静音",title:0===e?"取消静音":"静音",children:0===e?s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"})}):e<.3?s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M7 9v6h4l5 5V4l-5 5H7z"})}):e<.7?s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"})}):s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"})})}),(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsxs)("div",{ref:p,className:(0,n.W)("relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer","focus:outline-none focus:ring-2 focus:ring-amber-500",{sm:"horizontal"===i?"h-1":"w-1 h-16",md:"horizontal"===i?"h-2":"w-2 h-20",lg:"horizontal"===i?"h-3":"w-3 h-24"}[o],u&&"opacity-50 cursor-not-allowed"),onPointerDown:e=>{u||(h(!0),x(!0),w(e),e.preventDefault())},onKeyDown:t=>{if(u)return;let i=e;switch(t.key){case"ArrowUp":case"ArrowRight":i=Math.min(1,e+.1);break;case"ArrowDown":case"ArrowLeft":i=Math.max(0,e-.1);break;case"Home":i=1;break;case"End":i=0;break;default:return}t.preventDefault(),b(i)},tabIndex:u?-1:0,role:"slider","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":Math.round(100*e),"aria-label":m("volume"),onMouseEnter:()=>x(!0),onMouseLeave:()=>!g&&x(!1),children:[s.jsx("div",{className:"absolute bg-amber-500 rounded-full transition-all duration-150",style:{["horizontal"===i?"width":"height"]:`${100*e}%`,["horizontal"===i?"height":"width"]:"100%",["vertical"===i?"bottom":"left"]:0}}),s.jsx("div",{className:(0,n.W)("absolute bg-white border-2 border-amber-500 rounded-full shadow-md","transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150","hover:scale-110",g&&"scale-125",{sm:"w-3 h-3",md:"w-4 h-4",lg:"w-5 h-5"}[o]),style:{["horizontal"===i?"left":"bottom"]:`${100*e}%`,["horizontal"===i?"top":"left"]:"50%"}})]}),(f||c)&&(0,s.jsxs)("div",{className:(0,n.W)("absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg","transform -translate-x-1/2","horizontal"===i?"-top-8 left-1/2":"-right-12 top-1/2 -translate-y-1/2"),children:[Math.round(100*e),"%"]})]}),c&&(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 min-w-[3rem] text-right",children:[Math.round(100*e),"%"]})]})}},639:(e,t,i)=>{"use strict";i.d(t,{ErrorBoundary:()=>n});var s=i(326),a=i(7577),r=i(1308);class n extends a.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){(0,r.jG)(e,{componentStack:t.componentStack||"Unknown component stack"},this.props.locale||"en"),this.setState({error:e,errorInfo:t})}render(){if(this.state.hasError){if(this.props.fallback)return this.props.fallback;let e=this.props.locale||"en",t=(0,r.Ic)(e,"500");return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-md w-full mx-auto text-center px-4",children:[s.jsx("div",{className:"w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600",children:s.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),s.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:t.title}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:t.message}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("button",{onClick:this.handleRetry,className:"w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors",children:t.action}),s.jsx("button",{onClick:()=>window.location.href="zh"===e?"/zh":"/",className:"w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"zh"===e?"返回首页":"Go to Homepage"})]}),!1]})})}return this.props.children}}},7408:(e,t,i)=>{"use strict";i.d(t,{MixingChannel:()=>c});var s=i(326),a=i(7577),r=i(3844),n=i(1135),o=i(465),l=i(6709);function c({channel:e,audio:t,isPlaying:i,isLoading:c,onPlay:d,onPause:u,onStop:m,onVolumeChange:g,onMute:h,onRemove:f,className:x}){let p;let y=(0,r.useTranslations)("mixing"),v=(0,r.useLocale)(),[b,w]=(0,a.useState)(!1),j=(p=t.title)[v]||p.en||Object.values(p)[0]||"";return(0,s.jsxs)("div",{className:(0,n.W)("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700","shadow-sm hover:shadow-md transition-all duration-200",!e.isActive&&"opacity-60",x),children:[s.jsx("div",{className:"p-4 border-b border-gray-100 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center flex-shrink-0",children:s.jsx("span",{className:"text-lg",children:{rain:"\uD83C\uDF27️",nature:"\uD83C\uDF3F",noise:"\uD83D\uDD0A",animals:"\uD83D\uDC3E",things:"\uD83C\uDFE0",transport:"\uD83D\uDE97",urban:"\uD83C\uDFD9️",places:"\uD83D\uDCCD"}[t.category.toLowerCase()]||"\uD83C\uDFB5"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("h4",{className:"font-medium text-gray-900 dark:text-gray-100 truncate",children:j}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:t.category})]}),i&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[s.jsx("div",{className:"w-1 h-3 bg-green-500 rounded-full animate-pulse"}),s.jsx("div",{className:"w-1 h-4 bg-green-500 rounded-full animate-pulse",style:{animationDelay:"0.2s"}}),s.jsx("div",{className:"w-1 h-3 bg-green-500 rounded-full animate-pulse",style:{animationDelay:"0.4s"}})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[s.jsx("button",{onClick:()=>w(!b),className:"p-1 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors","aria-label":y(b?"hideDetails":"showDetails"),children:s.jsx("svg",{className:(0,n.W)("w-4 h-4 transition-transform",b&&"rotate-180"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})}),s.jsx("button",{onClick:f,className:"p-1 rounded-md text-gray-400 hover:text-red-500 transition-colors","aria-label":y("removeChannel"),children:s.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(l.SmallPlayButton,{isPlaying:i,isLoading:c,onPlay:d,onPause:u,variant:"secondary"}),s.jsx("button",{onClick:m,disabled:!i,className:(0,n.W)("p-1.5 rounded-md transition-colors","hover:bg-gray-100 dark:hover:bg-gray-700","disabled:opacity-50 disabled:cursor-not-allowed","text-gray-600 dark:text-gray-400"),"aria-label":y("stop"),children:s.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M6 6h12v12H6z"})})})]}),s.jsx("button",{onClick:h,className:(0,n.W)("p-1.5 rounded-md transition-colors","hover:bg-gray-100 dark:hover:bg-gray-700",e.isMuted?"text-red-500":"text-gray-600 dark:text-gray-400"),"aria-label":y(e.isMuted?"unmute":"mute"),children:e.isMuted?s.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"})}):s.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"})})})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:y("volume")}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(100*e.volume),"%"]})]}),s.jsx(o.VolumeControl,{volume:e.isMuted?0:e.volume,onVolumeChange:g,showIcon:!1,size:"sm",disabled:e.isMuted})]}),b&&s.jsx("div",{className:"pt-4 border-t border-gray-100 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"text-gray-500 dark:text-gray-400",children:[y("category"),":"]}),s.jsx("span",{className:"text-gray-900 dark:text-gray-100",children:t.category})]}),t.scientificRating&&(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"text-gray-500 dark:text-gray-400",children:[y("rating"),":"]}),(0,s.jsxs)("span",{className:"text-gray-900 dark:text-gray-100",children:["⭐ ",t.scientificRating.toFixed(1)]})]}),t.tags&&t.tags.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"text-gray-500 dark:text-gray-400 block mb-1",children:[y("tags"),":"]}),s.jsx("div",{className:"flex flex-wrap gap-1",children:t.tags.map(e=>s.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full",children:e},e))})]})]})})]})]})}},3436:(e,t,i)=>{"use strict";i.d(t,{MixingPanel:()=>h});var s=i(326),a=i(7577),r=i(3844),n=i(6462),o=i(2350),l=i(1135),c=i(184),d=i(7408),u=i(465),m=i(18);let g=[{id:"rain_heavy_rain",filename:"heavy-rain.mp3",category:"rain",title:{en:"Heavy Rain",zh:"大雨"},description:{en:"Intense rainfall for deep relaxation",zh:"强烈降雨，深度放松"},tags:["intense","powerful","relaxing"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:7.5},{id:"rain_light_rain",filename:"light-rain.mp3",category:"rain",title:{en:"Light Rain",zh:"轻雨"},description:{en:"Gentle light rain for peaceful relaxation",zh:"轻柔细雨，宁静放松"},tags:["gentle","soft","peaceful"],duration:3600,scientificRating:9.2,sleepEffectiveness:9.5,focusEffectiveness:8.8},{id:"rain_on_car_roof",filename:"rain-on-car-roof.mp3",category:"rain",title:{en:"Rain on Car Roof",zh:"雨打车顶"},description:{en:"Rain drumming on car roof for cozy atmosphere",zh:"雨滴敲打车顶，营造温馨氛围"},tags:["cozy","rhythmic","comforting"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8},{id:"rain_on_leaves",filename:"rain-on-leaves.mp3",category:"rain",title:{en:"Rain on Leaves",zh:"雨打树叶"},description:{en:"Rain falling on leaves for natural ambiance",zh:"雨滴落在树叶上，自然环境音"},tags:["natural","organic","soothing"],duration:3600,scientificRating:8.8,sleepEffectiveness:9.1,focusEffectiveness:8.3},{id:"rain_on_tent",filename:"rain-on-tent.mp3",category:"rain",title:{en:"Rain on Tent",zh:"雨打帐篷"},description:{en:"Rain pattering on tent fabric for camping atmosphere",zh:"雨滴敲打帐篷，露营氛围"},tags:["camping","adventure","cozy"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.6,focusEffectiveness:7.8},{id:"rain_on_umbrella",filename:"rain-on-umbrella.mp3",category:"rain",title:{en:"Rain on Umbrella",zh:"雨打雨伞"},description:{en:"Rain hitting umbrella for intimate sound experience",zh:"雨滴敲打雨伞，亲密的声音体验"},tags:["intimate","close","personal"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.5,focusEffectiveness:7.9},{id:"rain_on_window",filename:"rain-on-window.mp3",category:"rain",title:{en:"Rain on Window",zh:"雨打窗户"},description:{en:"Rain streaming down window glass for indoor comfort",zh:"雨水流淌在窗玻璃上，室内舒适感"},tags:["indoor","comfort","peaceful"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.1},{id:"rain_thunder",filename:"thunder.mp3",category:"rain",title:{en:"Thunder",zh:"雷声"},description:{en:"Thunder sounds for dramatic weather atmosphere",zh:"雷声，戏剧性天气氛围"},tags:["dramatic","powerful","weather"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.5,focusEffectiveness:6.8},{id:"nature_campfire",filename:"campfire.mp3",category:"nature",title:{en:"Campfire",zh:"篝火"},description:{en:"Crackling campfire for cozy outdoor atmosphere",zh:"噼啪作响的篝火，温馨户外氛围"},tags:["cozy","warm","outdoor"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.2},{id:"nature_droplets",filename:"droplets.mp3",category:"nature",title:{en:"Water Droplets",zh:"水滴"},description:{en:"Gentle water droplets for meditation",zh:"轻柔水滴声，适合冥想"},tags:["gentle","meditation","pure"],duration:3600,scientificRating:9,sleepEffectiveness:9.3,focusEffectiveness:8.7},{id:"nature_howling_wind",filename:"howling-wind.mp3",category:"nature",title:{en:"Howling Wind",zh:"呼啸风声"},description:{en:"Wind howling through landscapes",zh:"风声呼啸穿过大地"},tags:["wind","atmospheric","dramatic"],duration:3600,scientificRating:7.9,sleepEffectiveness:7.8,focusEffectiveness:7.2},{id:"nature_jungle",filename:"jungle.mp3",category:"nature",title:{en:"Jungle",zh:"丛林"},description:{en:"Tropical jungle ambiance with wildlife",zh:"热带丛林环境音，伴有野生动物声"},tags:["tropical","wildlife","exotic"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.2,focusEffectiveness:7.8},{id:"nature_river",filename:"river.mp3",category:"nature",title:{en:"River",zh:"河流"},description:{en:"Flowing river water for natural relaxation",zh:"流淌的河水，自然放松"},tags:["flowing","natural","peaceful"],duration:3600,scientificRating:9.1,sleepEffectiveness:9.4,focusEffectiveness:8.9},{id:"nature_walk_in_snow",filename:"walk-in-snow.mp3",category:"nature",title:{en:"Walk in Snow",zh:"雪中漫步"},description:{en:"Footsteps crunching in fresh snow",zh:"脚步踩在新雪上的声音"},tags:["winter","crisp","peaceful"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8},{id:"nature_walk_on_gravel",filename:"walk-on-gravel.mp3",category:"nature",title:{en:"Walk on Gravel",zh:"砾石路漫步"},description:{en:"Footsteps on gravel path",zh:"脚步声在砾石路上"},tags:["walking","texture","rhythmic"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.9,focusEffectiveness:7.5},{id:"nature_walk_on_leaves",filename:"walk-on-leaves.mp3",category:"nature",title:{en:"Walk on Leaves",zh:"踏叶而行"},description:{en:"Footsteps rustling through autumn leaves",zh:"脚步踩过秋叶的沙沙声"},tags:["autumn","rustling","seasonal"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.5,focusEffectiveness:8.1},{id:"nature_waterfall",filename:"waterfall.mp3",category:"nature",title:{en:"Waterfall",zh:"瀑布"},description:{en:"Powerful waterfall cascading down rocks",zh:"瀑布从岩石上倾泻而下"},tags:["powerful","cascading","majestic"],duration:3600,scientificRating:8.9,sleepEffectiveness:8.7,focusEffectiveness:8.4},{id:"nature_waves",filename:"waves.mp3",category:"nature",title:{en:"Ocean Waves",zh:"海浪"},description:{en:"Gentle ocean waves lapping the shore",zh:"轻柔的海浪拍打海岸"},tags:["ocean","rhythmic","calming"],duration:3600,scientificRating:9.3,sleepEffectiveness:9.6,focusEffectiveness:9},{id:"nature_wind_in_trees",filename:"wind-in-trees.mp3",category:"nature",title:{en:"Wind in Trees",zh:"林间风声"},description:{en:"Wind rustling through tree branches",zh:"风吹过树枝的沙沙声"},tags:["rustling","peaceful","forest"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.6},{id:"nature_wind",filename:"wind.mp3",category:"nature",title:{en:"Wind",zh:"风声"},description:{en:"Gentle wind blowing across open spaces",zh:"轻风吹过开阔地带"},tags:["gentle","open","airy"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8.3},{id:"noise_brown_noise",filename:"brown-noise.wav",category:"noise",title:{en:"Brown Noise",zh:"棕色噪音"},description:{en:"Deep brown noise for focus and relaxation",zh:"深沉的棕色噪音，专注与放松"},tags:["deep","focus","masking"],duration:3600,scientificRating:9,sleepEffectiveness:8.8,focusEffectiveness:9.2},{id:"noise_pink_noise",filename:"pink-noise.wav",category:"noise",title:{en:"Pink Noise",zh:"粉色噪音"},description:{en:"Balanced pink noise for sleep and concentration",zh:"平衡的粉色噪音，助眠与专注"},tags:["balanced","sleep","concentration"],duration:3600,scientificRating:9.2,sleepEffectiveness:9.4,focusEffectiveness:9.1},{id:"noise_white_noise",filename:"white-noise.wav",category:"noise",title:{en:"White Noise",zh:"白色噪音"},description:{en:"Classic white noise for sound masking",zh:"经典白色噪音，声音遮蔽"},tags:["classic","masking","consistent"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:9.3},{id:"animals_beehive",filename:"beehive.mp3",category:"animals",title:{en:"Beehive",zh:"蜂巢"},description:{en:"Gentle buzzing of bees in their hive",zh:"蜜蜂在蜂巢中轻柔的嗡嗡声"},tags:["buzzing","gentle","productive"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.5},{id:"animals_birds",filename:"birds.mp3",category:"animals",title:{en:"Birds",zh:"鸟鸣"},description:{en:"Cheerful bird songs for morning atmosphere",zh:"欢快的鸟鸣声，晨间氛围"},tags:["cheerful","morning","natural"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.2,focusEffectiveness:8.8},{id:"animals_cat_purring",filename:"cat-purring.mp3",category:"animals",title:{en:"Cat Purring",zh:"猫咪呼噜声"},description:{en:"Soothing cat purring for comfort and relaxation",zh:"舒缓的猫咪呼噜声，带来安慰与放松"},tags:["soothing","comfort","cozy"],duration:3600,scientificRating:8.9,sleepEffectiveness:9.1,focusEffectiveness:8.4}];function h({className:e}){let t=(0,r.useTranslations)("mixing"),[i,h]=(0,a.useState)(!1),[f,x]=(0,a.useState)("all"),{playerUI:p,mixingChannels:y,maxChannels:v,masterVolume:b,addMixingChannel:w,removeMixingChannel:j,updateChannelVolume:k,setMasterVolume:N,setMixingPanelVisible:z}=(0,c.U)(),{playChannel:E,pauseChannel:P,stopChannel:C,setChannelVolume:D,muteChannel:S,unmuteChannel:_,getChannelState:R,stopAllChannels:M,setMasterVolume:L}=(0,m.o)(),B=g.filter(e=>!y.some(t=>t.soundId===e.id));"all"===f||B.filter(e=>e.category===f),Array.from(new Set(g.map(e=>e.category))).sort(),(0,a.useCallback)(e=>{w(e)&&h(!1)},[w]);let T=(0,a.useCallback)(e=>{j(e)},[j]),A=(0,a.useCallback)(e=>{E(e)},[E]),W=(0,a.useCallback)(e=>{P(e)},[P]),U=(0,a.useCallback)(e=>{C(e)},[C]),V=(0,a.useCallback)((e,t)=>{D(e,t)},[D]),I=(0,a.useCallback)((e,t)=>{t?S(e):_(e)},[S,_]),$=(0,a.useCallback)(e=>{N(e),L(e)},[N,L]),F=e=>g.find(t=>t.id===e),H=()=>{z(!1)};return p.showMixingPanel?s.jsx(n.M,{children:s.jsx(o.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",onClick:H,children:(0,s.jsxs)(o.E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:(0,l.W)("absolute bottom-0 left-0 right-0 max-h-[80vh] overflow-y-auto","bg-white dark:bg-gray-900 rounded-t-xl shadow-2xl","border-t border-gray-200 dark:border-gray-700",e),onClick:e=>e.stopPropagation(),children:[s.jsx("div",{className:"sticky top-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t("title")}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:t("description",{max:v})})]}),s.jsx("button",{onClick:H,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",children:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}),(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:t("masterVolume")}),(0,s.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[Math.round(100*b),"%"]})]}),s.jsx(u.VolumeControl,{volume:b,onVolumeChange:$,size:"sm"})]}),0===y.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[s.jsx("div",{className:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",children:s.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"})})}),s.jsx("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:t("noChannels")}),s.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:t("addFirstAudio")}),(0,s.jsxs)("button",{onClick:()=>h(!0),className:"inline-flex items-center gap-2 px-4 py-2 bg-amber-500 text-white rounded-lg font-medium hover:bg-amber-600 transition-colors",children:[s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),t("addAudio")]})]}):(0,s.jsxs)("div",{className:"space-y-3",children:[y.map(e=>{let t=F(e.soundId);if(!t)return null;let i=R(e.id);return s.jsx(d.MixingChannel,{channel:e,audio:t,isPlaying:i.isPlaying,isLoading:i.isLoading,onPlay:()=>A(e.id),onPause:()=>W(e.id),onStop:()=>U(e.id),onVolumeChange:t=>V(e.id,t),onMute:()=>I(e.id,!e.isMuted),onRemove:()=>T(e.id)},e.id)}),y.length<v&&s.jsx("button",{onClick:()=>h(!0),className:"w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-amber-400 hover:text-amber-600 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),t("addAudio")]})})]})]})]})})}):null}},6970:(e,t,i)=>{"use strict";i.d(t,{S:()=>n});var s=i(326),a=i(3844),r=i(7428);function n({className:e="",variant:t="compact",showProgress:i=!0}){let n=(0,a.useTranslations)("timer"),{isActive:o,remainingTime:l,formatRemainingTime:c,progress:d,isNearEnd:u}=(0,r.M)();if(!o)return null;let m=c(l),g=Math.round(100*d);return s.jsx("div",{className:`timer-display ${e}`,children:"full"===t?(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,s.jsxs)("div",{className:`text-center ${u?"animate-pulse":""}`,children:[s.jsx("div",{className:`font-mono font-bold ${"full"===t?"text-3xl":"text-lg"} ${u?"text-red-500":"text-gray-900 dark:text-white"}`,children:m}),s.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:n("remaining")})]}),i&&(0,s.jsxs)("div",{className:"w-full max-w-xs",children:[(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1",children:[s.jsx("span",{children:n("progress")}),(0,s.jsxs)("span",{children:[g,"%"]})]}),s.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:s.jsx("div",{className:`h-2 rounded-full transition-all duration-1000 ${u?"bg-red-500":"bg-amber-500"}`,style:{width:`${g}%`}})})]}),u&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-red-500 text-sm",children:[s.jsx("svg",{className:"w-4 h-4 animate-pulse",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),s.jsx("span",{children:n("fadeOutSoon")})]})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[s.jsx("svg",{className:"w-4 h-4 text-amber-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),s.jsx("span",{className:`font-mono text-sm font-medium ${u?"text-red-500 animate-pulse":"text-gray-700 dark:text-gray-300"}`,children:m})]}),i&&s.jsx("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-1",children:s.jsx("div",{className:`h-1 rounded-full transition-all duration-1000 ${u?"bg-red-500":"bg-amber-500"}`,style:{width:`${g}%`}})})]})})}},6614:(e,t,i)=>{"use strict";i.d(t,{DA:()=>a}),parseInt("100");let s={rain:"rain",nature:"nature",noise:"noise",animals:"animals",things:"things",transport:"transport",urban:"urban",places:"places",ocean:"ocean"};function a(e,t){console.log("\uD83D\uDEA8\uD83D\uDEA8\uD83D\uDEA8 getAudioUrl 函数被调用!!! \uD83D\uDEA8\uD83D\uDEA8\uD83D\uDEA8"),console.log("\uD83D\uDCE5 输入参数:",{category:e,filename:t});let i=s[e.toLowerCase()]||e.toLowerCase();console.log("\uD83D\uDCC1 文件夹映射:",{category:e,folderName:i}),console.log("\uD83D\uDD0D getAudioUrl 调试:",{category:e,filename:t,folderName:i,NEXT_PUBLIC_AUDIO_CDN_URL:"https://cdn.noisesleep.com/sounds",NEXT_PUBLIC_CDN_PERCENTAGE:"100",allEnvVars:Object.keys(process.env).filter(e=>e.startsWith("NEXT_PUBLIC_"))});{let e=`https://cdn.noisesleep.com/sounds/${i}/${t}`;return console.log("✅ 使用CDN URL:",e),console.log("\uD83D\uDEA8 返回CDN URL:",e),e}}},4222:(e,t,i)=>{"use strict";i.d(t,{N:()=>s});let s=[{id:"urban_highway",filename:"highway.mp3",category:"urban",title:{en:"Highway",zh:"高速公路"},description:{en:"Steady highway traffic sounds for focus and relaxation",zh:"稳定的高速公路交通声，专注与放松"},tags:["traffic","steady","focus"],duration:3600,scientificRating:8.2,sleepEffectiveness:7.8,focusEffectiveness:8.5},{id:"urban_busy_street",filename:"busy-street.mp3",category:"urban",title:{en:"Busy Street",zh:"繁忙街道"},description:{en:"Urban street ambience with moderate activity",zh:"城市街道环境音，中等活跃度"},tags:["urban","street","ambient"],duration:3600,scientificRating:7.9,sleepEffectiveness:7.2,focusEffectiveness:8.1},{id:"urban_traffic",filename:"traffic.mp3",category:"urban",title:{en:"Traffic",zh:"交通声"},description:{en:"General traffic sounds for urban atmosphere",zh:"一般交通声音，城市氛围"},tags:["traffic","urban","background"],duration:3600,scientificRating:8,sleepEffectiveness:7.5,focusEffectiveness:8.3},{id:"urban_road",filename:"road.mp3",category:"urban",title:{en:"Road",zh:"道路"},description:{en:"Road traffic with consistent rhythm",zh:"道路交通，节奏一致"},tags:["road","consistent","rhythm"],duration:3600,scientificRating:8.1,sleepEffectiveness:7.7,focusEffectiveness:8.4},{id:"urban_fireworks",filename:"fireworks.mp3",category:"urban",title:{en:"Fireworks",zh:"烟花"},description:{en:"Distant fireworks for celebration atmosphere",zh:"远处烟花声，庆祝氛围"},tags:["celebration","distant","festive"],duration:3600,scientificRating:7.5,sleepEffectiveness:6.8,focusEffectiveness:7.2},{id:"urban_ambulance_siren",filename:"ambulance-siren.mp3",category:"urban",title:{en:"Ambulance Siren",zh:"救护车警报"},description:{en:"Distant ambulance siren in urban environment",zh:"城市环境中的远处救护车警报"},tags:["siren","emergency","urban"],duration:3600,scientificRating:6.8,sleepEffectiveness:6,focusEffectiveness:6.5},{id:"urban_crowd",filename:"crowd.mp3",category:"urban",title:{en:"Crowd",zh:"人群"},description:{en:"Distant crowd chatter for social ambience",zh:"远处人群交谈声，社交氛围"},tags:["crowd","social","chatter"],duration:3600,scientificRating:7.6,sleepEffectiveness:7,focusEffectiveness:7.8},{id:"rain_on_leaves",filename:"rain-on-leaves.mp3",category:"rain",title:{en:"Rain on Leaves",zh:"雨打树叶"},description:{en:"Gentle rain falling on leaves for natural relaxation",zh:"轻柔雨声打在树叶上，自然放松"},tags:["gentle","natural","leaves"],duration:3600,scientificRating:9.1,sleepEffectiveness:9.3,focusEffectiveness:8.8},{id:"rain_on_tent",filename:"rain-on-tent.mp3",category:"rain",title:{en:"Rain on Tent",zh:"雨打帐篷"},description:{en:"Cozy rain sounds on tent fabric",zh:"雨声打在帐篷布上，温馨舒适"},tags:["cozy","tent","camping"],duration:3600,scientificRating:8.9,sleepEffectiveness:9.1,focusEffectiveness:8.6},{id:"rain_on_umbrella",filename:"rain-on-umbrella.mp3",category:"rain",title:{en:"Rain on Umbrella",zh:"雨打雨伞"},description:{en:"Rain drops hitting umbrella surface",zh:"雨滴打在雨伞表面"},tags:["umbrella","drops","rhythmic"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.4},{id:"rain_on_car_roof",filename:"rain-on-car-roof.mp3",category:"rain",title:{en:"Rain on Car Roof",zh:"雨打车顶"},description:{en:"Rain falling on car roof for cozy atmosphere",zh:"雨声打在车顶上，温馨氛围"},tags:["car","cozy","shelter"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.5},{id:"rain_on_window",filename:"rain-on-window.mp3",category:"rain",title:{en:"Rain on Window",zh:"雨打窗户"},description:{en:"Rain drops on window glass for indoor comfort",zh:"雨滴打在窗玻璃上，室内舒适感"},tags:["window","indoor","comfort"],duration:3600,scientificRating:9,sleepEffectiveness:9.2,focusEffectiveness:8.7},{id:"rain_heavy_rain",filename:"heavy-rain.mp3",category:"rain",title:{en:"Heavy Rain",zh:"大雨"},description:{en:"Intense rainfall for deep relaxation",zh:"强烈降雨，深度放松"},tags:["intense","powerful","relaxing"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:8.2},{id:"rain_thunder",filename:"thunder.mp3",category:"rain",title:{en:"Thunder",zh:"雷声"},description:{en:"Distant thunder with rain for dramatic atmosphere",zh:"远处雷声伴随雨声，戏剧性氛围"},tags:["thunder","dramatic","distant"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.1,focusEffectiveness:7.8},{id:"rain_light_rain",filename:"light-rain.mp3",category:"rain",title:{en:"Light Rain",zh:"轻雨"},description:{en:"Gentle light rain for peaceful relaxation",zh:"轻柔细雨，宁静放松"},tags:["gentle","soft","peaceful"],duration:3600,scientificRating:9.2,sleepEffectiveness:9.5,focusEffectiveness:8.9},{id:"transport_airplane",filename:"airplane.mp3",category:"transport",title:{en:"Airplane",zh:"飞机"},description:{en:"Steady airplane cabin ambience for travel relaxation",zh:"稳定的飞机客舱环境音，旅行放松"},tags:["airplane","cabin","travel"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.6,focusEffectiveness:8.8},{id:"transport_rowing_boat",filename:"rowing-boat.mp3",category:"transport",title:{en:"Rowing Boat",zh:"划船"},description:{en:"Peaceful rowing boat sounds on calm water",zh:"平静水面上的划船声，宁静祥和"},tags:["rowing","peaceful","water"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.5},{id:"transport_submarine",filename:"submarine.mp3",category:"transport",title:{en:"Submarine",zh:"潜水艇"},description:{en:"Deep underwater submarine ambience",zh:"深海潜水艇环境音"},tags:["submarine","underwater","deep"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8.6},{id:"transport_train",filename:"train.mp3",category:"transport",title:{en:"Train",zh:"火车"},description:{en:"Rhythmic train sounds for steady relaxation",zh:"有节奏的火车声，稳定放松"},tags:["train","rhythmic","steady"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8.9},{id:"transport_sailboat",filename:"sailboat.mp3",category:"transport",title:{en:"Sailboat",zh:"帆船"},description:{en:"Gentle sailboat on calm seas",zh:"平静海面上的帆船"},tags:["sailboat","calm","seas"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.6},{id:"transport_inside_train",filename:"inside-a-train.mp3",category:"transport",title:{en:"Inside a Train",zh:"火车内部"},description:{en:"Interior train ambience for travel comfort",zh:"火车内部环境音，旅行舒适感"},tags:["interior","travel","comfort"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.7},{id:"places_construction_site",filename:"construction-site.mp3",category:"places",title:{en:"Construction Site",zh:"建筑工地"},description:{en:"Distant construction sounds for industrial ambience",zh:"远处建筑声音，工业氛围"},tags:["construction","industrial","distant"],duration:3600,scientificRating:7.2,sleepEffectiveness:6.8,focusEffectiveness:7.5},{id:"places_underwater",filename:"underwater.mp3",category:"places",title:{en:"Underwater",zh:"水下"},description:{en:"Peaceful underwater ambience for deep relaxation",zh:"宁静的水下环境音，深度放松"},tags:["underwater","peaceful","deep"],duration:3600,scientificRating:8.9,sleepEffectiveness:9.1,focusEffectiveness:8.7},{id:"places_airport",filename:"airport.mp3",category:"places",title:{en:"Airport",zh:"机场"},description:{en:"Airport terminal ambience for travel atmosphere",zh:"机场候机厅环境音，旅行氛围"},tags:["airport","terminal","travel"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.4,focusEffectiveness:8.1},{id:"places_church",filename:"church.mp3",category:"places",title:{en:"Church",zh:"教堂"},description:{en:"Peaceful church ambience for meditation",zh:"宁静的教堂环境音，适合冥想"},tags:["church","peaceful","meditation"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.4},{id:"places_temple",filename:"temple.mp3",category:"places",title:{en:"Temple",zh:"寺庙"},description:{en:"Serene temple atmosphere for spiritual relaxation",zh:"宁静的寺庙氛围，精神放松"},tags:["temple","serene","spiritual"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.5},{id:"places_cafe",filename:"cafe.mp3",category:"places",title:{en:"Cafe",zh:"咖啡厅"},description:{en:"Cozy cafe ambience for work and relaxation",zh:"温馨的咖啡厅环境音，工作与放松"},tags:["cafe","cozy","work"],duration:3600,scientificRating:8.3,sleepEffectiveness:7.9,focusEffectiveness:8.7},{id:"nature_waterfall",filename:"waterfall.mp3",category:"nature",title:{en:"Waterfall",zh:"瀑布"},description:{en:"Powerful waterfall sounds for natural white noise",zh:"强劲的瀑布声，天然白噪音"},tags:["waterfall","powerful","white-noise"],duration:3600,scientificRating:9,sleepEffectiveness:9.2,focusEffectiveness:8.8},{id:"nature_walk_on_gravel",filename:"walk-on-gravel.mp3",category:"nature",title:{en:"Walk on Gravel",zh:"砾石路行走"},description:{en:"Footsteps on gravel path for meditative walking",zh:"砾石路上的脚步声，冥想式行走"},tags:["footsteps","gravel","meditative"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.5},{id:"nature_jungle",filename:"jungle.mp3",category:"nature",title:{en:"Jungle",zh:"丛林"},description:{en:"Rich jungle ambience with diverse natural sounds",zh:"丰富的丛林环境音，多样化自然声音"},tags:["jungle","rich","diverse"],duration:3600,scientificRating:8.8,sleepEffectiveness:8.6,focusEffectiveness:8.9},{id:"nature_howling_wind",filename:"howling-wind.mp3",category:"nature",title:{en:"Howling Wind",zh:"呼啸风声"},description:{en:"Strong wind sounds for dramatic natural atmosphere",zh:"强风声音，戏剧性自然氛围"},tags:["wind","strong","dramatic"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.2,focusEffectiveness:8.6},{id:"nature_waves",filename:"waves.mp3",category:"nature",title:{en:"Waves",zh:"海浪"},description:{en:"Gentle ocean waves for coastal relaxation",zh:"轻柔的海浪声，海岸放松"},tags:["waves","ocean","coastal"],duration:3600,scientificRating:9.1,sleepEffectiveness:9.3,focusEffectiveness:8.7},{id:"nature_wind_in_trees",filename:"wind-in-trees.mp3",category:"nature",title:{en:"Wind in Trees",zh:"树林风声"},description:{en:"Gentle wind through trees for forest atmosphere",zh:"轻风穿过树林，森林氛围"},tags:["wind","trees","forest"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:8.8},{id:"nature_wind",filename:"wind.mp3",category:"nature",title:{en:"Wind",zh:"风声"},description:{en:"Pure wind sounds for natural ambience",zh:"纯净的风声，自然环境音"},tags:["wind","pure","natural"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.8,focusEffectiveness:8.6},{id:"nature_campfire",filename:"campfire.mp3",category:"nature",title:{en:"Campfire",zh:"篝火"},description:{en:"Crackling campfire for cozy outdoor atmosphere",zh:"噼啪作响的篝火，温馨户外氛围"},tags:["campfire","crackling","cozy"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.2},{id:"nature_river",filename:"river.mp3",category:"nature",title:{en:"River",zh:"河流"},description:{en:"Flowing river sounds for peaceful relaxation",zh:"流水声，宁静放松"},tags:["river","flowing","peaceful"],duration:3600,scientificRating:9,sleepEffectiveness:9.1,focusEffectiveness:8.9},{id:"nature_droplets",filename:"droplets.mp3",category:"nature",title:{en:"Water Droplets",zh:"水滴"},description:{en:"Gentle water droplets for meditation",zh:"轻柔水滴声，适合冥想"},tags:["droplets","gentle","meditation"],duration:3600,scientificRating:9,sleepEffectiveness:9.3,focusEffectiveness:8.7},{id:"nature_walk_on_leaves",filename:"walk-on-leaves.mp3",category:"nature",title:{en:"Walk on Leaves",zh:"踩落叶"},description:{en:"Footsteps on autumn leaves for seasonal atmosphere",zh:"踩在秋叶上的脚步声，季节氛围"},tags:["footsteps","leaves","autumn"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8.3},{id:"nature_walk_in_snow",filename:"walk-in-snow.mp3",category:"nature",title:{en:"Walk in Snow",zh:"雪地行走"},description:{en:"Footsteps in snow for winter tranquility",zh:"雪地中的脚步声，冬日宁静"},tags:["footsteps","snow","winter"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8.4},{id:"noise_pink_noise",filename:"pink-noise.wav",category:"noise",title:{en:"Pink Noise",zh:"粉色噪音"},description:{en:"Balanced pink noise for sleep and concentration",zh:"平衡的粉色噪音，助眠与专注"},tags:["pink-noise","balanced","sleep"],duration:3600,scientificRating:9.2,sleepEffectiveness:9.4,focusEffectiveness:9.1},{id:"noise_white_noise",filename:"white-noise.wav",category:"noise",title:{en:"White Noise",zh:"白色噪音"},description:{en:"Classic white noise for sound masking",zh:"经典白色噪音，声音遮蔽"},tags:["white-noise","classic","masking"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:9.3},{id:"noise_brown_noise",filename:"brown-noise.wav",category:"noise",title:{en:"Brown Noise",zh:"棕色噪音"},description:{en:"Deep brown noise for focus and relaxation",zh:"深沉的棕色噪音，专注与放松"},tags:["brown-noise","deep","focus"],duration:3600,scientificRating:9,sleepEffectiveness:8.8,focusEffectiveness:9.2},{id:"animals_beehive",filename:"beehive.mp3",category:"animals",title:{en:"Beehive",zh:"蜂巢"},description:{en:"Gentle buzzing of bees in their hive",zh:"蜜蜂在蜂巢中轻柔的嗡嗡声"},tags:["bees","buzzing","gentle"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.5},{id:"animals_birds",filename:"birds.mp3",category:"animals",title:{en:"Birds",zh:"鸟鸣"},description:{en:"Cheerful bird songs for morning atmosphere",zh:"欢快的鸟鸣声，晨间氛围"},tags:["birds","cheerful","morning"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.2,focusEffectiveness:8.8},{id:"animals_dog_barking",filename:"dog-barking.mp3",category:"animals",title:{en:"Dog Barking",zh:"狗叫声"},description:{en:"Distant dog barking for neighborhood ambience",zh:"远处狗叫声，邻里氛围"},tags:["dog","barking","distant"],duration:3600,scientificRating:6.8,sleepEffectiveness:6.2,focusEffectiveness:7.1},{id:"animals_cat_purring",filename:"cat-purring.mp3",category:"animals",title:{en:"Cat Purring",zh:"猫咪呼噜声"},description:{en:"Soothing cat purring for comfort and relaxation",zh:"舒缓的猫咪呼噜声，带来安慰与放松"},tags:["cat","purring","soothing"],duration:3600,scientificRating:8.9,sleepEffectiveness:9.1,focusEffectiveness:8.4},{id:"animals_wolf",filename:"wolf.mp3",category:"animals",title:{en:"Wolf",zh:"狼嚎"},description:{en:"Distant wolf howling for wild atmosphere",zh:"远处狼嚎声，野性氛围"},tags:["wolf","howling","wild"],duration:3600,scientificRating:7.5,sleepEffectiveness:7.2,focusEffectiveness:7.8},{id:"animals_frog",filename:"frog.mp3",category:"animals",title:{en:"Frog",zh:"青蛙"},description:{en:"Peaceful frog sounds for pond atmosphere",zh:"宁静的青蛙声，池塘氛围"},tags:["frog","peaceful","pond"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.5,focusEffectiveness:8.1},{id:"animals_seagulls",filename:"seagulls.mp3",category:"animals",title:{en:"Seagulls",zh:"海鸥"},description:{en:"Seagull calls for coastal atmosphere",zh:"海鸥叫声，海岸氛围"},tags:["seagulls","coastal","calls"],duration:3600,scientificRating:8,sleepEffectiveness:7.8,focusEffectiveness:8.2},{id:"animals_crickets",filename:"crickets.mp3",category:"animals",title:{en:"Crickets",zh:"蟋蟀"},description:{en:"Rhythmic cricket sounds for summer nights",zh:"有节奏的蟋蟀声，夏夜氛围"},tags:["crickets","rhythmic","summer"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.3},{id:"animals_sheep",filename:"sheep.mp3",category:"animals",title:{en:"Sheep",zh:"羊群"},description:{en:"Gentle sheep sounds for pastoral atmosphere",zh:"温和的羊群声，田园氛围"},tags:["sheep","gentle","pastoral"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8},{id:"animals_horse_galopp",filename:"horse-galopp.mp3",category:"animals",title:{en:"Horse Gallop",zh:"马蹄声"},description:{en:"Rhythmic horse galloping for dynamic atmosphere",zh:"有节奏的马蹄声，动感氛围"},tags:["horse","gallop","rhythmic"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.4,focusEffectiveness:8.1},{id:"animals_cows",filename:"cows.mp3",category:"animals",title:{en:"Cows",zh:"牛群"},description:{en:"Peaceful cow sounds for farm atmosphere",zh:"宁静的牛群声，农场氛围"},tags:["cows","peaceful","farm"],duration:3600,scientificRating:8,sleepEffectiveness:8.2,focusEffectiveness:7.9},{id:"animals_owl",filename:"owl.mp3",category:"animals",title:{en:"Owl",zh:"猫头鹰"},description:{en:"Mysterious owl hooting for night atmosphere",zh:"神秘的猫头鹰叫声，夜晚氛围"},tags:["owl","mysterious","night"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.6,focusEffectiveness:8.2},{id:"animals_whale",filename:"whale.mp3",category:"animals",title:{en:"Whale",zh:"鲸鱼"},description:{en:"Deep whale songs for oceanic meditation",zh:"深沉的鲸鱼歌声，海洋冥想"},tags:["whale","deep","oceanic"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.6},{id:"animals_woodpecker",filename:"woodpecker.mp3",category:"animals",title:{en:"Woodpecker",zh:"啄木鸟"},description:{en:"Rhythmic woodpecker sounds for forest atmosphere",zh:"有节奏的啄木鸟声，森林氛围"},tags:["woodpecker","rhythmic","forest"],duration:3600,scientificRating:7.9,sleepEffectiveness:7.7,focusEffectiveness:8.3},{id:"animals_crows",filename:"crows.mp3",category:"animals",title:{en:"Crows",zh:"乌鸦"},description:{en:"Distant crow calls for atmospheric ambience",zh:"远处乌鸦叫声，氛围环境音"},tags:["crows","distant","atmospheric"],duration:3600,scientificRating:7.3,sleepEffectiveness:7,focusEffectiveness:7.6},{id:"animals_chickens",filename:"chickens.mp3",category:"animals",title:{en:"Chickens",zh:"鸡群"},description:{en:"Gentle chicken sounds for farmyard atmosphere",zh:"温和的鸡群声，农家院氛围"},tags:["chickens","gentle","farmyard"],duration:3600,scientificRating:7.7,sleepEffectiveness:7.5,focusEffectiveness:7.9},{id:"things_bubbles",filename:"bubbles.mp3",category:"things",title:{en:"Bubbles",zh:"气泡"},description:{en:"Gentle bubble sounds for playful relaxation",zh:"轻柔的气泡声，轻松愉快的放松"},tags:["bubbles","gentle","playful"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.5,focusEffectiveness:8.1},{id:"things_boiling_water",filename:"boiling-water.mp3",category:"things",title:{en:"Boiling Water",zh:"沸水"},description:{en:"Steady boiling water sounds for kitchen comfort",zh:"稳定的沸水声，厨房舒适感"},tags:["boiling","water","steady"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.4},{id:"things_wind_chimes",filename:"wind-chimes.mp3",category:"things",title:{en:"Wind Chimes",zh:"风铃"},description:{en:"Melodic wind chimes for peaceful atmosphere",zh:"悦耳的风铃声，宁静氛围"},tags:["wind-chimes","melodic","peaceful"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.5},{id:"things_vinyl_effect",filename:"vinyl-effect.mp3",category:"things",title:{en:"Vinyl Effect",zh:"黑胶唱片"},description:{en:"Nostalgic vinyl record crackling for vintage atmosphere",zh:"怀旧的黑胶唱片噼啪声，复古氛围"},tags:["vinyl","nostalgic","vintage"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8.6},{id:"things_tuning_radio",filename:"tuning-radio.mp3",category:"things",title:{en:"Tuning Radio",zh:"调频收音机"},description:{en:"Radio static and tuning for retro ambience",zh:"收音机静电和调频声，复古环境音"},tags:["radio","static","retro"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.6,focusEffectiveness:8},{id:"things_ceiling_fan",filename:"ceiling-fan.mp3",category:"things",title:{en:"Ceiling Fan",zh:"吊扇"},description:{en:"Steady ceiling fan for consistent white noise",zh:"稳定的吊扇声，持续白噪音"},tags:["fan","steady","white-noise"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8.8},{id:"things_washing_machine",filename:"washing-machine.mp3",category:"things",title:{en:"Washing Machine",zh:"洗衣机"},description:{en:"Rhythmic washing machine for household comfort",zh:"有节奏的洗衣机声，家庭舒适感"},tags:["washing-machine","rhythmic","household"],duration:3600,scientificRating:8,sleepEffectiveness:8.2,focusEffectiveness:8.3},{id:"things_clock",filename:"clock.mp3",category:"things",title:{en:"Clock",zh:"时钟"},description:{en:"Steady clock ticking for time meditation",zh:"稳定的时钟滴答声，时间冥想"},tags:["clock","ticking","meditation"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.6,focusEffectiveness:8.7},{id:"things_morse_code",filename:"morse-code.mp3",category:"things",title:{en:"Morse Code",zh:"摩尔斯电码"},description:{en:"Rhythmic morse code for focus and concentration",zh:"有节奏的摩尔斯电码，专注与集中"},tags:["morse-code","rhythmic","focus"],duration:3600,scientificRating:7.9,sleepEffectiveness:7.5,focusEffectiveness:8.4},{id:"things_windshield_wipers",filename:"windshield-wipers.mp3",category:"things",title:{en:"Windshield Wipers",zh:"雨刷器"},description:{en:"Steady windshield wipers for car comfort",zh:"稳定的雨刷器声，车内舒适感"},tags:["wipers","steady","car"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.2},{id:"things_typewriter",filename:"typewriter.mp3",category:"things",title:{en:"Typewriter",zh:"打字机"},description:{en:"Classic typewriter sounds for productive atmosphere",zh:"经典打字机声，高效工作氛围"},tags:["typewriter","classic","productive"],duration:3600,scientificRating:8.3,sleepEffectiveness:7.8,focusEffectiveness:8.9},{id:"things_dryer",filename:"dryer.mp3",category:"things",title:{en:"Dryer",zh:"烘干机"},description:{en:"Steady dryer sounds for household comfort",zh:"稳定的烘干机声，家庭舒适感"},tags:["dryer","steady","household"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8.1},{id:"things_paper",filename:"paper.mp3",category:"things",title:{en:"Paper",zh:"纸张"},description:{en:"Gentle paper rustling for reading atmosphere",zh:"轻柔的纸张沙沙声，阅读氛围"},tags:["paper","rustling","reading"],duration:3600,scientificRating:8,sleepEffectiveness:8.1,focusEffectiveness:8.5},{id:"things_singing_bowl",filename:"singing-bowl.mp3",category:"things",title:{en:"Singing Bowl",zh:"颂钵"},description:{en:"Resonant singing bowl for meditation and healing",zh:"共鸣颂钵声，冥想与疗愈"},tags:["singing-bowl","meditation","healing"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.6},{id:"things_keyboard",filename:"keyboard.mp3",category:"things",title:{en:"Keyboard",zh:"键盘"},description:{en:"Gentle keyboard typing for work ambience",zh:"轻柔的键盘敲击声，工作环境音"},tags:["keyboard","typing","work"],duration:3600,scientificRating:8.1,sleepEffectiveness:7.7,focusEffectiveness:8.6},{id:"things_slide_projector",filename:"slide-projector.mp3",category:"things",title:{en:"Slide Projector",zh:"幻灯机"},description:{en:"Nostalgic slide projector for vintage atmosphere",zh:"怀旧幻灯机声，复古氛围"},tags:["projector","nostalgic","vintage"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.9,focusEffectiveness:8.2}]},7768:(e,t,i)=>{"use strict";i.d(t,{x:()=>o});var s=i(7577),a=i(7230),r=i(184),n=i(6614);let o=()=>{let e=(0,s.useRef)(null),t=(0,s.useRef)(null),{currentSound:i,playState:o,userVolume:l,setCurrentSound:c,updatePlayState:d,setUserVolume:u}=(0,r.U)(),m=(0,s.useCallback)(()=>{t.current&&(clearInterval(t.current),t.current=null)},[]),g=(0,s.useCallback)(()=>{m(),t.current=setInterval(()=>{e.current&&e.current.playing()&&d({currentTime:e.current.seek()})},1e3)},[d,m]),h=(0,s.useCallback)(t=>{try{if(console.log("\uD83C\uDFB5 播放音频被调用:",{sound:t?.title,currentSound:i?.title}),t&&t.id!==i?.id){console.log("\uD83D\uDD04 切换到新音频:",t.title),console.log("\uD83D\uDEA8\uD83D\uDEA8\uD83D\uDEA8 DEBUG: 准备停止当前音频"),e.current&&(e.current.stop(),e.current.unload(),e.current=null),m(),d({isPlaying:!1,isPaused:!1,currentTime:0}),console.log("✅ 停止音频完成"),c(t),console.log("\uD83D\uDEA8 useAudioPlayer: 准备调用 getAudioUrl",{category:t.category,filename:t.filename});let i=(0,n.DA)(t.category,t.filename);console.log("\uD83D\uDEA8 useAudioPlayer: getAudioUrl 返回结果:",i),e.current=new a.Howl({src:[i],volume:l,loop:o.isLooping,onload:()=>{d({isLoading:!1,duration:e.current?.duration()||0,error:void 0})},onplay:()=>{d({isPlaying:!0,isPaused:!1,error:void 0}),g()},onpause:()=>{d({isPlaying:!1,isPaused:!0}),m()},onstop:()=>{d({isPlaying:!1,isPaused:!1,currentTime:0}),m()},onend:()=>{d({isPlaying:!1,isPaused:!1,currentTime:0}),m()},onloaderror:(e,t)=>{console.error("音频加载失败:",t),d({isLoading:!1,error:"音频加载失败"})},onplayerror:(e,t)=>{console.error("音频播放失败:",t),d({isPlaying:!1,error:"音频播放失败"})}}),d({isLoading:!0})}e.current&&(o.isPaused,e.current.play())}catch(e){console.error("播放音频时发生错误:",e),d({error:"播放失败",isLoading:!1})}},[o.isLooping,o.isPaused,l,c,d,g,i,m]),f=(0,s.useCallback)(()=>{e.current&&e.current.playing()&&e.current.pause()},[]),x=(0,s.useCallback)(()=>{e.current&&(e.current.stop(),e.current.unload(),e.current=null),m(),d({isPlaying:!1,isPaused:!1,currentTime:0})},[m,d]),p=(0,s.useCallback)(t=>{let i=Math.max(0,Math.min(1,t));u(i),d({volume:i}),e.current&&e.current.volume(i),a.Howler.volume(i)},[u,d]),y=(0,s.useCallback)(t=>{d({isLooping:t}),e.current&&e.current.loop(t)},[d]),v=(0,s.useCallback)(t=>{e.current&&(e.current.seek(t),d({currentTime:t}))},[d]);return(0,s.useEffect)(()=>()=>{x()},[x]),(0,s.useEffect)(()=>{e.current&&o.volume!==l&&e.current.volume(l)},[l,o.volume]),{play:h,pause:f,stop:x,setVolume:p,setLoop:y,seek:v,isPlaying:o.isPlaying,isPaused:o.isPaused,isLoading:o.isLoading,currentTime:o.currentTime,duration:o.duration,volume:o.volume,isLooping:o.isLooping,error:o.error||null}}},18:(e,t,i)=>{"use strict";i.d(t,{o:()=>l});var s=i(7577),a=i(7230),r=i(184),n=i(4222),o=i(6614);let l=()=>{let e=(0,s.useRef)(new Map),{mixingChannels:t,masterVolume:i,updateChannelVolume:l,setMasterVolume:c}=(0,r.U)(),d=(0,s.useCallback)(e=>(0,o.DA)(e.category,e.filename),[]),u=(0,s.useCallback)((t,s)=>{let r=d(s),n={howl:new a.Howl({src:[r],volume:t.volume*i*(t.isMuted?0:1),loop:!0,onload:()=>{let i=e.current.get(t.id);i&&(i.isLoading=!1,i.error=null)},onplay:()=>{let i=e.current.get(t.id);i&&(i.isPlaying=!0)},onpause:()=>{let i=e.current.get(t.id);i&&(i.isPlaying=!1)},onstop:()=>{let i=e.current.get(t.id);i&&(i.isPlaying=!1)},onloaderror:(i,s)=>{console.error(`音频加载失败 (${t.id}):`,s);let a=e.current.get(t.id);a&&(a.isLoading=!1,a.error="音频加载失败")},onplayerror:(i,s)=>{console.error(`音频播放失败 (${t.id}):`,s);let a=e.current.get(t.id);a&&(a.isPlaying=!1,a.error="音频播放失败")}}),isPlaying:!1,isLoading:!0,error:null};return e.current.set(t.id,n),n},[d,i]),m=(0,s.useCallback)(t=>{let i=e.current.get(t);!i||i.isLoading||i.error||i.howl.play()},[]),g=(0,s.useCallback)(t=>{let i=e.current.get(t);i&&i.howl.pause()},[]),h=(0,s.useCallback)(t=>{let i=e.current.get(t);i&&i.howl.stop()},[]),f=(0,s.useCallback)((s,a)=>{let r=e.current.get(s),n=t.find(e=>e.id===s);if(r&&n){let e=a*i*(n.isMuted?0:1);r.howl.volume(e),l(s,a)}},[t,i,l]),x=(0,s.useCallback)(t=>{let i=e.current.get(t);i&&i.howl.volume(0)},[]),p=(0,s.useCallback)(s=>{let a=e.current.get(s),r=t.find(e=>e.id===s);if(a&&r){let e=r.volume*i;a.howl.volume(e)}},[t,i]),y=(0,s.useCallback)(t=>{let i=e.current.get(t);return i?{isPlaying:i.isPlaying,isLoading:i.isLoading,error:i.error}:{isPlaying:!1,isLoading:!1,error:null}},[]),v=(0,s.useCallback)(()=>{e.current.forEach(e=>{e.howl.stop()})},[]),b=(0,s.useCallback)(i=>{c(i),e.current.forEach((e,s)=>{let a=t.find(e=>e.id===s);if(a){let t=a.volume*i*(a.isMuted?0:1);e.howl.volume(t)}})},[t,c]);return(0,s.useEffect)(()=>{let i=new Set(t.map(e=>e.id));new Set(e.current.keys()).forEach(t=>{if(!i.has(t)){let i=e.current.get(t);i&&(i.howl.unload(),e.current.delete(t))}}),t.forEach(t=>{if(!e.current.has(t.id)){let e=n.N.find(e=>e.id===t.soundId);e&&u(t,e)}})},[t,u]),(0,s.useEffect)(()=>()=>{e.current.forEach(e=>{e.howl.unload()}),e.current.clear()},[]),{playChannel:m,pauseChannel:g,stopChannel:h,setChannelVolume:f,muteChannel:x,unmuteChannel:p,getChannelState:y,stopAllChannels:v,setMasterVolume:b}}},7428:(e,t,i)=>{"use strict";i.d(t,{M:()=>r});var s=i(7577),a=i(184);function r(){let{timer:e,playState:t,setTimerActive:i,setTimerDuration:r,setTimerRemainingTime:n,updatePlayState:o}=(0,a.U)(),l=(0,s.useRef)(null),c=(0,s.useRef)(null),d=(0,s.useRef)(.7),u=(0,s.useCallback)(e=>{console.log("\uD83D\uDD50 启动睡眠定时器:",e,"分钟"),d.current=t.volume,r(e),n(60*e),i(!0),l.current&&clearInterval(l.current),l.current=setInterval(()=>{a.U.getState().setTimerRemainingTime(Math.max(0,a.U.getState().timer.remainingTime-1))},1e3)},[t.volume,r,n,i]),m=(0,s.useCallback)(()=>{console.log("⏹️ 停止睡眠定时器"),i(!1),n(0),l.current&&(clearInterval(l.current),l.current=null),c.current&&(clearInterval(c.current),c.current=null)},[i,n]),g=(0,s.useCallback)(()=>{l.current&&(clearInterval(l.current),l.current=null)},[]),h=(0,s.useCallback)(()=>{e.isActive&&e.remainingTime>0&&!l.current&&(l.current=setInterval(()=>{a.U.getState().setTimerRemainingTime(Math.max(0,a.U.getState().timer.remainingTime-1))},1e3))},[e.isActive,e.remainingTime]),f=(0,s.useCallback)(e=>{u(e)},[u]),x=(0,s.useCallback)(e=>{e>0&&e<=480&&u(e)},[u]);(0,s.useCallback)(()=>{console.log("\uD83D\uDD09 开始淡出效果");let t=e.fadeOutDuration,i=d.current/t,s=0;c.current=setInterval(()=>{s++;let a=Math.max(0,d.current-i*s);o({volume:a}),(s>=t||a<=0)&&(c.current&&(clearInterval(c.current),c.current=null),e.autoStop&&o({isPlaying:!1,isPaused:!1,volume:d.current}),m())},1e3)},[e.fadeOutDuration,e.autoStop,o,m]);let p=(0,s.useCallback)(e=>{let t=Math.floor(e/3600),i=Math.floor(e%3600/60),s=e%60;return t>0?`${t}:${i.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}`:`${i}:${s.toString().padStart(2,"0")}`},[]);return{isActive:e.isActive,duration:e.duration,remainingTime:e.remainingTime,fadeOutDuration:e.fadeOutDuration,autoStop:e.autoStop,startTimer:u,stopTimer:m,pauseTimer:g,resumeTimer:h,setPresetTimer:f,setCustomTimer:x,formatRemainingTime:p,progress:e.duration>0?(60*e.duration-e.remainingTime)/(60*e.duration):0,isNearEnd:e.remainingTime<=e.fadeOutDuration}}},184:(e,t,i)=>{"use strict";i.d(t,{U:()=>r});var s=i(551),a=i(5251);let r=(0,s.Ue)()((0,a.tJ)((e,t)=>({currentSound:null,playState:{isPlaying:!1,isPaused:!1,isLoading:!1,currentTime:0,duration:0,volume:.7,isLooping:!1},playerUI:{mode:"standard",isVisible:!1,position:"bottom",isMinimized:!1,showTimerPanel:!1,showMixingPanel:!1},mixingChannels:[],maxChannels:2,masterVolume:.8,timer:{duration:0,isActive:!1,remainingTime:0,fadeOutDuration:10,autoStop:!0},favorites:[],recentlyPlayed:[],userVolume:.7,setCurrentSound:i=>{console.log("\uD83C\uDFB5 设置当前音频:",{sound:i?.title,isVisible:t().playerUI.isVisible}),e({currentSound:i}),i&&(t().addToRecentlyPlayed(i.id),console.log("\uD83D\uDC41️ 显示播放器"),t().setPlayerVisible(!0))},updatePlayState:t=>{e(e=>({playState:{...e.playState,...t}}))},addMixingChannel:i=>{let{mixingChannels:s,maxChannels:a}=t();if(s.length>=a)return console.warn(`MVP版本最多支持${a}个音频同时播放`),!1;let r={id:`channel_${Date.now()}`,soundId:i.id,volume:.7,isMuted:!1,isActive:!0};return e(e=>({mixingChannels:[...e.mixingChannels,r]})),!0},removeMixingChannel:t=>{e(e=>({mixingChannels:e.mixingChannels.filter(e=>e.id!==t)}))},updateChannelVolume:(t,i)=>{e(e=>({mixingChannels:e.mixingChannels.map(e=>e.id===t?{...e,volume:i}:e)}))},setMasterVolume:t=>{e({masterVolume:t})},setTimer:t=>{e({timer:{duration:t,isActive:!0,remainingTime:60*t,fadeOutDuration:10,autoStop:!0}})},clearTimer:()=>{e(e=>({timer:{...e.timer,isActive:!1,remainingTime:0}}))},updateTimerRemaining:t=>{e(e=>({timer:{...e.timer,remainingTime:t}}))},setTimerActive:t=>{e(e=>({timer:{...e.timer,isActive:t}}))},setTimerDuration:t=>{e(e=>({timer:{...e.timer,duration:t}}))},setTimerRemainingTime:t=>{e(e=>({timer:{...e.timer,remainingTime:t}}))},addToFavorites:t=>{e(e=>({favorites:e.favorites.includes(t)?e.favorites:[...e.favorites,t]}))},removeFromFavorites:t=>{e(e=>({favorites:e.favorites.filter(e=>e!==t)}))},addToRecentlyPlayed:t=>{e(e=>{let i=e.recentlyPlayed.filter(e=>e!==t);return{recentlyPlayed:[t,...i].slice(0,20)}})},setUserVolume:t=>{e({userVolume:t})},setPlayerMode:t=>{e(e=>({playerUI:{...e.playerUI,mode:t}}))},setPlayerVisible:t=>{console.log("\uD83D\uDC41️ 设置播放器可见性:",t),e(e=>({playerUI:{...e.playerUI,isVisible:t}}))},setPlayerPosition:t=>{e(e=>({playerUI:{...e.playerUI,position:t}}))},togglePlayerMinimized:()=>{e(e=>({playerUI:{...e.playerUI,isMinimized:!e.playerUI.isMinimized}}))},setTimerPanelVisible:t=>{e(e=>({playerUI:{...e.playerUI,showTimerPanel:t}}))},setMixingPanelVisible:t=>{e(e=>({playerUI:{...e.playerUI,showMixingPanel:t}}))}}),{name:"noisesleep-audio-store",partialize:e=>({favorites:e.favorites,recentlyPlayed:e.recentlyPlayed,userVolume:e.userVolume,masterVolume:e.masterVolume,playerUI:e.playerUI})}))},1308:(e,t,i)=>{"use strict";function s(e,t,i){console.error("Error Boundary caught an error:",{error:e.message,stack:e.stack,componentStack:t.componentStack,locale:i,timestamp:new Date().toISOString(),userAgent:"SSR"})}function a(e,t){let i={en:{404:{title:"Page Not Found",message:"The page you are looking for does not exist.",action:"Go to Homepage"},500:{title:"Server Error",message:"Something went wrong on our end. Please try again later.",action:"Refresh Page"},network:{title:"Network Error",message:"Unable to connect to the server. Please check your internet connection.",action:"Try Again"}},zh:{404:{title:"页面未找到",message:"您访问的页面不存在。",action:"返回首页"},500:{title:"服务器错误",message:"服务器出现问题，请稍后重试。",action:"刷新页面"},network:{title:"网络错误",message:"无法连接到服务器，请检查您的网络连接。",action:"重试"}}};return i[e]?.[t]||i.en[t]}i.d(t,{Ic:()=>a,jG:()=>s})},1578:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>y,generateMetadata:()=>p,generateStaticParams:()=>x});var s=i(9510),a=i(3222),r=i.n(a),n=i(8391),o=i.n(n),l=i(3186),c=i(5031),d=i(8585),u=i(9163),m=i(8570);(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx#AudioPlayer`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#PlayButton`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#PrimaryPlayButton`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#SecondaryPlayButton`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#GhostPlayButton`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#LargePlayButton`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#SmallPlayButton`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx#VolumeControl`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx#ProgressBar`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx#StandardPlayer`);let g=(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx#AudioPlayerProvider`),h=(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx#ErrorBoundary`);(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx#withErrorBoundary`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx#useErrorHandler`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx#default`);let f=(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Analytics/AnalyticsProvider.tsx#AnalyticsProvider`);function x(){return u.D.locales.map(e=>({locale:e}))}async function p({params:{locale:e}}){let t=(await (0,c.Z)()).meta;return{title:t.title,description:t.description,keywords:t.keywords,verification:{other:{"msvalidate.01":"D4211744E59C0BE310A2D85C799EC3BF"}},openGraph:{title:t.title,description:t.description,url:`https://noisesleep.com${"en"===e?"":"/zh"}`,siteName:"NoiseSleep",locale:"zh"===e?"zh_CN":"en_US",type:"website"},twitter:{card:"summary_large_image",title:t.title,description:t.description},alternates:{canonical:`https://noisesleep.com${"en"===e?"":"/zh"}`,languages:{en:"https://noisesleep.com",zh:"https://noisesleep.com/zh"}}}}async function y({children:e,params:{locale:t}}){u.D.locales.includes(t)||(0,d.notFound)();let i=await (0,c.Z)();return(0,s.jsxs)("html",{lang:t,dir:"ltr",className:`${r().variable} ${o().variable}`,children:[(0,s.jsxs)("head",{children:[s.jsx("link",{rel:"alternate",hrefLang:"en",href:"https://noisesleep.com"}),s.jsx("link",{rel:"alternate",hrefLang:"zh",href:"https://noisesleep.com/zh"}),s.jsx("link",{rel:"alternate",hrefLang:"x-default",href:"https://noisesleep.com"}),s.jsx("link",{rel:"manifest",href:"/manifest.json"}),s.jsx("meta",{name:"theme-color",content:"#f59e0b"}),s.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),s.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),s.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),s.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),s.jsx("link",{rel:"dns-prefetch",href:"https://cdn.noisesleep.com"}),s.jsx("meta",{httpEquiv:"Content-Security-Policy",content:" default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' blob: https://cdn.noisesleep.com; connect-src 'self' https://cdn.noisesleep.com https://www.google-analytics.com https://www.googletagmanager.com; "})]}),s.jsx("body",{className:`
          ${"zh"===t?"font-noto-sans-sc":"font-inter"}
          antialiased
          bg-white dark:bg-gray-900
          text-gray-900 dark:text-gray-100
          transition-colors duration-300
        `,"data-locale":t,children:s.jsx(f,{children:s.jsx(l.Z,{messages:i,children:s.jsx(h,{locale:t,children:s.jsx(g,{children:e})})})})})]})}(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Analytics/AnalyticsProvider.tsx#trackUnifiedEvent`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Analytics/AnalyticsProvider.tsx#trackAudioPlayUnified`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Analytics/AnalyticsProvider.tsx#trackPageViewUnified`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Analytics/AnalyticsProvider.tsx#trackUserInteractionUnified`),(0,m.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Analytics/AnalyticsProvider.tsx#trackErrorUnified`)},7149:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s=(0,i(8570).createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx#default`)},2029:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o,metadata:()=>n});var s=i(9510),a=i(5317),r=i.n(a);i(5023);let n={metadataBase:new URL("https://noisesleep.com"),title:{template:"%s | Sleep Well - Science-Based Sleep Audio Platform",default:"Sleep Well - Science-Based Sleep Audio Platform"},description:"Discover high-quality sleep sounds, white noise, and nature audio to improve your sleep quality. Science-based audio platform with 80+ carefully curated sounds.",keywords:["sleep sounds","white noise","nature sounds","sleep music","relaxation","insomnia","sleep aid"],authors:[{name:"Sleep Well Team"}],creator:"Sleep Well",publisher:"Sleep Well",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:"https://noisesleep.com",siteName:"Sleep Well",title:"Sleep Well - Science-Based Sleep Audio Platform",description:"Discover high-quality sleep sounds, white noise, and nature audio to improve your sleep quality.",images:[{url:"/icon-192x192.png",width:192,height:192,alt:"Sleep Well Logo"}]},twitter:{card:"summary_large_image",title:"Sleep Well - Science-Based Sleep Audio Platform",description:"Discover high-quality sleep sounds, white noise, and nature audio to improve your sleep quality.",creator:"@noisesleep",images:["/icon-192x192.png"]},verification:{google:process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION},alternates:{canonical:"https://noisesleep.com",languages:{en:"https://noisesleep.com",zh:"https://noisesleep.com/zh"}}};function o({children:e}){return s.jsx("html",{children:s.jsx("body",{className:r().className,children:e})})}},2523:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s=(0,i(8570).createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx#default`)},4288:(e,t,i)=>{"use strict";i.d(t,{Z:()=>r});var s=i(107),a=i(9163);let r=(0,s.Z)(async({requestLocale:e})=>{let t=await e;return t&&a.D.locales.includes(t)||(console.warn(`Invalid locale: ${t}, using default locale: ${a.D.defaultLocale}`),t=a.D.defaultLocale),{locale:t,messages:(await i(1186)(`./${t}.json`)).default,timeZone:"zh"===t?"Asia/Shanghai":"America/New_York",now:new Date,formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"}},number:{precise:{maximumFractionDigits:2}}}}})},9163:(e,t,i)=>{"use strict";i.d(t,{D:()=>s});let s=(0,i(8952).R)({locales:["en","zh"],defaultLocale:"en",localePrefix:{mode:"as-needed",prefixes:{zh:"/zh"}},pathnames:{"/":"/","/about":"/about","/sounds":"/sounds","/sounds/[category]":{en:"/sounds/[category]",zh:"/sounds/[category]"},"/sounds/[category]/[sound]":{en:"/sounds/[category]/[sound]",zh:"/sounds/[category]/[sound]"},"/mix":"/mix","/favorites":"/favorites","/settings":"/settings"}})},5023:()=>{}};