{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|_vercel|favicon.ico|robots.txt|sitemap.xml|manifest.json|icon-.*\\.png|apple-.*\\.png|.*\\..*).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|_vercel|favicon.ico|robots.txt|sitemap.xml|manifest.json|icon-.*\\.png|apple-.*\\.png|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "txs0pWWwbwc7ni6WRUr19", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LZiuRitL88kIgt0w8jgFVwGphx5+hHqh5hiC3RPibnk=", "__NEXT_PREVIEW_MODE_ID": "cd187d30d1ddf475e63d4fb9fffd3586", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9e908eba398315fef71c063201510f0f95b167eaa2ebe3ff97305c8bc47d3ec8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "43161dcfa9b01dae7c9c791847e2032bba7d3bda53039b816c2bd431a4c90fb1"}}}, "functions": {}, "sortedMiddleware": ["/"]}