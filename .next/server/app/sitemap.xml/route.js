"use strict";(()=>{var e={};e.id=717,e.ids=[717],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9554:(e,a,t)=>{t.r(a),t.d(a,{originalPathname:()=>v,patchFetch:()=>R,requestAsyncStorage:()=>w,routeModule:()=>y,serverHooks:()=>$,staticGenerationAsyncStorage:()=>x});var n={};t.r(n),t.d(n,{default:()=>u});var i={};t.r(i),t.d(i,{GET:()=>g});var r=t(9303),s=t(8716),o=t(670),l=t(5661);let c=(0,t(4546).R)({locales:["en","zh"],defaultLocale:"en",localePrefix:{mode:"as-needed",prefixes:{zh:"/zh"}},pathnames:{"/":"/","/about":"/about","/sounds":"/sounds","/sounds/[category]":{en:"/sounds/[category]",zh:"/sounds/[category]"},"/sounds/[category]/[sound]":{en:"/sounds/[category]/[sound]",zh:"/sounds/[category]/[sound]"},"/mix":"/mix","/favorites":"/favorites","/settings":"/settings"}}),d=[{id:"light-rain",filename:"light-rain.mp3",duration:600,sleepScore:95.8,safetyLevel:"safe",noiseType:"white",category:"rain",title:{en:"Light Rain",zh:"轻雨声"},description:{en:"Gentle rainfall sounds with white noise characteristics, scientifically proven for sleep enhancement",zh:"温和的雨声，具有白噪音特征，科学证明有助于睡眠"},tags:["rain","gentle","white noise","sleep","relaxing","natural water"],scientificRating:95.8,userGroups:{adults:96,elderly:96,children:67,insomnia:100},regionalPreferences:{northAmerica:92,europe:88,eastAsia:96,china:98},technicalParams:{spectralSlope:.115,loudnessStability:.175,dynamicRange:14.7,tonalPeakRatio:6.76,effectPrediction:31.6},fileSize:58e5,format:"mp3",bitrate:128,sampleRate:44100,recommendedVolume:[45,60],createdAt:"2025-07-01T00:00:00Z",updatedAt:"2025-07-01T11:12:43Z"},{id:"heavy-rain",filename:"heavy-rain.mp3",duration:600,sleepScore:79.9,safetyLevel:"safe",noiseType:"pink",category:"rain",title:{en:"Heavy Rain",zh:"大雨声"},description:{en:"Intense rainfall with pink noise characteristics, excellent for masking environmental sounds",zh:"强烈的雨声，具有粉噪音特征，极佳的环境声音遮蔽效果"},tags:["rain","heavy","pink noise","masking","intense","natural water"],scientificRating:79.9,userGroups:{adults:96,elderly:100,children:56,insomnia:80},regionalPreferences:{northAmerica:88,europe:85,eastAsia:90,china:92},technicalParams:{spectralSlope:-.963,loudnessStability:.113,dynamicRange:7.1,tonalPeakRatio:278.28,effectPrediction:65.5},fileSize:62e5,format:"mp3",bitrate:128,sampleRate:44100,recommendedVolume:[45,60],createdAt:"2025-07-01T00:00:00Z",updatedAt:"2025-07-01T11:12:43Z"},{id:"rain-on-window",filename:"rain-on-window.mp3",duration:600,sleepScore:70.3,safetyLevel:"caution",noiseType:"complex",category:"rain",title:{en:"Rain on Window",zh:"雨打窗户"},description:{en:"Rhythmic rain drops on glass surface with complex noise patterns",zh:"雨滴打在玻璃表面的有节奏声音，具有复杂噪音模式"},tags:["rain","window","rhythmic","glass","complex","natural water","natural wind"],scientificRating:70.3,userGroups:{adults:70,elderly:70,children:49,insomnia:70},regionalPreferences:{northAmerica:85,europe:90,eastAsia:75,china:78},technicalParams:{spectralSlope:-1.631,loudnessStability:.131,dynamicRange:6.4,tonalPeakRatio:2252.63,effectPrediction:21.1},fileSize:59e5,format:"mp3",bitrate:128,sampleRate:44100,recommendedVolume:[45,60],createdAt:"2025-07-01T00:00:00Z",updatedAt:"2025-07-01T11:12:43Z"},{id:"thunder",filename:"thunder.mp3",duration:600,sleepScore:10.8,safetyLevel:"warning",noiseType:"deep-red",category:"rain",title:{en:"Thunder",zh:"雷声"},description:{en:"Thunder sounds with deep red noise characteristics - not recommended for sleep",zh:"雷声，具有深红噪音特征 - 不推荐用于睡眠"},tags:["thunder","storm","deep red noise","weather","natural water","low frequency"],scientificRating:10.8,userGroups:{adults:11,elderly:11,children:8,insomnia:11},regionalPreferences:{northAmerica:25,europe:20,eastAsia:15,china:12},technicalParams:{spectralSlope:-3.577,loudnessStability:1.052,dynamicRange:118.2,tonalPeakRatio:25340068,effectPrediction:4.9},fileSize:68e5,format:"mp3",bitrate:128,sampleRate:44100,recommendedVolume:[30,45],createdAt:"2025-07-01T00:00:00Z",updatedAt:"2025-07-01T11:12:43Z"}];function u(){let e="https://noisesleep.com",a=new Date,t=[];["","/sounds","/mixing","/favorites"].forEach(n=>{c.locales.forEach(i=>{let r="en"===i?`${e}${n}`:`${e}/${i}${n}`;t.push({url:r,lastModified:a,changeFrequency:""===n?"daily":"weekly",priority:""===n?1:.8,alternates:{languages:{en:`${e}${n}`,zh:`${e}/zh${n}`}}})})}),["rain","nature","noise","animals","things","transport","urban","places"].forEach(n=>{c.locales.forEach(i=>{let r="en"===i?`${e}/sounds/${n}`:`${e}/${i}/sounds/${n}`;t.push({url:r,lastModified:a,changeFrequency:"weekly",priority:.7,alternates:{languages:{en:`${e}/sounds/${n}`,zh:`${e}/zh/sounds/${n}`}}})})});try{d.forEach(n=>{c.locales.forEach(i=>{let r="en"===i?`${e}/sounds/${n.category}/${n.id}`:`${e}/${i}/sounds/${n.category}/${n.id}`;t.push({url:r,lastModified:a,changeFrequency:"monthly",priority:.6,alternates:{languages:{en:`${e}/sounds/${n.category}/${n.id}`,zh:`${e}/zh/sounds/${n.category}/${n.id}`}}})})})}catch(e){console.warn("无法加载音频数据库，跳过音频详情页面的sitemap生成")}return t}var p=t(707);let m={...n},h=m.default,f=m.generateSitemaps;if("function"!=typeof h)throw Error('Default export is missing in "/Users/<USER>/Documents/NoiseSleep/src/app/sitemap.ts"');async function g(e,a){let t;let{__metadata_id__:n,...i}=a.params||{},r=f?await f():null;if(r&&null==(t=r.find(e=>{let a=e.id.toString();return(a+=".xml")===n})?.id))return new l.NextResponse("Not Found",{status:404});let s=await h({id:t}),o=(0,p.resolveRouteData)(s,"sitemap");return new l.NextResponse(o,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let y=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Fsitemap.xml%2Froute&filePath=%2FUsers%2Fhej%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fsitemap.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"export",userland:i}),{requestAsyncStorage:w,staticGenerationAsyncStorage:x,serverHooks:$}=y,v="/sitemap.xml/route";function R(){return(0,o.patchFetch)({serverHooks:$,staticGenerationAsyncStorage:x})}},4546:(e,a,t)=>{var n=t(8037);a.R=n.default},8037:(e,a)=>{Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(e){return e}}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),n=a.X(0,[948,346],()=>t(9554));module.exports=n})();