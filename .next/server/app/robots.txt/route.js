"use strict";(()=>{var e={};e.id=703,e.ids=[703],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3859:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>x,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>c});var r={};o.r(r),o.d(r,{GET:()=>p});var s=o(9303),a=o(8716),n=o(670),i=o(5661),u=o(707);async function p(){let e=await {rules:[{userAgent:"*",allow:"/",disallow:["/api/","/admin/","/test-*","/debug/","/_next/","/private/","*.json","/audio-test","/test-audio","/test-player","/test-landing"]},{userAgent:"*",allow:"/sounds/"},{userAgent:"*",allow:["/favicon.ico","/manifest.json","/icon*.png"]}],sitemap:"https://noisesleep.com/sitemap.xml",host:"https://noisesleep.com"},t=(0,u.resolveRouteData)(e,"robots");return new i.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let l=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Frobots.txt%2Froute&filePath=%2FUsers%2Fhej%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Frobots.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"export",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:c,serverHooks:m}=l,x="/robots.txt/route";function g(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:c})}}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[948,346],()=>o(3859));module.exports=r})();