(()=>{var e={};e.id=409,e.ids=[409],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5588:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(5866),r(2523),r(2029);var s=r(3191),o=r(8716),n=r(7922),i=r.n(n),a=r(5231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],c=[],u="/_not-found/page",h={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1479:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},996:()=>{},2544:(e,t,r)=>{Promise.resolve().then(r.bind(r,3846))},5303:()=>{},3846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(326),o=r(1308),n=r(434),i=r(5047);function a(){let e=(0,i.usePathname)().startsWith("/zh")?"zh":"en",t=(0,o.Ic)(e,"404");return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-md w-full mx-auto text-center px-4",children:[s.jsx("div",{className:"w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600",children:s.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"})})}),s.jsx("h1",{className:"text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:"404"}),s.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4",children:t.title}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:t.message}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(n.default,{href:"zh"===e?"/zh":"/",className:"inline-block w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors",children:t.action}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[s.jsx(n.default,{href:"zh"===e?"/zh/sounds":"/sounds",className:"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"zh"===e?"浏览音频":"Browse Sounds"}),s.jsx(n.default,{href:"zh"===e?"/":"/zh",className:"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"zh"===e?"English":"中文版"})]})]}),(0,s.jsxs)("div",{className:"mt-8 text-sm text-gray-500 dark:text-gray-400",children:[s.jsx("p",{children:"zh"===e?"您可能在寻找：":"You might be looking for:"}),(0,s.jsxs)("div",{className:"mt-2 space-x-4",children:[s.jsx(n.default,{href:"zh"===e?"/zh/sounds/rain":"/sounds/rain",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"雨声":"Rain Sounds"}),s.jsx(n.default,{href:"zh"===e?"/zh/sounds/nature":"/sounds/nature",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"自然音":"Nature Sounds"}),s.jsx(n.default,{href:"zh"===e?"/zh/mixing":"/mixing",className:"text-indigo-500 hover:text-indigo-600",children:"zh"===e?"混音器":"Audio Mixer"})]})]})]})})}},1308:(e,t,r)=>{"use strict";function s(e,t,r){console.error("Error Boundary caught an error:",{error:e.message,stack:e.stack,componentStack:t.componentStack,locale:r,timestamp:new Date().toISOString(),userAgent:"SSR"})}function o(e,t){let r={en:{404:{title:"Page Not Found",message:"The page you are looking for does not exist.",action:"Go to Homepage"},500:{title:"Server Error",message:"Something went wrong on our end. Please try again later.",action:"Refresh Page"},network:{title:"Network Error",message:"Unable to connect to the server. Please check your internet connection.",action:"Try Again"}},zh:{404:{title:"页面未找到",message:"您访问的页面不存在。",action:"返回首页"},500:{title:"服务器错误",message:"服务器出现问题，请稍后重试。",action:"刷新页面"},network:{title:"网络错误",message:"无法连接到服务器，请检查您的网络连接。",action:"重试"}}};return r[e]?.[t]||r.en[t]}r.d(t,{Ic:()=>o,jG:()=>s})},5866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r(3370);let s=r(9510);r(1159);let o={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function n(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("title",{children:"404: This page could not be found."}),(0,s.jsx)("div",{style:o.error,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,s.jsx)("h1",{className:"next-error-h1",style:o.h1,children:"404"}),(0,s.jsx)("div",{style:o.desc,children:(0,s.jsx)("h2",{style:o.h2,children:"This page could not be found."})})]})})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>i});var s=r(9510),o=r(5317),n=r.n(o);r(5023);let i={metadataBase:new URL("https://noisesleep.com"),title:{template:"%s | Sleep Well - Science-Based Sleep Audio Platform",default:"Sleep Well - Science-Based Sleep Audio Platform"},description:"Discover high-quality sleep sounds, white noise, and nature audio to improve your sleep quality. Science-based audio platform with 80+ carefully curated sounds.",keywords:["sleep sounds","white noise","nature sounds","sleep music","relaxation","insomnia","sleep aid"],authors:[{name:"Sleep Well Team"}],creator:"Sleep Well",publisher:"Sleep Well",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:"https://noisesleep.com",siteName:"Sleep Well",title:"Sleep Well - Science-Based Sleep Audio Platform",description:"Discover high-quality sleep sounds, white noise, and nature audio to improve your sleep quality.",images:[{url:"/icon-192x192.png",width:192,height:192,alt:"Sleep Well Logo"}]},twitter:{card:"summary_large_image",title:"Sleep Well - Science-Based Sleep Audio Platform",description:"Discover high-quality sleep sounds, white noise, and nature audio to improve your sleep quality.",creator:"@noisesleep",images:["/icon-192x192.png"]},verification:{google:process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION},alternates:{canonical:"https://noisesleep.com",languages:{en:"https://noisesleep.com",zh:"https://noisesleep.com/zh"}}};function a({children:e}){return s.jsx("html",{children:s.jsx("body",{className:n().className,children:e})})}},2523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(8570).createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx#default`)},5023:()=>{},3370:(e,t,r)=>{"use strict";function s(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>s,_interop_require_default:()=>s})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[948,327],()=>r(5588));module.exports=s})();