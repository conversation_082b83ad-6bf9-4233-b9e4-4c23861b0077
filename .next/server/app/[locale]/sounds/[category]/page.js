"use strict";(()=>{var e={};e.id=91,e.ids=[91],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9609:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>g,pages:()=>l,routeModule:()=>p,tree:()=>c}),t(8191),t(1578),t(7149),t(2029),t(2523);var a=t(3191),s=t(8716),o=t(7922),n=t.n(o),i=t(5231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let c=["",{children:["[locale]",{children:["sounds",{children:["[category]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8191)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/sounds/[category]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1578)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,7149)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],l=["/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/sounds/[category]/page.tsx"],g="/[locale]/sounds/[category]/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/sounds/[category]/page",pathname:"/[locale]/sounds/[category]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8191:(e,r,t)=>{t.r(r),t.d(r,{default:()=>x,generateMetadata:()=>g,generateStaticParams:()=>l});var a=t(9510),s=t(8585),o=t(6442),n=t(5013),i=t(7681),d=t(2078),c=t(2443);async function l(){return Array.from(new Set(c.N.map(e=>e.category))).map(e=>({category:e.toLowerCase()}))}async function g({params:e}){let r=await (0,n.Z)({locale:e.locale,namespace:"metadata"}),t=decodeURIComponent(e.category);return{title:r("category.title",{category:t}),description:r("category.description",{category:t})}}function x({params:e}){let r=(0,o.Z)("sounds"),t=decodeURIComponent(e.category),n=c.N.filter(e=>e.category.toLowerCase()===t.toLowerCase());0===n.length&&(0,s.notFound)();let l={rain:{icon:"\uD83C\uDF27️",description:r("categoryDescriptions.rain")},nature:{icon:"\uD83C\uDF3F",description:r("categoryDescriptions.nature")},noise:{icon:"\uD83D\uDD0A",description:r("categoryDescriptions.noise")},animals:{icon:"\uD83D\uDC3E",description:r("categoryDescriptions.animals")},things:{icon:"\uD83C\uDFE0",description:r("categoryDescriptions.things")},transport:{icon:"\uD83D\uDE97",description:r("categoryDescriptions.transport")},urban:{icon:"\uD83C\uDFD9️",description:r("categoryDescriptions.urban")},places:{icon:"\uD83D\uDCCD",description:r("categoryDescriptions.places")}}[t.toLowerCase()]||{icon:"\uD83C\uDFB5",description:r("categoryDescriptions.default")};return a.jsx(d.Xg,{headerVariant:"default",showGetStarted:!1,children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-6",children:[a.jsx("a",{href:"zh"===e.locale?"/zh":"/",className:"hover:text-indigo-600 dark:hover:text-indigo-400",children:r("home")}),a.jsx("span",{children:"/"}),a.jsx("a",{href:"zh"===e.locale?"/zh/sounds":"/sounds",className:"hover:text-indigo-600 dark:hover:text-indigo-400",children:r("sounds")}),a.jsx("span",{children:"/"}),(0,a.jsxs)("span",{className:"text-gray-900 dark:text-gray-100 font-medium",children:[l.icon," ",t]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-indigo-100 to-purple-200 dark:from-indigo-900/20 dark:to-purple-800/20 rounded-2xl flex items-center justify-center",children:a.jsx("span",{className:"text-3xl",children:l.icon})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:[t," ",r("sounds")]}),(0,a.jsxs)("p",{className:"text-lg text-gray-600 dark:text-gray-400",children:[n.length," ",r("audioFiles")]})]})]}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 max-w-3xl",children:l.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[a.jsx("div",{className:"text-2xl font-bold text-indigo-600 dark:text-indigo-400",children:n.length}),a.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("totalSounds")})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[a.jsx("div",{className:"text-2xl font-bold text-indigo-600 dark:text-indigo-400",children:Math.round(n.reduce((e,r)=>e+(r.scientificRating||0),0)/n.length*10)/10}),a.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("avgRating")})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[a.jsx("div",{className:"text-2xl font-bold text-indigo-600 dark:text-indigo-400",children:Array.from(new Set(n.flatMap(e=>e.tags||[]))).length}),a.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("uniqueTags")})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[a.jsx("div",{className:"text-2xl font-bold text-indigo-600 dark:text-indigo-400",children:Math.round(n.reduce((e,r)=>e+(r.duration||0),0)/60)}),a.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("totalMinutes")})]})]}),a.jsx(i.F,{audios:n,variant:"detailed",showSearch:!0,showFilter:!1,showSort:!0,emptyMessage:r("noCategoryAudios",{category:t})})]})})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[948,327,308,350,78,323,575],()=>t(9609));module.exports=a})();