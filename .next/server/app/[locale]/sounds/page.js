"use strict";(()=>{var e={};e.id=141,e.ids=[141],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9505:(e,s,t)=>{t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>x,tree:()=>d}),t(1037),t(1578),t(7149),t(2029),t(2523);var a=t(3191),r=t(8716),o=t(7922),n=t.n(o),l=t(5231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(s,i);let d=["",{children:["[locale]",{children:["sounds",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1037)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/sounds/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1578)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,7149)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],u=["/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/sounds/page.tsx"],c="/[locale]/sounds/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/sounds/page",pathname:"/[locale]/sounds",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1037:(e,s,t)=>{t.r(s),t.d(s,{default:()=>u,generateMetadata:()=>d});var a=t(9510),r=t(6442),o=t(5013),n=t(7681),l=t(2078),i=t(2443);async function d({params:e}){let s=await (0,o.Z)({locale:e.locale,namespace:"metadata"});return{title:s("sounds.title"),description:s("sounds.description")}}function u({params:e,searchParams:s}){let t=(0,r.Z)("sounds"),{locale:o}=e,d=i.N,u=d.reduce((e,s)=>(e[s.category]=(e[s.category]||0)+1,e),{}),c=Object.keys(u).sort();return a.jsx(l.Xg,{headerVariant:"default",showGetStarted:!1,children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:t("title")}),a.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 mb-6",children:t("description")}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)("a",{href:"zh"===o?"/zh/sounds":"/sounds",className:"px-4 py-2 rounded-full text-sm font-medium transition-colors bg-indigo-500 text-white",children:[t("allCategories")," (",d.length,")"]}),c.map(e=>(0,a.jsxs)("a",{href:"zh"===o?`/zh/sounds/${encodeURIComponent(e)}`:`/sounds/${encodeURIComponent(e)}`,className:"px-4 py-2 rounded-full text-sm font-medium transition-colors bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600",children:[{rain:"\uD83C\uDF27️",nature:"\uD83C\uDF3F",noise:"\uD83D\uDD0A",animals:"\uD83D\uDC3E",things:"\uD83C\uDFE0",transport:"\uD83D\uDE97",urban:"\uD83C\uDFD9️",places:"\uD83D\uDCCD"}[e.toLowerCase()]||"\uD83C\uDFB5"," ",e," (",u[e],")"]},e))]})]}),a.jsx(n.F,{audios:d,variant:"default",showSearch:!0,showFilter:!0,showSort:!0,emptyMessage:t("noAudiosFound")})]})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[948,327,308,350,78,323,575],()=>t(9505));module.exports=a})();