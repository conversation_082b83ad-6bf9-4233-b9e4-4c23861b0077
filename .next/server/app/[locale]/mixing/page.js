(()=>{var e={};e.id=322,e.ids=[322],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2321:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>g,tree:()=>o}),s(4435),s(1578),s(7149),s(2029),s(2523);var t=s(3191),a=s(8716),i=s(7922),n=s.n(i),l=s(5231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(r,d);let o=["",{children:["[locale]",{children:["mixing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4435)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/mixing/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,1578)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,7149)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],c=["/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/mixing/page.tsx"],x="/[locale]/mixing/page",m={require:s,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/mixing/page",pathname:"/[locale]/mixing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},4780:(e,r,s)=>{Promise.resolve().then(s.bind(s,3170)),Promise.resolve().then(s.bind(s,3086)),Promise.resolve().then(s.bind(s,7408)),Promise.resolve().then(s.bind(s,3436))},3086:(e,r,s)=>{"use strict";s.d(r,{MixingBoard:()=>m});var t=s(326),a=s(7577),i=s(3844),n=s(1135),l=s(184),d=s(7408),o=s(465),c=s(18),x=s(4222);function m({className:e}){let r=(0,i.useTranslations)("mixing"),[s,m]=(0,a.useState)(!1),[g,h]=(0,a.useState)("all"),{mixingChannels:u,maxChannels:b,masterVolume:p,addMixingChannel:y,removeMixingChannel:j,updateChannelVolume:f,setMasterVolume:v}=(0,l.U)(),{playChannel:k,pauseChannel:N,stopChannel:w,setChannelVolume:D,muteChannel:C,unmuteChannel:P,getChannelState:M,stopAllChannels:S,setMasterVolume:T}=(0,c.o)(),_=x.N.filter(e=>!u.some(r=>r.soundId===e.id)),A="all"===g?_:_.filter(e=>e.category===g),B=Array.from(new Set(x.N.map(e=>e.category))).sort(),L=(0,a.useCallback)(e=>{y(e)&&m(!1)},[y]),U=(0,a.useCallback)(e=>{j(e)},[j]),E=(0,a.useCallback)((e,r)=>{f(e,r),D(e,r)},[f,D]),F=(0,a.useCallback)(e=>{v(e),T(e)},[v,T]),V=(0,a.useCallback)(e=>{k(e)},[k]),Z=(0,a.useCallback)(e=>{N(e)},[N]),q=(0,a.useCallback)(e=>{w(e)},[w]),z=(0,a.useCallback)((e,r)=>{r?C(e):P(e)},[C,P]),W=e=>x.N.find(r=>r.id===e),G=e=>({rain:"\uD83C\uDF27️",nature:"\uD83C\uDF3F",noise:"\uD83D\uDD0A",animals:"\uD83D\uDC3E",things:"\uD83C\uDFE0",transport:"\uD83D\uDE97",urban:"\uD83C\uDFD9️",places:"\uD83D\uDCCD"})[e.toLowerCase()]||"\uD83C\uDFB5";return(0,t.jsxs)("div",{className:(0,n.W)("space-y-6",e),children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:r("title")}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:r("description",{max:b})})]}),(0,t.jsxs)("button",{onClick:()=>m(!0),disabled:u.length>=b,className:(0,n.W)("inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2",u.length>=b?"bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500":"bg-amber-500 text-white hover:bg-amber-600 shadow-sm hover:shadow-md"),children:[t.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),r("addAudio")]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:r("masterVolume")}),(0,t.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(100*p),"%"]})]}),t.jsx(o.VolumeControl,{volume:p,onVolumeChange:F,showIcon:!0,showValue:!1,size:"md"})]})]}),t.jsx("div",{className:"space-y-4",children:0===u.length?(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700 p-12 text-center",children:[t.jsx("div",{className:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",children:t.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{d:"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"})})}),t.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:r("noChannels")}),t.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:r("addFirstAudio")}),(0,t.jsxs)("button",{onClick:()=>m(!0),className:"inline-flex items-center gap-2 px-4 py-2 bg-amber-500 text-white rounded-lg font-medium hover:bg-amber-600 transition-colors",children:[t.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),r("addAudio")]})]}):t.jsx("div",{className:"grid gap-4 md:grid-cols-2",children:u.map(e=>{let r=W(e.soundId);if(!r)return null;let s=M(e.id);return t.jsx(d.MixingChannel,{channel:e,audio:r,isPlaying:s.isPlaying,isLoading:s.isLoading,onPlay:()=>V(e.id),onPause:()=>Z(e.id),onStop:()=>q(e.id),onVolumeChange:r=>E(e.id,r),onMute:()=>z(e.id,!e.isMuted),onRemove:()=>U(e.id)},e.id)})})}),s&&t.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0",children:[t.jsx("div",{className:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75",onClick:()=>m(!1)}),(0,t.jsxs)("div",{className:"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:r("selectAudio")}),t.jsx("button",{onClick:()=>m(!1),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:t.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),t.jsx("div",{className:"mb-4",children:(0,t.jsxs)("select",{value:g,onChange:e=>h(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-amber-500",children:[t.jsx("option",{value:"all",children:r("allCategories")}),B.map(e=>(0,t.jsxs)("option",{value:e,children:[G(e)," ",e]},e))]})}),t.jsx("div",{className:"max-h-96 overflow-y-auto",children:0===A.length?t.jsx("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:r("noAvailableAudios")}):t.jsx("div",{className:"grid gap-3 sm:grid-cols-2",children:A.map(e=>(0,t.jsxs)("button",{onClick:()=>L(e),className:"flex items-center gap-3 p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:border-amber-300 dark:hover:border-amber-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[t.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center flex-shrink-0",children:t.jsx("span",{className:"text-lg",children:G(e.category)})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[t.jsx("h4",{className:"font-medium text-gray-900 dark:text-gray-100 truncate",children:e.title.en}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:e.category})]}),e.scientificRating&&(0,t.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["⭐ ",e.scientificRating.toFixed(1)]})]},e.id))})})]})]})})]})}},1506:(e,r,s)=>{"use strict";s.d(r,{Z:()=>i});var t=s(1159),a=s(9243);function i(e){return function(e,r){try{return(0,t.use)(r)}catch(r){throw r instanceof TypeError&&r.message.includes("Cannot read properties of null (reading 'use')")?Error("`".concat(e,"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components"),{cause:r}):r}}(e,(0,a.Z)())}},6442:(e,r,s)=>{"use strict";s.d(r,{Z:()=>l});var t=s(1159),a=s(7872),i=(0,t.cache)(function(e,r){return(0,a.eX)({...e,namespace:r})}),n=s(1506);function l(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];let[t]=r;return i((0,n.Z)("useTranslations"),t)}},5013:(e,r,s)=>{"use strict";s.d(r,{Z:()=>n});var t=s(1159),a=s(7872),i=s(9243),n=(0,t.cache)(async function(e){let r,s;"string"==typeof e?r=e:e&&(s=e.locale,r=e.namespace);let t=await (0,i.Z)(s);return(0,a.eX)({...t,namespace:r,messages:t.messages})})},4435:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o,generateMetadata:()=>d});var t=s(9510),a=s(6442),i=s(5013),n=s(8570);let l=(0,n.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/MixingBoard/MixingBoard.tsx#MixingBoard`);async function d({params:e}){let r=await (0,i.Z)({locale:e.locale,namespace:"metadata"});return{title:r("mixing.title"),description:r("mixing.description")}}function o({params:e}){let r=(0,a.Z)("mixing");return t.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:r("pageTitle")}),t.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 mb-6",children:r("pageDescription")}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[t.jsx("div",{className:"w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center",children:t.jsx("span",{className:"text-lg",children:"\uD83C\uDF9B️"})}),t.jsx("h3",{className:"font-semibold text-gray-900 dark:text-gray-100",children:r("feature1Title")})]}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("feature1Description")})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[t.jsx("div",{className:"w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center",children:t.jsx("span",{className:"text-lg",children:"\uD83D\uDD0A"})}),t.jsx("h3",{className:"font-semibold text-gray-900 dark:text-gray-100",children:r("feature2Title")})]}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("feature2Description")})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[t.jsx("div",{className:"w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center",children:t.jsx("span",{className:"text-lg",children:"⚡"})}),t.jsx("h3",{className:"font-semibold text-gray-900 dark:text-gray-100",children:r("feature3Title")})]}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("feature3Description")})]})]}),t.jsx("div",{className:"bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-8",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[t.jsx("div",{className:"w-6 h-6 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5",children:t.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium text-amber-800 dark:text-amber-200 mb-1",children:r("mvpNoticeTitle")}),t.jsx("p",{className:"text-sm text-amber-700 dark:text-amber-300",children:r("mvpNoticeDescription")})]})]})})]}),t.jsx(l,{}),(0,t.jsxs)("div",{className:"mt-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4",children:r("howToUseTitle")}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[t.jsx("div",{className:"w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5",children:"1"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-1",children:r("step1Title")}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("step1Description")})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[t.jsx("div",{className:"w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5",children:"2"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-1",children:r("step2Title")}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("step2Description")})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[t.jsx("div",{className:"w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5",children:"3"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-1",children:r("step3Title")}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("step3Description")})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[t.jsx("div",{className:"w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5",children:"4"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-1",children:r("step4Title")}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("step4Description")})]})]})]})]})]})})}(0,n.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/MixingBoard/MixingChannel.tsx#MixingChannel`),(0,n.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/MixingBoard/MixingPanel.tsx#MixingPanel`)}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[948,327,308,350],()=>s(2321));module.exports=t})();