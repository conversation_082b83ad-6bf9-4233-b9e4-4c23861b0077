(()=>{var e={};e.id=565,e.ids=[565],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1266:(e,s,l)=>{"use strict";l.r(s),l.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o}),l(450),l(1578),l(7149),l(2029),l(2523);var a=l(3191),t=l(8716),r=l(7922),i=l.n(r),n=l(5231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);l.d(s,d);let o=["",{children:["[locale]",{children:["audio-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,450)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,1578)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx"],"not-found":[()=>Promise.resolve().then(l.bind(l,7149)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(l.bind(l,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(l.bind(l,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],c=["/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx"],m="/[locale]/audio-test/page",x={require:l,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/[locale]/audio-test/page",pathname:"/[locale]/audio-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2040:(e,s,l)=>{Promise.resolve().then(l.bind(l,5826))},5826:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>n});var a=l(326),t=l(7577),r=l(4222),i=l(7768);function n(){let[e,s]=(0,t.useState)(r.N[0]),{isLoading:l,isPlaying:n,currentTime:d,duration:o,volume:c,error:m,play:x,pause:p,stop:h,setVolume:u}=(0,i.x)(),g=e=>`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`;return a.jsx("div",{className:"min-h-screen bg-gray-100 p-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8 text-center",children:"音频播放测试"}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"选择音频文件"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:r.N.map(l=>(0,a.jsxs)("button",{onClick:()=>s(l),className:`p-4 rounded-lg border-2 text-left transition-colors ${e?.id===l.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:[a.jsx("div",{className:"font-medium",children:l.title.zh}),a.jsx("div",{className:"text-sm text-gray-600",children:l.category}),a.jsx("div",{className:"text-xs text-gray-500",children:l.filename})]},l.id))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"播放器控制"}),e&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("h3",{className:"font-medium",children:["当前选择: ",e.title.zh]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["文件路径: /Sounds/",e.category.charAt(0).toUpperCase()+e.category.slice(1),"/",e.filename]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[a.jsx("button",{onClick:()=>{e&&x(e)},disabled:l||!e,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300",children:l?"加载中...":"播放"}),a.jsx("button",{onClick:p,disabled:!n,className:"px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 disabled:bg-gray-300",children:"暂停"}),a.jsx("button",{onClick:h,className:"px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600",children:"停止"})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium mb-2",children:["音量: ",Math.round(100*c),"%"]}),a.jsx("input",{type:"range",min:"0",max:"1",step:"0.1",value:c,onChange:e=>u(parseFloat(e.target.value)),className:"w-full"})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[a.jsx("span",{children:g(d)}),a.jsx("span",{children:g(o)})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all",style:{width:o>0?`${d/o*100}%`:"0%"}})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"状态信息"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"加载状态:"}),a.jsx("span",{className:`ml-2 px-2 py-1 rounded ${l?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:l?"加载中":"已就绪"})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"播放状态:"}),a.jsx("span",{className:`ml-2 px-2 py-1 rounded ${n?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:n?"播放中":"已停止"})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"当前时间:"}),a.jsx("span",{className:"ml-2",children:g(d)})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"总时长:"}),a.jsx("span",{className:"ml-2",children:g(o)})]})]}),m&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-red-100 border border-red-300 rounded-lg",children:[a.jsx("h3",{className:"font-medium text-red-800 mb-2",children:"错误信息:"}),a.jsx("p",{className:"text-red-700",children:m})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mt-6",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"文件系统验证"}),(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("p",{className:"mb-2",children:"实际音频文件列表 (/Sounds/):"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-gray-600",children:[a.jsx("li",{children:"Rain/heavy-rain.mp3, light-rain.mp3, rain-on-car-roof.mp3, etc."}),a.jsx("li",{children:"Nature/campfire.mp3, droplets.mp3, river.mp3, waves.mp3, etc."}),a.jsx("li",{children:"Noise/brown-noise.wav, pink-noise.wav, white-noise.wav"}),a.jsx("li",{children:"Animals/beehive.mp3, birds.mp3, cat-purring.mp3, etc."}),a.jsx("li",{children:"Things/boiling-water.mp3, bubbles.mp3, clock.mp3, etc."}),a.jsx("li",{children:"Transport/airplane.mp3, train.mp3, sailboat.mp3, etc."}),a.jsx("li",{children:"Urban/busy-street.mp3, traffic.mp3, highway.mp3, etc."}),a.jsx("li",{children:"Places/airport.mp3, cafe.mp3, church.mp3, etc."})]})]})]})]})})}},450:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>a});let a=(0,l(8570).createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var l=e=>s(s.s=e),a=s.X(0,[948,327,308,350],()=>l(1266));module.exports=a})();