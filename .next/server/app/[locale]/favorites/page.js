(()=>{var e={};e.id=667,e.ids=[667],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3350:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>g,pages:()=>d,routeModule:()=>x,tree:()=>c}),a(8064),a(1578),a(7149),a(2029),a(2523);var s=a(3191),t=a(8716),l=a(7922),o=a.n(l),i=a(5231),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);a.d(r,n);let c=["",{children:["[locale]",{children:["favorites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,8064)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/favorites/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,1578)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,7149)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],d=["/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/favorites/page.tsx"],g="/[locale]/favorites/page",u={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/[locale]/favorites/page",pathname:"/[locale]/favorites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7061:(e,r,a)=>{Promise.resolve().then(a.bind(a,312))},312:(e,r,a)=>{"use strict";a.d(r,{FavoritesContent:()=>c});var s=a(326),t=a(3844);a(4323);var l=a(1182);a(9291),a(1545);var o=a(9910),i=a(184),n=a(4222);function c({params:e}){let r=(0,t.useTranslations)("favorites"),{favorites:a}=(0,i.U)(),c=n.N.filter(e=>a.includes(e.id));return s.jsx(o.default,{headerVariant:"default",showGetStarted:!1,children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:r("title")}),s.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 mb-6",children:r("description")}),s.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("svg",{className:"w-5 h-5 text-red-500",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})}),s.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("totalFavorites",{count:c.length})})]}),c.length>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("svg",{className:"w-5 h-5 text-amber-500",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})}),s.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:r("averageRating",{rating:(c.reduce((e,r)=>e+(r.scientificRating||0),0)/c.length).toFixed(1)})})]})]})})]}),c.length>0?s.jsx(l.AudioGrid,{audios:c,variant:"default",showSearch:!0,showFilter:!0,showSort:!0,emptyMessage:r("noFavorites")}):(0,s.jsxs)("div",{className:"text-center py-16",children:[s.jsx("div",{className:"w-24 h-24 mx-auto mb-6 text-gray-300 dark:text-gray-600",children:s.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),s.jsx("h3",{className:"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2",children:r("emptyTitle")}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto",children:r("emptyDescription")}),(0,s.jsxs)("a",{href:"zh"===e.locale?"/zh/sounds":"/sounds",className:"inline-flex items-center gap-2 px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors",children:[s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"})}),r("browseAudio")]})]})]})})}},1182:(e,r,a)=>{"use strict";a.d(r,{AudioGrid:()=>n});var s=a(326),t=a(7577),l=a(3844),o=a(1135),i=a(4323);function n({audios:e,variant:r="default",columns:a="auto",showSearch:n=!0,showFilter:c=!0,showSort:d=!0,onAudioPlay:g,className:u,emptyMessage:x,loading:m=!1}){let h=(0,l.useTranslations)("common"),[p,v]=(0,t.useState)(""),[f,y]=(0,t.useState)("all"),[j,b]=(0,t.useState)("name"),w=(0,t.useMemo)(()=>Array.from(new Set(e.map(e=>e.category))).sort(),[e]),N=(0,t.useMemo)(()=>{let r=e;if(p.trim()){let e=p.toLowerCase().trim();r=r.filter(r=>Object.values(r.title).some(r=>r.toLowerCase().includes(e))||r.description&&Object.values(r.description).some(r=>r.toLowerCase().includes(e))||r.category.toLowerCase().includes(e)||r.tags?.some(r=>r.toLowerCase().includes(e)))}return"all"!==f&&(r=r.filter(e=>e.category===f)),r.sort((e,r)=>{switch(j){case"name":return(e.title.en||e.title.zh||"").localeCompare(r.title.en||r.title.zh||"");case"category":return e.category.localeCompare(r.category);case"rating":return(r.scientificRating||0)-(e.scientificRating||0);case"recent":return e.id.localeCompare(r.id);default:return 0}}),r},[e,p,f,j]),k=()=>{if("auto"===a)switch(r){case"compact":return"grid-cols-1";case"detailed":return"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";default:return"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"}return({1:"grid-cols-1",2:"grid-cols-1 sm:grid-cols-2",3:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"})[a]};return m?(0,s.jsxs)("div",{className:(0,o.W)("space-y-4",u),children:[(n||c||d)&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[n&&s.jsx("div",{className:"flex-1",children:s.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})}),c&&s.jsx("div",{className:"w-full sm:w-48",children:s.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})}),d&&s.jsx("div",{className:"w-full sm:w-48",children:s.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})})]}),s.jsx("div",{className:(0,o.W)("grid gap-4",k()),children:Array.from({length:8}).map((e,r)=>s.jsx("div",{className:"bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse",children:s.jsx("div",{className:"h-48"})},r))})]}):(0,s.jsxs)("div",{className:(0,o.W)("space-y-6",u),children:[(n||c||d)&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[n&&s.jsx("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),s.jsx("input",{type:"text",placeholder:h("searchAudios"),value:p,onChange:e=>v(e.target.value),className:(0,o.W)("block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","placeholder-gray-500 dark:placeholder-gray-400","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200")})]})}),c&&s.jsx("div",{className:"w-full sm:w-48",children:(0,s.jsxs)("select",{value:f,onChange:e=>y(e.target.value),className:(0,o.W)("block w-full px-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200"),children:[s.jsx("option",{value:"all",children:h("allCategories")}),w.map(e=>s.jsx("option",{value:e,children:e},e))]})}),d&&s.jsx("div",{className:"w-full sm:w-48",children:(0,s.jsxs)("select",{value:j,onChange:e=>b(e.target.value),className:(0,o.W)("block w-full px-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200"),children:[s.jsx("option",{value:"name",children:h("sortByName")}),s.jsx("option",{value:"category",children:h("sortByCategory")}),s.jsx("option",{value:"rating",children:h("sortByRating")}),s.jsx("option",{value:"recent",children:h("sortByRecent")})]})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400",children:[s.jsx("span",{children:h("showingResults",{count:N.length,total:e.length})}),p&&s.jsx("button",{onClick:()=>v(""),className:"text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300",children:h("clearSearch")})]}),N.length>0?s.jsx("div",{className:(0,o.W)("grid gap-4",k()),children:N.map(e=>s.jsx(i.AudioCard,{audio:e,variant:r,showCategory:"all"===f,showTags:!0,showDuration:"detailed"===r,showDescription:"detailed"===r,onPlay:g},e.id))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("div",{className:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",children:s.jsx("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"})})}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:x||h("noAudiosFound")}),s.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:h(p?"tryDifferentSearch":"noAudiosAvailable")}),p&&s.jsx("button",{onClick:()=>v(""),className:(0,o.W)("inline-flex items-center px-4 py-2 border border-transparent","text-sm font-medium rounded-md text-white bg-amber-600","hover:bg-amber-700 focus:outline-none focus:ring-2","focus:ring-offset-2 focus:ring-amber-500 transition-colors"),children:h("clearSearch")})]})]})}},5013:(e,r,a)=>{"use strict";a.d(r,{Z:()=>o});var s=a(1159),t=a(7872),l=a(9243),o=(0,s.cache)(async function(e){let r,a;"string"==typeof e?r=e:e&&(a=e.locale,r=e.namespace);let s=await (0,l.Z)(a);return(0,t.eX)({...s,namespace:r,messages:s.messages})})},8064:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>i,generateMetadata:()=>o});var s=a(9510),t=a(5013);let l=(0,a(8570).createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/favorites/FavoritesContent.tsx#FavoritesContent`);async function o({params:e}){let r=await (0,t.Z)({locale:e.locale,namespace:"metadata"});return{title:r("favorites.title"),description:r("favorites.description")}}function i({params:e}){return s.jsx(l,{params:e})}}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[948,327,308,350,78,323],()=>a(3350));module.exports=s})();