(()=>{var e={};e.id=61,e.ids=[61],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5579:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(4320),s(1578),s(7149),s(2029),s(2523);var r=s(3191),i=s(8716),a=s(7922),n=s.n(a),l=s(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4320)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,1578)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,7149)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],d=["/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx"],x="/[locale]/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2832:(e,t,s)=>{Promise.resolve().then(s.bind(s,3170)),Promise.resolve().then(s.bind(s,1201)),Promise.resolve().then(s.bind(s,7084)),Promise.resolve().then(s.bind(s,28)),Promise.resolve().then(s.bind(s,1545)),Promise.resolve().then(s.bind(s,9291)),Promise.resolve().then(s.bind(s,9910))},7084:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(326),i=s(3844),a=s(434);function n({className:e=""}){let t=(0,i.useTranslations)("landing"),s=(0,i.useLocale)();return r.jsx("section",{className:`py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-indigo-600 to-purple-600 ${e}`,children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[r.jsx("h3",{className:"text-4xl font-light text-white mb-6",children:t("cta.title")}),r.jsx("p",{className:"text-xl text-indigo-100 mb-8 max-w-2xl mx-auto",children:t("cta.description")}),r.jsx(a.default,{href:`/${s}/sounds`,className:"inline-block bg-white text-indigo-600 px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-50 transition-all transform hover:scale-105",children:t("buttons.startFree")})]})})}},1201:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(326),i=s(3844),a=s(7577);function n({icon:e,type:t,name:s,description:n,className:l=""}){let o=(0,i.useTranslations)("landing"),[c,d]=(0,a.useState)(!1);return(0,r.jsxs)("div",{className:`bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100 ${l}`,children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[r.jsx("div",{className:"text-4xl",children:e}),r.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{noise:"bg-blue-100 text-blue-800",things:"bg-gray-100 text-gray-800",transport:"bg-yellow-100 text-yellow-800",places:"bg-purple-100 text-purple-800",urban:"bg-orange-100 text-orange-800",animals:"bg-green-100 text-green-800",rain:"bg-cyan-100 text-cyan-800",nature:"bg-emerald-100 text-emerald-800"}[t]||"bg-gray-100 text-gray-800"}`,children:o(`typeLabels.${t}`)})]}),r.jsx("h4",{className:"text-xl font-medium text-gray-800 mb-2",children:s}),r.jsx("p",{className:"text-gray-600 mb-4",children:n}),r.jsx("button",{onClick:()=>{d(!c),c||setTimeout(()=>{d(!1)},3e3)},className:`w-full py-3 rounded-full transition-all ${c?"bg-indigo-600 text-white":"bg-gray-100 text-gray-700 hover:bg-indigo-100"}`,children:o(c?"buttons.pause":"buttons.play")})]})}},28:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(326),i=s(3844),a=s(434);function n({className:e=""}){let t=(0,i.useTranslations)("landing"),s=(0,i.useLocale)();return r.jsx("section",{className:`py-20 px-4 sm:px-6 lg:px-8 ${e}`,children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[r.jsx("h2",{className:"text-5xl md:text-6xl font-light text-gray-800 mb-6 leading-tight",children:"zh"===s?(0,r.jsxs)(r.Fragment,{children:["让",r.jsx("span",{className:"text-indigo-600",children:t("hero.titleHighlight")}),r.jsx("br",{}),"陪伴你的美梦"]}):(0,r.jsxs)(r.Fragment,{children:["Let"," ",r.jsx("span",{className:"text-indigo-600",children:t("hero.titleHighlight")}),r.jsx("br",{}),"Accompany Your Sweet Dreams"]})}),r.jsx("p",{className:"text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed",children:t("hero.description")}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[r.jsx(a.default,{href:`/${s}/sounds`,className:"inline-block bg-indigo-600 text-white px-8 py-4 rounded-full text-lg hover:bg-indigo-700 transition-all transform hover:scale-105",children:t("buttons.experience")}),r.jsx("button",{className:"border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-full text-lg hover:bg-indigo-50 transition-colors",children:t("buttons.learnMore")})]})]})})}},1506:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var r=s(1159),i=s(9243);function a(e){return function(e,t){try{return(0,r.use)(t)}catch(t){throw t instanceof TypeError&&t.message.includes("Cannot read properties of null (reading 'use')")?Error("`".concat(e,"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components"),{cause:t}):t}}(e,(0,i.Z)())}},6442:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var r=s(1159),i=s(7872),a=(0,r.cache)(function(e,t){return(0,i.eX)({...e,namespace:t})}),n=s(1506);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];let[r]=t;return a((0,n.Z)("useTranslations"),r)}},4320:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(9510),i=s(6442),a=s(1506),n=s(55),l=s(2078),o=s(8570);let c=(0,o.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx#default`),d=(0,o.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx#default`);function x({icon:e,title:t,description:s,bgColor:i="bg-indigo-100",className:a=""}){return(0,r.jsxs)("div",{className:`text-center ${a}`,children:[r.jsx("div",{className:`w-16 h-16 ${i} rounded-full flex items-center justify-center mx-auto mb-4`,children:r.jsx("span",{className:"text-2xl",children:e})}),r.jsx("h4",{className:"text-xl font-medium text-gray-800 mb-2",children:t}),r.jsx("p",{className:"text-gray-600",children:s})]})}function u({post:e,className:t="",onClick:s}){return(0,r.jsxs)("article",{className:`bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all cursor-pointer ${t}`,onClick:s,children:[r.jsx("h4",{className:"text-xl font-medium text-gray-800 mb-3 hover:text-indigo-600 transition-colors",children:e.title}),r.jsx("p",{className:"text-gray-600 mb-4 leading-relaxed",children:e.excerpt}),(0,r.jsxs)("div",{className:"flex justify-between items-center text-sm text-gray-500",children:[r.jsx("span",{children:e.date}),r.jsx("span",{children:e.readTime})]})]})}let p=(0,o.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/CTASection/CTASection.tsx#default`);function m({params:{locale:e}}){(0,n.t)(e);let t=(0,i.Z)("landing");return function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];(0,a.Z)("useLocale").locale}(),(0,r.jsxs)(l.Xg,{headerVariant:"landing",children:[r.jsx(c,{}),r.jsx("section",{id:"sounds",className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[r.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:t("categories.title")}),r.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:t("categories.subtitle")})]}),r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6",children:[{icon:"\uD83C\uDF27️",type:"rain"},{icon:"\uD83C\uDF0A",type:"nature"},{icon:"\uD83C\uDF32",type:"nature"},{icon:"☕",type:"places"},{icon:"\uD83D\uDD25",type:"nature"},{icon:"\uD83D\uDCA8",type:"nature"}].map((e,s)=>r.jsx(d,{icon:e.icon,type:e.type,name:t(`categories.items.${e.type}.name`),description:t(`categories.items.${e.type}.description`)},s))})]})}),r.jsx("section",{id:"features",className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[r.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:t("features.title")}),r.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:t("features.subtitle")})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[r.jsx(x,{icon:"\uD83E\uDDE0",title:t("features.scientific.title"),description:t("features.scientific.description")}),r.jsx(x,{icon:"\uD83C\uDF9A️",title:t("features.customizable.title"),description:t("features.customizable.description")}),r.jsx(x,{icon:"\uD83D\uDCF1",title:t("features.responsive.title"),description:t("features.responsive.description")}),r.jsx(x,{icon:"\uD83C\uDF19",title:t("features.sleepMode.title"),description:t("features.sleepMode.description")}),r.jsx(x,{icon:"\uD83C\uDFAF",title:t("features.focus.title"),description:t("features.focus.description")}),r.jsx(x,{icon:"\uD83C\uDF0D",title:t("features.multilingual.title"),description:t("features.multilingual.description")})]})]})}),r.jsx("section",{id:"blog",className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[r.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:t("blog.title")}),r.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:t("blog.subtitle")})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[r.jsx(u,{post:{title:t("blog.posts.sleepScience.title"),excerpt:t("blog.posts.sleepScience.excerpt"),date:t("blog.posts.sleepScience.date"),readTime:t("blog.posts.sleepScience.readTime")}}),r.jsx(u,{post:{title:t("blog.posts.whiteNoise.title"),excerpt:t("blog.posts.whiteNoise.excerpt"),date:t("blog.posts.whiteNoise.date"),readTime:t("blog.posts.whiteNoise.readTime")}}),r.jsx(u,{post:{title:t("blog.posts.productivity.title"),excerpt:t("blog.posts.productivity.excerpt"),date:t("blog.posts.productivity.date"),readTime:t("blog.posts.productivity.readTime")}})]})]})}),r.jsx(p,{})]})}},2078:(e,t,s)=>{"use strict";s.d(t,{Xg:()=>i});var r=s(8570);(0,r.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx#default`),(0,r.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx#default`);let i=(0,r.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/PageLayout.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[948,327,308,350,78],()=>s(5579));module.exports=r})();