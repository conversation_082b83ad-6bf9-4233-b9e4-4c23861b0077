(()=>{var e={};e.id=35,e.ids=[35],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},547:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(2310),t(1578),t(7149),t(2029),t(2523);var a=t(3191),r=t(8716),i=t(7922),n=t.n(i),o=t(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d=["",{children:["[locale]",{children:["test-player",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2310)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1578)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,7149)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],c=["/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx"],m="/[locale]/test-player/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/test-player/page",pathname:"/[locale]/test-player",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5606:(e,s,t)=>{Promise.resolve().then(t.bind(t,7945))},7945:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(326),r=t(3844),i=t(4323),n=t(184),o=t(7768);function l(){let{currentSound:e,playState:s,playerUI:t}=(0,n.U)(),{isPlaying:r,isLoading:i,currentTime:l,duration:d}=(0,o.x)();return(0,a.jsxs)("div",{className:"fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-[100] max-w-xs",children:[a.jsx("h3",{className:"font-bold mb-2",children:"播放器调试信息"}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:["当前音频: ",e?.title.zh||"无"]}),(0,a.jsxs)("div",{children:["播放状态: ",r?"播放中":"暂停"]}),(0,a.jsxs)("div",{children:["加载状态: ",i?"加载中":"已加载"]}),(0,a.jsxs)("div",{children:["播放器可见: ",t.isVisible?"是":"否"]}),(0,a.jsxs)("div",{children:["播放器模式: ",t.mode]}),(0,a.jsxs)("div",{children:["播放器位置: ",t.position]}),(0,a.jsxs)("div",{children:["最小化: ",t.isMinimized?"是":"否"]}),(0,a.jsxs)("div",{children:["当前时间: ",Math.floor(l),"s"]}),(0,a.jsxs)("div",{children:["总时长: ",Math.floor(d),"s"]}),(0,a.jsxs)("div",{children:["音量: ",Math.floor(100*s.volume),"%"]})]})]})}var d=t(7577);function c({testAudio:e}){let[s,t]=(0,d.useState)([{name:"播放器显示测试",status:"pending"},{name:"音频加载测试",status:"pending"},{name:"播放/暂停测试",status:"pending"},{name:"音量控制测试",status:"pending"},{name:"进度条测试",status:"pending"},{name:"停止功能测试",status:"pending"},{name:"最小化测试",status:"pending"},{name:"响应式布局测试",status:"pending"}]),{currentSound:r,playerUI:i,setPlayerVisible:l,togglePlayerMinimized:c,setUserVolume:m}=(0,n.U)(),{play:u,pause:x,stop:h,setVolume:p,isPlaying:g,isLoading:w,volume:f,currentTime:b,duration:y}=(0,o.x)(),j=(e,s,a)=>{t(t=>t.map((t,r)=>r===e?{...t,status:s,message:a}:t))},v=async e=>{j(e,"running");try{switch(e){case 0:await N();break;case 1:await P();break;case 2:await k();break;case 3:await T();break;case 4:await E();break;case 5:await _();break;case 6:await z();break;case 7:await A()}j(e,"passed","测试通过")}catch(s){j(e,"failed",s instanceof Error?s.message:"测试失败")}},N=async()=>{if(u(e),await new Promise(e=>setTimeout(e,1e3)),!i.isVisible)throw Error("播放器未显示");if(!r||r.id!==e.id)throw Error("当前音频设置错误")},P=async()=>{if(u(e),await new Promise(e=>setTimeout(e,2e3)),y<=0)throw Error("音频时长获取失败")},k=async()=>{if(u(e),await new Promise(e=>setTimeout(e,1e3)),!g)throw Error("播放功能异常");if(x(),await new Promise(e=>setTimeout(e,500)),g)throw Error("暂停功能异常")},T=async()=>{if(p(.5),await new Promise(e=>setTimeout(e,500)),Math.abs(f-.5)>.1)throw Error("音量设置失败");p(f)},E=async()=>{if(u(e),await new Promise(e=>setTimeout(e,2e3)),b<=0)throw Error("进度时间未更新")},_=async()=>{if(u(e),await new Promise(e=>setTimeout(e,1e3)),h(),await new Promise(e=>setTimeout(e,500)),g||b>0)throw Error("停止功能异常")},z=async()=>{l(!0),await new Promise(e=>setTimeout(e,500));let e=i.isMinimized;if(c(),await new Promise(e=>setTimeout(e,500)),i.isMinimized===e)throw Error("最小化切换失败")},A=async()=>{let e=document.querySelector('[data-testid="standard-player"]');if(!e)throw Error("播放器元素未找到");if(!(e.className.includes("sm:")||e.className.includes("md:")||e.className.includes("lg:")))throw Error("缺少响应式样式类")},D=async()=>{for(let e=0;e<s.length;e++)await v(e),await new Promise(e=>setTimeout(e,1e3))},M=e=>{switch(e){case"pending":default:return"text-gray-500";case"running":return"text-blue-500";case"passed":return"text-green-500";case"failed":return"text-red-500"}},S=e=>{switch(e){case"pending":default:return"⏳";case"running":return"\uD83D\uDD04";case"passed":return"✅";case"failed":return"❌"}};return(0,a.jsxs)("div",{className:"fixed top-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg z-[100] max-w-sm",children:[a.jsx("h3",{className:"font-bold mb-3 text-gray-900 dark:text-gray-100",children:"播放器功能测试"}),a.jsx("div",{className:"space-y-2 mb-4",children:s.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{children:S(e.status)}),a.jsx("span",{className:M(e.status),children:e.name})]}),a.jsx("button",{onClick:()=>v(s),disabled:"running"===e.status,className:"px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:"测试"})]},s))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx("button",{onClick:D,className:"flex-1 px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm",children:"运行全部测试"}),a.jsx("button",{onClick:()=>t(e=>e.map(e=>({...e,status:"pending",message:void 0}))),className:"px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm",children:"重置"})]}),s.some(e=>e.message)&&(0,a.jsxs)("div",{className:"mt-3 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs",children:[a.jsx("h4",{className:"font-semibold mb-1",children:"测试结果:"}),s.filter(e=>e.message).map((e,s)=>(0,a.jsxs)("div",{className:`${M(e.status)} mb-1`,children:[e.name,": ",e.message]},s))]})]})}let m=[{id:"test-rain-1",title:{zh:"轻柔雨声",en:"Gentle Rain"},description:{zh:"舒缓的雨声，帮助放松和睡眠",en:"Soothing rain sounds for relaxation and sleep"},category:"rain",tags:["rain","nature","sleep"],duration:30,filename:"gentle-rain.wav",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"test-rain-2",title:{zh:"暴雨声",en:"Heavy Rain"},description:{zh:"强烈的暴雨声，适合深度专注",en:"Intense rain sounds for deep focus"},category:"rain",tags:["rain","storm","focus"],duration:30,filename:"heavy-rain.wav",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"test-nature-1",title:{zh:"森林鸟声",en:"Forest Birds"},description:{zh:"清晨森林中的鸟儿歌唱",en:"Morning birds singing in the forest"},category:"nature",tags:["birds","forest","morning"],duration:30,filename:"forest-birds.wav",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"test-ocean-1",title:{zh:"海浪声",en:"Ocean Waves"},description:{zh:"平静的海浪拍打海岸的声音",en:"Peaceful ocean waves hitting the shore"},category:"ocean",tags:["ocean","waves","peaceful"],duration:30,filename:"ocean-waves.wav",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"}];function u(){return(0,r.useTranslations)("common"),(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:[a.jsx(l,{}),m[0]&&a.jsx(c,{testAudio:m[0]}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:"音频播放器测试页面"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"点击下面的音频卡片来测试播放器功能"})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m.map((e,s)=>a.jsx(i.AudioCard,{audio:e,variant:3===s?"compact":"default",showDuration:!0},e.id))}),(0,a.jsxs)("div",{className:"mt-12 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"测试说明"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600 dark:text-gray-400",children:[a.jsx("li",{children:"• 点击任意音频卡片应该会显示底部播放器"}),a.jsx("li",{children:"• 播放器应该显示当前播放的音频信息"}),a.jsx("li",{children:"• 播放器应该有播放/暂停、停止、音量控制等功能"}),a.jsx("li",{children:"• 在桌面端应该显示定时器和混音按钮"}),a.jsx("li",{children:"• 在移动端这些按钮应该被隐藏"}),a.jsx("li",{children:"• 播放器应该支持最小化和关闭"}),a.jsx("li",{children:"• 播放器应该有平滑的动画效果"}),a.jsx("li",{children:"• 音频文件为30秒的测试文件（静音），用于验证播放器功能"}),a.jsx("li",{children:"• 测试不同类型的音频：雨声、自然声音、海浪声"})]})]})]})]})}},2310:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[948,327,308,350,323],()=>t(547));module.exports=a})();