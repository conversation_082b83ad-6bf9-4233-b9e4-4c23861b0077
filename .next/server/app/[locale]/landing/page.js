(()=>{var e={};e.id=583,e.ids=[583],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8332:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>m,tree:()=>p}),r(1526),r(1578),r(7149),r(2029),r(2523);var t=r(3191),n=r(8716),o=r(7922),a=r.n(o),l=r(5231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(s,i);let p=["",{children:["[locale]",{children:["landing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1526)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/landing/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1578)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,7149)),"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,2523)),"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx"]}],c=["/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/landing/page.tsx"],d="/[locale]/landing/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/[locale]/landing/page",pathname:"/[locale]/landing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},5303:()=>{},1526:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(8585);function n({params:{locale:e}}){(0,t.redirect)(`/${e}`)}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[948,327,308,350],()=>r(8332));module.exports=t})();