{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [], "headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.google-analytics.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https://cdn.noisesleep.com https://www.google-analytics.com; media-src 'self' https://cdn.noisesleep.com; connect-src 'self' https://cdn.noisesleep.com https://www.google-analytics.com https://www.googletagmanager.com; frame-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/sounds/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Access-Control-Allow-Origin", "value": "https://noisesleep.com"}], "regex": "^/sounds(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/_next/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/_next/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [{"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/audio-test", "regex": "^/([^/]+?)/audio\\-test(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/audio\\-test(?:/)?$"}, {"page": "/[locale]/favorites", "regex": "^/([^/]+?)/favorites(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/favorites(?:/)?$"}, {"page": "/[locale]/landing", "regex": "^/([^/]+?)/landing(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/landing(?:/)?$"}, {"page": "/[locale]/mixing", "regex": "^/([^/]+?)/mixing(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/mixing(?:/)?$"}, {"page": "/[locale]/sounds", "regex": "^/([^/]+?)/sounds(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/sounds(?:/)?$"}, {"page": "/[locale]/sounds/[category]", "regex": "^/([^/]+?)/sounds/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPcategory": "nxtPcategory"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/sounds/(?<nxtPcategory>[^/]+?)(?:/)?$"}, {"page": "/[locale]/test-player", "regex": "^/([^/]+?)/test\\-player(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-player(?:/)?$"}], "staticRoutes": [{"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}