(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[282],{6456:function(e,t,n){"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}n.d(t,{default:function(){return a}});var i=n(2265),o=n(9362);function a(e){let{locale:t,...n}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return i.createElement(o.IntlProvider,r({locale:t},n))}},8667:function(e,t,n){"use strict";n.d(t,{default:function(){return i.a}});var r=n(8003),i=n.n(r)},8221:function(e,t){"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DOMAttributeNames:function(){return r},default:function(){return a},isEqualNode:function(){return o}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function i(e){let{type:t,props:n}=e,i=document.createElement(t);for(let e in n){if(!n.hasOwnProperty(e)||"children"===e||"dangerouslySetInnerHTML"===e||void 0===n[e])continue;let o=r[e]||e.toLowerCase();"script"===t&&("async"===o||"defer"===o||"noModule"===o)?i[o]=!!n[e]:i.setAttribute(o,n[e])}let{children:o,dangerouslySetInnerHTML:a}=n;return a?i.innerHTML=a.__html||"":o&&(i.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):""),i}function o(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let n=t.getAttribute("nonce");if(n&&!e.getAttribute("nonce")){let r=t.cloneNode(!0);return r.setAttribute("nonce",""),r.nonce=n,n===e.nonce&&e.isEqualNode(r)}}return e.isEqualNode(t)}function a(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let n=t[e.type]||[];n.push(e),t[e.type]=n});let r=t.title?t.title[0]:null,i="";if(r){let{children:e}=r.props;i="string"==typeof e?e:Array.isArray(e)?e.join(""):""}i!==document.title&&(document.title=i),["meta","base","link","style","script"].forEach(e=>{n(e,t[e]||[])})}}}n=(e,t)=>{let n=document.getElementsByTagName("head")[0],r=n.querySelector("meta[name=next-head-count]"),a=Number(r.content),l=[];for(let t=0,n=r.previousElementSibling;t<a;t++,n=(null==n?void 0:n.previousElementSibling)||null){var u;(null==n?void 0:null==(u=n.tagName)?void 0:u.toLowerCase())===e&&l.push(n)}let c=t.map(i).filter(e=>{for(let t=0,n=l.length;t<n;t++)if(o(l[t],e))return l.splice(t,1),!1;return!0});l.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),c.forEach(e=>n.insertBefore(e,r)),r.content=(a-l.length+c.length).toString()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3515:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8003:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return b},handleClientScriptLoad:function(){return h},initScriptLoader:function(){return g}});let r=n(7043),i=n(3099),o=n(7437),a=r._(n(4887)),l=i._(n(2265)),u=n(8701),c=n(8221),s=n(3515),f=new Map,d=new Set,p=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],m=e=>{if(a.default.preinit){e.forEach(e=>{a.default.preinit(e,{as:"style"})});return}if("undefined"!=typeof window){let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},v=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:i=null,dangerouslySetInnerHTML:o,children:a="",strategy:l="afterInteractive",onError:u,stylesheets:s}=e,v=n||t;if(v&&d.has(v))return;if(f.has(t)){d.add(v),f.get(t).then(r,u);return}let h=()=>{i&&i(),d.add(v)},g=document.createElement("script"),y=new Promise((e,t)=>{g.addEventListener("load",function(t){e(),r&&r.call(this,t),h()}),g.addEventListener("error",function(e){t(e)})}).catch(function(e){u&&u(e)});for(let[n,r]of(o?(g.innerHTML=o.__html||"",h()):a?(g.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):"",h()):t&&(g.src=t,f.set(t,y)),Object.entries(e))){if(void 0===r||p.includes(n))continue;let e=c.DOMAttributeNames[n]||n.toLowerCase();g.setAttribute(e,r)}"worker"===l&&g.setAttribute("type","text/partytown"),g.setAttribute("data-nscript",l),s&&m(s),document.body.appendChild(g)};function h(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,s.requestIdleCallback)(()=>v(e))}):v(e)}function g(e){e.forEach(h),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");d.add(t)})}function y(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:i=null,strategy:c="afterInteractive",onError:f,stylesheets:p,...m}=e,{updateScripts:h,scripts:g,getIsSsr:y,appDir:b,nonce:w}=(0,l.useContext)(u.HeadManagerContext),_=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||n;_.current||(i&&e&&d.has(e)&&i(),_.current=!0)},[i,t,n]);let E=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{!E.current&&("afterInteractive"===c?v(e):"lazyOnload"===c&&("complete"===document.readyState?(0,s.requestIdleCallback)(()=>v(e)):window.addEventListener("load",()=>{(0,s.requestIdleCallback)(()=>v(e))})),E.current=!0)},[e,c]),("beforeInteractive"===c||"worker"===c)&&(h?(g[c]=(g[c]||[]).concat([{id:t,src:n,onLoad:r,onReady:i,onError:f,...m}]),h(g)):y&&y()?d.add(t||n):y&&!y()&&v(e)),b){if(p&&p.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)return n?(a.default.preload(n,m.integrity?{as:"script",integrity:m.integrity,nonce:w,crossOrigin:m.crossOrigin}:{as:"script",nonce:w,crossOrigin:m.crossOrigin}),(0,o.jsx)("script",{nonce:w,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...m,id:t}])+")"}})):(m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,o.jsx)("script",{nonce:w,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}}));"afterInteractive"===c&&n&&a.default.preload(n,m.integrity?{as:"script",integrity:m.integrity,nonce:w,crossOrigin:m.crossOrigin}:{as:"script",nonce:w,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(y,"__nextScript",{value:!0});let b=y;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5488:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},3717:function(e){e.exports={style:{fontFamily:"'__Noto_Sans_SC_8dfb49', '__Noto_Sans_SC_Fallback_8dfb49'",fontStyle:"normal"},className:"__className_8dfb49",variable:"__variable_8dfb49"}},322:function(e,t,n){"use strict";var r=n(2265);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=i},2137:function(e,t,n){"use strict";var r=n(2265);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))});t.Z=i},1:function(e,t,n){"use strict";var r=n(2265);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))});t.Z=i},6072:function(e,t,n){"use strict";var r=n(2265);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"}))});t.Z=i},5945:function(e,t,n){"use strict";var r=n(2265);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"}))});t.Z=i},7165:function(e,t,n){"use strict";var r=n(2265);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=i},1906:function(e,t,n){"use strict";n.d(t,{c:function(){return l}});var r=n(2265),i=n(3078),o=n(5750),a=n(3576);function l(e){let t=(0,a.h)(()=>(0,i.BX)(e)),{isStatic:n}=(0,r.useContext)(o._);if(n){let[,n]=(0,r.useState)(e);(0,r.useEffect)(()=>t.on("change",n),[])}return t}},3812:function(e,t,n){"use strict";n.d(t,{H:function(){return d}});var r=n(2888);let i=e=>e&&"object"==typeof e&&e.mix,o=e=>i(e)?e.mix:void 0;var a=n(1906),l=n(1534),u=n(8345);function c(e,t){let n=(0,a.c)(t()),r=()=>n.set(t());return r(),(0,l.L)(()=>{let t=()=>u.Wi.update(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,u.Pn)(r)}}),n}var s=n(3576),f=n(3078);function d(e,t,n,i){if("function"==typeof e)return function(e){f.S1.current=[],e();let t=c(f.S1.current,e);return f.S1.current=void 0,t}(e);let a="function"==typeof t?t:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let i=!Array.isArray(t[0]),a=i?0:-1,l=t[0+a],u=t[1+a],c=t[2+a],s=t[3+a],f=(0,r.s)(u,c,{mixer:o(c[0]),...s});return i?f(l):f}(t,n,i);return Array.isArray(e)?p(e,a):p([e],e=>{let[t]=e;return a(t)})}function p(e,t){let n=(0,s.h)(()=>[]);return c(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}},6430:function(e,t,n){"use strict";n.d(t,{CA:function(){return D},Tb:function(){return R},Tx:function(){return N},Y:function(){return C},kz:function(){return T}});var r,i,o,a,l=-1,u=function(e){addEventListener("pageshow",function(t){t.persisted&&(l=t.timeStamp,e(t))},!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},s=function(){var e=c();return e&&e.activationStart||0},f=function(e,t){var n=c(),r="navigate";return l>=0?r="back-forward-cache":n&&(document.prerendering||s()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},d=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})});return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},p=function(e,t,n,r){var i,o;return function(a){var l;t.value>=0&&(a||r)&&((o=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=o,t.rating=(l=t.value)>n[1]?"poor":l>n[0]?"needs-improvement":"good",e(t))}},m=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},v=function(e){var t=function(t){"pagehide"!==t.type&&"hidden"!==document.visibilityState||e(t)};addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0)},h=function(e){var t=!1;return function(n){t||(e(n),t=!0)}},g=-1,y=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},b=function(e){"hidden"===document.visibilityState&&g>-1&&(g="visibilitychange"===e.type?e.timeStamp:0,_())},w=function(){addEventListener("visibilitychange",b,!0),addEventListener("prerenderingchange",b,!0)},_=function(){removeEventListener("visibilitychange",b,!0),removeEventListener("prerenderingchange",b,!0)},E=function(){return g<0&&(g=y(),w(),u(function(){setTimeout(function(){g=y(),w()},0)})),{get firstHiddenTime(){return g}}},L=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},k=[1800,3e3],C=function(e,t){t=t||{},L(function(){var n,r=E(),i=f("FCP"),o=d("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-s(),0),i.entries.push(e),n(!0)))})});o&&(n=p(e,i,k,t.reportAllChanges),u(function(r){n=p(e,i=f("FCP"),k,t.reportAllChanges),m(function(){i.value=performance.now()-r.timeStamp,n(!0)})}))})},S=[.1,.25],T=function(e,t){t=t||{},C(h(function(){var n,r=f("CLS",0),i=0,o=[],a=function(e){e.forEach(function(e){if(!e.hadRecentInput){var t=o[0],n=o[o.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,o.push(e)):(i=e.value,o=[e])}}),i>r.value&&(r.value=i,r.entries=o,n())},l=d("layout-shift",a);l&&(n=p(e,r,S,t.reportAllChanges),v(function(){a(l.takeRecords()),n(!0)}),u(function(){i=0,n=p(e,r=f("CLS",0),S,t.reportAllChanges),m(function(){return n()})}),setTimeout(n,0))}))},x={passive:!0,capture:!0},M=new Date,O=function(e,t){r||(r=t,i=e,o=new Date,I(removeEventListener),A())},A=function(){if(i>=0&&i<o-M){var e={entryType:"first-input",name:r.type,target:r.target,cancelable:r.cancelable,startTime:r.timeStamp,processingStart:r.timeStamp+i};a.forEach(function(t){t(e)}),a=[]}},j=function(e){if(e.cancelable){var t,n,r,i=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?(t=function(){O(i,e),r()},n=function(){r()},r=function(){removeEventListener("pointerup",t,x),removeEventListener("pointercancel",n,x)},addEventListener("pointerup",t,x),addEventListener("pointercancel",n,x)):O(i,e)}},I=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,j,x)})},P=[100,300],N=function(e,t){t=t||{},L(function(){var n,o=E(),l=f("FID"),c=function(e){e.startTime<o.firstHiddenTime&&(l.value=e.processingStart-e.startTime,l.entries.push(e),n(!0))},s=function(e){e.forEach(c)},m=d("first-input",s);n=p(e,l,P,t.reportAllChanges),m&&v(h(function(){s(m.takeRecords()),m.disconnect()})),m&&u(function(){n=p(e,l=f("FID"),P,t.reportAllChanges),a=[],i=-1,r=null,I(addEventListener),a.push(c),A()})})},H=[2500,4e3],q={},R=function(e,t){t=t||{},L(function(){var n,r=E(),i=f("LCP"),o=function(e){var t=e[e.length-1];t&&t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries=[t],n())},a=d("largest-contentful-paint",o);if(a){n=p(e,i,H,t.reportAllChanges);var l=h(function(){q[i.id]||(o(a.takeRecords()),a.disconnect(),q[i.id]=!0,n(!0))});["keydown","click"].forEach(function(e){addEventListener(e,function(){return setTimeout(l,0)},!0)}),v(l),u(function(r){n=p(e,i=f("LCP"),H,t.reportAllChanges),m(function(){i.value=performance.now()-r.timeStamp,q[i.id]=!0,n(!0)})})}})},F=[800,1800],B=function e(t){document.prerendering?L(function(){return e(t)}):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},D=function(e,t){t=t||{};var n=f("TTFB"),r=p(e,n,F,t.reportAllChanges);B(function(){var i=c();if(i){var o=i.responseStart;if(o<=0||o>performance.now())return;n.value=Math.max(o-s(),0),n.entries=[i],r(!0),u(function(){(r=p(e,n=f("TTFB",0),F,t.reportAllChanges))(!0)})}})}}}]);