(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[91,141],{2151:function(e,r,a){Promise.resolve().then(a.bind(a,6456)),Promise.resolve().then(a.bind(a,3055)),Promise.resolve().then(a.bind(a,1326)),Promise.resolve().then(a.bind(a,6494)),Promise.resolve().then(a.bind(a,9919)),Promise.resolve().then(a.bind(a,6563))},6456:function(e,r,a){"use strict";function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}a.d(r,{default:function(){return o}});var t=a(2265),l=a(9362);function o(e){let{locale:r,...a}=e;if(!r)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return t.createElement(l.IntlProvider,s({locale:r},a))}},1326:function(e,r,a){"use strict";a.d(r,{AudioGrid:function(){return n}});var s=a(7437),t=a(2265),l=a(2586),o=a(1994),i=a(3055);function n(e){let{audios:r,variant:a="default",columns:n="auto",showSearch:c=!0,showFilter:d=!0,showSort:u=!0,onAudioPlay:g,className:m,emptyMessage:h,loading:x=!1}=e,f=(0,l.useTranslations)("common"),[b,v]=(0,t.useState)(""),[y,p]=(0,t.useState)("all"),[j,w]=(0,t.useState)("name"),N=(0,t.useMemo)(()=>Array.from(new Set(r.map(e=>e.category))).sort(),[r]),k=(0,t.useMemo)(()=>{let e=r;if(b.trim()){let r=b.toLowerCase().trim();e=e.filter(e=>{var a;return Object.values(e.title).some(e=>e.toLowerCase().includes(r))||e.description&&Object.values(e.description).some(e=>e.toLowerCase().includes(r))||e.category.toLowerCase().includes(r)||(null===(a=e.tags)||void 0===a?void 0:a.some(e=>e.toLowerCase().includes(r)))})}return"all"!==y&&(e=e.filter(e=>e.category===y)),e.sort((e,r)=>{switch(j){case"name":return(e.title.en||e.title.zh||"").localeCompare(r.title.en||r.title.zh||"");case"category":return e.category.localeCompare(r.category);case"rating":return(r.scientificRating||0)-(e.scientificRating||0);case"recent":return e.id.localeCompare(r.id);default:return 0}}),e},[r,b,y,j]),C=()=>{if("auto"===n)switch(a){case"compact":return"grid-cols-1";case"detailed":return"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";default:return"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"}return({1:"grid-cols-1",2:"grid-cols-1 sm:grid-cols-2",3:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"})[n]};return x?(0,s.jsxs)("div",{className:(0,o.W)("space-y-4",m),children:[(c||d||u)&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[c&&(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})}),d&&(0,s.jsx)("div",{className:"w-full sm:w-48",children:(0,s.jsx)("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})}),u&&(0,s.jsx)("div",{className:"w-full sm:w-48",children:(0,s.jsx)("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})})]}),(0,s.jsx)("div",{className:(0,o.W)("grid gap-4",C()),children:Array.from({length:8}).map((e,r)=>(0,s.jsx)("div",{className:"bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse",children:(0,s.jsx)("div",{className:"h-48"})},r))})]}):(0,s.jsxs)("div",{className:(0,o.W)("space-y-6",m),children:[(c||d||u)&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[c&&(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,s.jsx)("input",{type:"text",placeholder:f("searchAudios"),value:b,onChange:e=>v(e.target.value),className:(0,o.W)("block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","placeholder-gray-500 dark:placeholder-gray-400","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200")})]})}),d&&(0,s.jsx)("div",{className:"w-full sm:w-48",children:(0,s.jsxs)("select",{value:y,onChange:e=>p(e.target.value),className:(0,o.W)("block w-full px-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200"),children:[(0,s.jsx)("option",{value:"all",children:f("allCategories")}),N.map(e=>(0,s.jsx)("option",{value:e,children:e},e))]})}),u&&(0,s.jsx)("div",{className:"w-full sm:w-48",children:(0,s.jsxs)("select",{value:j,onChange:e=>w(e.target.value),className:(0,o.W)("block w-full px-3 py-2 border border-gray-300 dark:border-gray-600","rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500","transition-colors duration-200"),children:[(0,s.jsx)("option",{value:"name",children:f("sortByName")}),(0,s.jsx)("option",{value:"category",children:f("sortByCategory")}),(0,s.jsx)("option",{value:"rating",children:f("sortByRating")}),(0,s.jsx)("option",{value:"recent",children:f("sortByRecent")})]})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("span",{children:f("showingResults",{count:k.length,total:r.length})}),b&&(0,s.jsx)("button",{onClick:()=>v(""),className:"text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300",children:f("clearSearch")})]}),k.length>0?(0,s.jsx)("div",{className:(0,o.W)("grid gap-4",C()),children:k.map(e=>(0,s.jsx)(i.AudioCard,{audio:e,variant:a,showCategory:"all"===y,showTags:!0,showDuration:"detailed"===a,showDescription:"detailed"===a,onPlay:g},e.id))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",children:(0,s.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"})})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:h||f("noAudiosFound")}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:f(b?"tryDifferentSearch":"noAudiosAvailable")}),b&&(0,s.jsx)("button",{onClick:()=>v(""),className:(0,o.W)("inline-flex items-center px-4 py-2 border border-transparent","text-sm font-medium rounded-md text-white bg-amber-600","hover:bg-amber-700 focus:outline-none focus:ring-2","focus:ring-offset-2 focus:ring-amber-500 transition-colors"),children:f("clearSearch")})]})]})}}},function(e){e.O(0,[586,979,626,648,742,914,127,917,563,971,117,744],function(){return e(e.s=2151)}),_N_E=e.O()}]);