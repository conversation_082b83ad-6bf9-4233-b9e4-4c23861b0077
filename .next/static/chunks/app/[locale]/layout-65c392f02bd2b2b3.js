(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[203],{604:function(e,t,n){Promise.resolve().then(n.bind(n,6456)),Promise.resolve().then(n.t.bind(n,5488,23)),Promise.resolve().then(n.t.bind(n,3717,23)),Promise.resolve().then(n.bind(n,2808)),Promise.resolve().then(n.bind(n,4195)),Promise.resolve().then(n.bind(n,8888)),Promise.resolve().then(n.bind(n,8119)),Promise.resolve().then(n.bind(n,3173)),Promise.resolve().then(n.bind(n,1485)),Promise.resolve().then(n.bind(n,4805)),Promise.resolve().then(n.bind(n,5496))},2808:function(e,t,n){"use strict";n.d(t,{AnalyticsProvider:function(){return m}});var r=n(7437),a=n(8667),o=n(2265),s=n(9376);let i="G-FKSNVZQTMD";function l(){let e=(0,s.usePathname)(),t=(0,s.useSearchParams)();return((0,o.useEffect)(()=>{if(!i)return;let n=e+t.toString();window.gtag&&window.gtag("config",i,{page_path:n})},[e,t]),i)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.default,{strategy:"afterInteractive",src:"https://www.googletagmanager.com/gtag/js?id=".concat(i)}),(0,r.jsx)(a.default,{id:"google-analytics",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:"\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', '".concat(i,"', {\n              page_path: window.location.pathname,\n              anonymize_ip: true,\n              allow_google_signals: false,\n              allow_ad_personalization_signals: false\n            });\n          ")}})]}):null}let c="se2huma822";function g(){return((0,o.useEffect)(()=>{if(!c){console.log("\uD83D\uDD0D Microsoft Clarity 未启用:",{hasProjectId:!!c,analyticsEnabled:!0});return}console.log("\uD83D\uDD0D Microsoft Clarity 已启用:",c)},[]),c)?(0,r.jsx)(a.default,{id:"microsoft-clarity",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:'\n          (function(c,l,a,r,i,t,y){\n              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};\n              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;\n              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);\n          })(window, document, "clarity", "script", "'.concat(c,'");\n        ')}}):null}var d=n(6430);function u(e){window.gtag&&window.gtag("event",e.name,{event_category:"Web Vitals",event_label:e.id,value:Math.round("CLS"===e.name?1e3*e.value:e.value),non_interaction:!0}),fetch("/api/analytics/web-vitals",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,value:e.value,id:e.id,delta:e.delta,rating:e.rating,navigationType:e.navigationType,timestamp:Date.now(),url:window.location.href,userAgent:navigator.userAgent})}).catch(console.error)}function h(){return(0,o.useEffect)(()=>{(0,d.kz)(u),(0,d.Tx)(u),(0,d.Y)(u),(0,d.Tb)(u),(0,d.CA)(u)},[]),null}function m(e){let{children:t}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l,{}),(0,r.jsx)(g,{}),(0,r.jsx)(h,{}),t]})}},5496:function(e,t,n){"use strict";n.d(t,{ErrorBoundary:function(){return s}});var r=n(7437),a=n(2265),o=n(6585);class s extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){(0,o.jG)(e,{componentStack:t.componentStack||"Unknown component stack"},this.props.locale||"en"),this.setState({error:e,errorInfo:t})}render(){if(this.state.hasError){if(this.props.fallback)return this.props.fallback;let e=this.props.locale||"en",t=(0,o.Ic)(e,"500");return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"max-w-md w-full mx-auto text-center px-4",children:[(0,r.jsx)("div",{className:"w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600",children:(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:t.title}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:t.message}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{onClick:this.handleRetry,className:"w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors",children:t.action}),(0,r.jsx)("button",{onClick:()=>window.location.href="zh"===e?"/zh":"/",className:"w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"zh"===e?"返回首页":"Go to Homepage"})]}),!1]})})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.state={hasError:!1}}}},6585:function(e,t,n){"use strict";function r(e,t,n){console.error("Error Boundary caught an error:",{error:e.message,stack:e.stack,componentStack:t.componentStack,locale:n,timestamp:new Date().toISOString(),userAgent:window.navigator.userAgent})}function a(e,t){var n;let r={en:{404:{title:"Page Not Found",message:"The page you are looking for does not exist.",action:"Go to Homepage"},500:{title:"Server Error",message:"Something went wrong on our end. Please try again later.",action:"Refresh Page"},network:{title:"Network Error",message:"Unable to connect to the server. Please check your internet connection.",action:"Try Again"}},zh:{404:{title:"页面未找到",message:"您访问的页面不存在。",action:"返回首页"},500:{title:"服务器错误",message:"服务器出现问题，请稍后重试。",action:"刷新页面"},network:{title:"网络错误",message:"无法连接到服务器，请检查您的网络连接。",action:"重试"}}};return(null===(n=r[e])||void 0===n?void 0:n[t])||r.en[t]}n.d(t,{Ic:function(){return a},jG:function(){return r}})}},function(e){e.O(0,[500,732,586,979,626,282,742,914,127,971,117,744],function(){return e(e.s=604)}),_N_E=e.O()}]);