(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[322],{3196:function(e,r,a){Promise.resolve().then(a.bind(a,6456)),Promise.resolve().then(a.bind(a,6877)),Promise.resolve().then(a.bind(a,2608)),Promise.resolve().then(a.bind(a,9914))},6456:function(e,r,a){"use strict";function t(){return(t=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)({}).hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e}).apply(null,arguments)}a.d(r,{default:function(){return i}});var s=a(2265),n=a(9362);function i(e){let{locale:r,...a}=e;if(!r)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return s.createElement(n.IntlProvider,t({locale:r},a))}},6877:function(e,r,a){"use strict";a.d(r,{MixingBoard:function(){return g}});var t=a(7437),s=a(2265),n=a(2586),i=a(1994),l=a(8981),o=a(2608),d=a(4805),c=a(9590),u=a(9280);function g(e){let{className:r}=e,a=(0,n.useTranslations)("mixing"),[g,x]=(0,s.useState)(!1),[m,h]=(0,s.useState)("all"),{mixingChannels:b,maxChannels:y,masterVolume:f,addMixingChannel:v,removeMixingChannel:k,updateChannelVolume:p,setMasterVolume:j}=(0,l.U)(),{playChannel:N,pauseChannel:w,stopChannel:C,setChannelVolume:D,muteChannel:M,unmuteChannel:P,getChannelState:L,stopAllChannels:F,setMasterVolume:A}=(0,c.o)(),E=u.N.filter(e=>!b.some(r=>r.soundId===e.id)),V="all"===m?E:E.filter(e=>e.category===m),B=Array.from(new Set(u.N.map(e=>e.category))).sort(),_=(0,s.useCallback)(e=>{v(e)&&x(!1)},[v]),I=(0,s.useCallback)(e=>{k(e)},[k]),O=(0,s.useCallback)((e,r)=>{p(e,r),D(e,r)},[p,D]),S=(0,s.useCallback)(e=>{j(e),A(e)},[j,A]),W=(0,s.useCallback)(e=>{N(e)},[N]),z=(0,s.useCallback)(e=>{w(e)},[w]),R=(0,s.useCallback)(e=>{C(e)},[C]),H=(0,s.useCallback)((e,r)=>{r?M(e):P(e)},[M,P]),T=e=>u.N.find(r=>r.id===e),U=e=>({rain:"\uD83C\uDF27️",nature:"\uD83C\uDF3F",noise:"\uD83D\uDD0A",animals:"\uD83D\uDC3E",things:"\uD83C\uDFE0",transport:"\uD83D\uDE97",urban:"\uD83C\uDFD9️",places:"\uD83D\uDCCD"})[e.toLowerCase()]||"\uD83C\uDFB5";return(0,t.jsxs)("div",{className:(0,i.W)("space-y-6",r),children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:a("title")}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:a("description",{max:y})})]}),(0,t.jsxs)("button",{onClick:()=>x(!0),disabled:b.length>=y,className:(0,i.W)("inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2",b.length>=y?"bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500":"bg-amber-500 text-white hover:bg-amber-600 shadow-sm hover:shadow-md"),children:[(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),a("addAudio")]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:a("masterVolume")}),(0,t.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(100*f),"%"]})]}),(0,t.jsx)(d.VolumeControl,{volume:f,onVolumeChange:S,showIcon:!0,showValue:!1,size:"md"})]})]}),(0,t.jsx)("div",{className:"space-y-4",children:0===b.length?(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700 p-12 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",children:(0,t.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"})})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:a("noChannels")}),(0,t.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:a("addFirstAudio")}),(0,t.jsxs)("button",{onClick:()=>x(!0),className:"inline-flex items-center gap-2 px-4 py-2 bg-amber-500 text-white rounded-lg font-medium hover:bg-amber-600 transition-colors",children:[(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),a("addAudio")]})]}):(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:b.map(e=>{let r=T(e.soundId);if(!r)return null;let a=L(e.id);return(0,t.jsx)(o.MixingChannel,{channel:e,audio:r,isPlaying:a.isPlaying,isLoading:a.isLoading,onPlay:()=>W(e.id),onPause:()=>z(e.id),onStop:()=>R(e.id),onVolumeChange:r=>O(e.id,r),onMute:()=>H(e.id,!e.isMuted),onRemove:()=>I(e.id)},e.id)})})}),g&&(0,t.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0",children:[(0,t.jsx)("div",{className:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75",onClick:()=>x(!1)}),(0,t.jsxs)("div",{className:"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:a("selectAudio")}),(0,t.jsx)("button",{onClick:()=>x(!1),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("select",{value:m,onChange:e=>h(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-amber-500",children:[(0,t.jsx)("option",{value:"all",children:a("allCategories")}),B.map(e=>(0,t.jsxs)("option",{value:e,children:[U(e)," ",e]},e))]})}),(0,t.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===V.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:a("noAvailableAudios")}):(0,t.jsx)("div",{className:"grid gap-3 sm:grid-cols-2",children:V.map(e=>(0,t.jsxs)("button",{onClick:()=>_(e),className:"flex items-center gap-3 p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:border-amber-300 dark:hover:border-amber-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,t.jsx)("span",{className:"text-lg",children:U(e.category)})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100 truncate",children:e.title.en}),(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:e.category})]}),e.scientificRating&&(0,t.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["⭐ ",e.scientificRating.toFixed(1)]})]},e.id))})})]})]})})]})}}},function(e){e.O(0,[586,979,626,742,914,971,117,744],function(){return e(e.s=3196)}),_N_E=e.O()}]);