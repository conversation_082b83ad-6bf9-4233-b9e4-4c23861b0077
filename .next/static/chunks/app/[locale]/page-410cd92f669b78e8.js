(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[61],{7662:function(e,t,n){Promise.resolve().then(n.bind(n,6456)),Promise.resolve().then(n.bind(n,2861)),Promise.resolve().then(n.bind(n,3720)),Promise.resolve().then(n.bind(n,8482)),Promise.resolve().then(n.bind(n,6494)),Promise.resolve().then(n.bind(n,9919)),Promise.resolve().then(n.bind(n,6563))},6456:function(e,t,n){"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}n.d(t,{default:function(){return i}});var l=n(2265),s=n(9362);function i(e){let{locale:t,...n}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return l.createElement(s.IntlProvider,r({locale:t},n))}},3720:function(e,t,n){"use strict";n.d(t,{default:function(){return i}});var r=n(7437),l=n(2586),s=n(7648);function i(e){let{className:t=""}=e,n=(0,l.useTranslations)("landing"),i=(0,l.useLocale)();return(0,r.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-indigo-600 to-purple-600 ".concat(t),children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h3",{className:"text-4xl font-light text-white mb-6",children:n("cta.title")}),(0,r.jsx)("p",{className:"text-xl text-indigo-100 mb-8 max-w-2xl mx-auto",children:n("cta.description")}),(0,r.jsx)(s.default,{href:"/".concat(i,"/sounds"),className:"inline-block bg-white text-indigo-600 px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-50 transition-all transform hover:scale-105",children:n("buttons.startFree")})]})})}},2861:function(e,t,n){"use strict";n.d(t,{default:function(){return i}});var r=n(7437),l=n(2586),s=n(2265);function i(e){let{icon:t,type:n,name:i,description:a,className:o=""}=e,c=(0,l.useTranslations)("landing"),[d,x]=(0,s.useState)(!1);return(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100 ".concat(o),children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,r.jsx)("div",{className:"text-4xl",children:t}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat({noise:"bg-blue-100 text-blue-800",things:"bg-gray-100 text-gray-800",transport:"bg-yellow-100 text-yellow-800",places:"bg-purple-100 text-purple-800",urban:"bg-orange-100 text-orange-800",animals:"bg-green-100 text-green-800",rain:"bg-cyan-100 text-cyan-800",nature:"bg-emerald-100 text-emerald-800"}[n]||"bg-gray-100 text-gray-800"),children:c("typeLabels.".concat(n))})]}),(0,r.jsx)("h4",{className:"text-xl font-medium text-gray-800 mb-2",children:i}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:a}),(0,r.jsx)("button",{onClick:()=>{x(!d),d||setTimeout(()=>{x(!1)},3e3)},className:"w-full py-3 rounded-full transition-all ".concat(d?"bg-indigo-600 text-white":"bg-gray-100 text-gray-700 hover:bg-indigo-100"),children:c(d?"buttons.pause":"buttons.play")})]})}},8482:function(e,t,n){"use strict";n.d(t,{default:function(){return i}});var r=n(7437),l=n(2586),s=n(7648);function i(e){let{className:t=""}=e,n=(0,l.useTranslations)("landing"),i=(0,l.useLocale)();return(0,r.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8 ".concat(t),children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h2",{className:"text-5xl md:text-6xl font-light text-gray-800 mb-6 leading-tight",children:"zh"===i?(0,r.jsxs)(r.Fragment,{children:["让",(0,r.jsx)("span",{className:"text-indigo-600",children:n("hero.titleHighlight")}),(0,r.jsx)("br",{}),"陪伴你的美梦"]}):(0,r.jsxs)(r.Fragment,{children:["Let"," ",(0,r.jsx)("span",{className:"text-indigo-600",children:n("hero.titleHighlight")}),(0,r.jsx)("br",{}),"Accompany Your Sweet Dreams"]})}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed",children:n("hero.description")}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(s.default,{href:"/".concat(i,"/sounds"),className:"inline-block bg-indigo-600 text-white px-8 py-4 rounded-full text-lg hover:bg-indigo-700 transition-all transform hover:scale-105",children:n("buttons.experience")}),(0,r.jsx)("button",{className:"border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-full text-lg hover:bg-indigo-50 transition-colors",children:n("buttons.learnMore")})]})]})})}}},function(e){e.O(0,[586,648,563,971,117,744],function(){return e(e.s=7662)}),_N_E=e.O()}]);