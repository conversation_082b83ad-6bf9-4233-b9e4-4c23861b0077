(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[35],{3816:function(e,s,a){Promise.resolve().then(a.bind(a,2172))},2172:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return u}});var t=a(7437),i=a(2586),r=a(3055),n=a(8981),d=a(5444);function c(){let{currentSound:e,playState:s,playerUI:a}=(0,n.U)(),{isPlaying:i,isLoading:r,currentTime:c,duration:l}=(0,d.x)();return(0,t.jsxs)("div",{className:"fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-[100] max-w-xs",children:[(0,t.jsx)("h3",{className:"font-bold mb-2",children:"播放器调试信息"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{children:["当前音频: ",(null==e?void 0:e.title.zh)||"无"]}),(0,t.jsxs)("div",{children:["播放状态: ",i?"播放中":"暂停"]}),(0,t.jsxs)("div",{children:["加载状态: ",r?"加载中":"已加载"]}),(0,t.jsxs)("div",{children:["播放器可见: ",a.isVisible?"是":"否"]}),(0,t.jsxs)("div",{children:["播放器模式: ",a.mode]}),(0,t.jsxs)("div",{children:["播放器位置: ",a.position]}),(0,t.jsxs)("div",{children:["最小化: ",a.isMinimized?"是":"否"]}),(0,t.jsxs)("div",{children:["当前时间: ",Math.floor(c),"s"]}),(0,t.jsxs)("div",{children:["总时长: ",Math.floor(l),"s"]}),(0,t.jsxs)("div",{children:["音量: ",Math.floor(100*s.volume),"%"]})]})]})}var l=a(2265);function o(e){let{testAudio:s}=e,[a,i]=(0,l.useState)([{name:"播放器显示测试",status:"pending"},{name:"音频加载测试",status:"pending"},{name:"播放/暂停测试",status:"pending"},{name:"音量控制测试",status:"pending"},{name:"进度条测试",status:"pending"},{name:"停止功能测试",status:"pending"},{name:"最小化测试",status:"pending"},{name:"响应式布局测试",status:"pending"}]),{currentSound:r,playerUI:c,setPlayerVisible:o,togglePlayerMinimized:m,setUserVolume:u}=(0,n.U)(),{play:h,pause:x,stop:g,setVolume:w,isPlaying:f,isLoading:p,volume:b,currentTime:j,duration:y}=(0,d.x)(),v=(e,s,a)=>{i(t=>t.map((t,i)=>i===e?{...t,status:s,message:a}:t))},N=async e=>{v(e,"running");try{switch(e){case 0:await k();break;case 1:await T();break;case 2:await E();break;case 3:await z();break;case 4:await P();break;case 5:await A();break;case 6:await M();break;case 7:await Z()}v(e,"passed","测试通过")}catch(s){v(e,"failed",s instanceof Error?s.message:"测试失败")}},k=async()=>{if(h(s),await new Promise(e=>setTimeout(e,1e3)),!c.isVisible)throw Error("播放器未显示");if(!r||r.id!==s.id)throw Error("当前音频设置错误")},T=async()=>{if(h(s),await new Promise(e=>setTimeout(e,2e3)),y<=0)throw Error("音频时长获取失败")},E=async()=>{if(h(s),await new Promise(e=>setTimeout(e,1e3)),!f)throw Error("播放功能异常");if(x(),await new Promise(e=>setTimeout(e,500)),f)throw Error("暂停功能异常")},z=async()=>{if(w(.5),await new Promise(e=>setTimeout(e,500)),Math.abs(b-.5)>.1)throw Error("音量设置失败");w(b)},P=async()=>{if(h(s),await new Promise(e=>setTimeout(e,2e3)),j<=0)throw Error("进度时间未更新")},A=async()=>{if(h(s),await new Promise(e=>setTimeout(e,1e3)),g(),await new Promise(e=>setTimeout(e,500)),f||j>0)throw Error("停止功能异常")},M=async()=>{o(!0),await new Promise(e=>setTimeout(e,500));let e=c.isMinimized;if(m(),await new Promise(e=>setTimeout(e,500)),c.isMinimized===e)throw Error("最小化切换失败")},Z=async()=>{let e=document.querySelector('[data-testid="standard-player"]');if(!e)throw Error("播放器元素未找到");if(!(e.className.includes("sm:")||e.className.includes("md:")||e.className.includes("lg:")))throw Error("缺少响应式样式类")},C=async()=>{for(let e=0;e<a.length;e++)await N(e),await new Promise(e=>setTimeout(e,1e3))},_=e=>{switch(e){case"pending":default:return"text-gray-500";case"running":return"text-blue-500";case"passed":return"text-green-500";case"failed":return"text-red-500"}},D=e=>{switch(e){case"pending":default:return"⏳";case"running":return"\uD83D\uDD04";case"passed":return"✅";case"failed":return"❌"}};return(0,t.jsxs)("div",{className:"fixed top-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg z-[100] max-w-sm",children:[(0,t.jsx)("h3",{className:"font-bold mb-3 text-gray-900 dark:text-gray-100",children:"播放器功能测试"}),(0,t.jsx)("div",{className:"space-y-2 mb-4",children:a.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:D(e.status)}),(0,t.jsx)("span",{className:_(e.status),children:e.name})]}),(0,t.jsx)("button",{onClick:()=>N(s),disabled:"running"===e.status,className:"px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:"测试"})]},s))}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("button",{onClick:C,className:"flex-1 px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm",children:"运行全部测试"}),(0,t.jsx)("button",{onClick:()=>i(e=>e.map(e=>({...e,status:"pending",message:void 0}))),className:"px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm",children:"重置"})]}),a.some(e=>e.message)&&(0,t.jsxs)("div",{className:"mt-3 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs",children:[(0,t.jsx)("h4",{className:"font-semibold mb-1",children:"测试结果:"}),a.filter(e=>e.message).map((e,s)=>(0,t.jsxs)("div",{className:"".concat(_(e.status)," mb-1"),children:[e.name,": ",e.message]},s))]})]})}let m=[{id:"test-rain-1",title:{zh:"轻柔雨声",en:"Gentle Rain"},description:{zh:"舒缓的雨声，帮助放松和睡眠",en:"Soothing rain sounds for relaxation and sleep"},category:"rain",tags:["rain","nature","sleep"],duration:30,filename:"gentle-rain.wav",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"test-rain-2",title:{zh:"暴雨声",en:"Heavy Rain"},description:{zh:"强烈的暴雨声，适合深度专注",en:"Intense rain sounds for deep focus"},category:"rain",tags:["rain","storm","focus"],duration:30,filename:"heavy-rain.wav",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"test-nature-1",title:{zh:"森林鸟声",en:"Forest Birds"},description:{zh:"清晨森林中的鸟儿歌唱",en:"Morning birds singing in the forest"},category:"nature",tags:["birds","forest","morning"],duration:30,filename:"forest-birds.wav",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"test-ocean-1",title:{zh:"海浪声",en:"Ocean Waves"},description:{zh:"平静的海浪拍打海岸的声音",en:"Peaceful ocean waves hitting the shore"},category:"ocean",tags:["ocean","waves","peaceful"],duration:30,filename:"ocean-waves.wav",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"}];function u(){return(0,i.useTranslations)("common"),(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:[(0,t.jsx)(c,{}),m[0]&&(0,t.jsx)(o,{testAudio:m[0]}),(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:"音频播放器测试页面"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"点击下面的音频卡片来测试播放器功能"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m.map((e,s)=>(0,t.jsx)(r.AudioCard,{audio:e,variant:3===s?"compact":"default",showDuration:!0},e.id))}),(0,t.jsxs)("div",{className:"mt-12 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"测试说明"}),(0,t.jsxs)("ul",{className:"space-y-2 text-gray-600 dark:text-gray-400",children:[(0,t.jsx)("li",{children:"• 点击任意音频卡片应该会显示底部播放器"}),(0,t.jsx)("li",{children:"• 播放器应该显示当前播放的音频信息"}),(0,t.jsx)("li",{children:"• 播放器应该有播放/暂停、停止、音量控制等功能"}),(0,t.jsx)("li",{children:"• 在桌面端应该显示定时器和混音按钮"}),(0,t.jsx)("li",{children:"• 在移动端这些按钮应该被隐藏"}),(0,t.jsx)("li",{children:"• 播放器应该支持最小化和关闭"}),(0,t.jsx)("li",{children:"• 播放器应该有平滑的动画效果"}),(0,t.jsx)("li",{children:"• 音频文件为30秒的测试文件（静音），用于验证播放器功能"}),(0,t.jsx)("li",{children:"• 测试不同类型的音频：雨声、自然声音、海浪声"})]})]})]})]})}}},function(e){e.O(0,[586,979,626,742,914,127,917,971,117,744],function(){return e(e.s=3816)}),_N_E=e.O()}]);