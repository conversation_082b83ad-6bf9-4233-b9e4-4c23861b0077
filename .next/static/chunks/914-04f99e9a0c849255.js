"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[914],{8119:function(e,t,n){n.d(t,{GhostPlayButton:function(){return c},LargePlayButton:function(){return d},PlayButton:function(){return s},PrimaryPlayButton:function(){return o},SecondaryPlayButton:function(){return l},SmallPlayButton:function(){return u}});var a=n(7437),i=n(2586),r=n(1994);function s(e){let{isPlaying:t,isLoading:n,onPlay:s,onPause:o,size:l="md",variant:c="primary",disabled:d=!1,className:u}=e,f=(0,i.useTranslations)("common"),g={sm:"w-3 h-3",md:"w-4 h-4",lg:"w-6 h-6"};return(0,a.jsxs)("button",{onClick:e=>{console.log("\uD83C\uDFB5 PlayButton handleClick 被调用:",{isPlaying:t,disabled:d,isLoading:n}),e.stopPropagation(),d||n||(t?(console.log("⏸️ 调用 onPause"),o()):(console.log("▶️ 调用 onPlay"),s()))},disabled:d||n,className:(0,r.W)("relative rounded-full transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","transform hover:scale-105 active:scale-95",{sm:"w-8 h-8 p-1.5",md:"w-12 h-12 p-3",lg:"w-16 h-16 p-4"}[l],{primary:"bg-amber-500 hover:bg-amber-600 text-white shadow-lg hover:shadow-xl",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200",ghost:"bg-transparent hover:bg-gray-100 text-gray-600 dark:hover:bg-gray-800 dark:text-gray-400"}[c],u),"aria-label":f(t?"pause":"play"),title:f(t?"pause":"play"),children:[n&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:(0,r.W)("animate-spin rounded-full border-2 border-current border-t-transparent",g[l])})}),!n&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:t?(0,a.jsx)("svg",{className:g[l],fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{d:"M6 4h4v16H6V4zm8 0h4v16h-4V4z"})}):(0,a.jsx)("svg",{className:(0,r.W)(g[l],"ml-0.5"),fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{d:"M8 5v14l11-7z"})})}),t&&!n&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-full animate-ping bg-current opacity-20"})]})}function o(e){return(0,a.jsx)(s,{...e,variant:"primary"})}function l(e){return(0,a.jsx)(s,{...e,variant:"secondary"})}function c(e){return(0,a.jsx)(s,{...e,variant:"ghost"})}function d(e){return(0,a.jsx)(s,{...e,size:"lg"})}function u(e){return(0,a.jsx)(s,{...e,size:"sm"})}},4805:function(e,t,n){n.d(t,{VolumeControl:function(){return o}});var a=n(7437),i=n(2265),r=n(2586),s=n(1994);function o(e){let{volume:t,onVolumeChange:n,orientation:o="horizontal",size:l="md",showIcon:c=!0,showValue:d=!1,className:u,disabled:f=!1}=e,g=(0,r.useTranslations)("common"),[h,m]=(0,i.useState)(!1),[p,x]=(0,i.useState)(!1),v=(0,i.useRef)(null),[y,b]=(0,i.useState)(t),w=e=>{n(Math.max(0,Math.min(1,e)))},j=e=>{h&&!f&&z(e)},k=()=>{m(!1),x(!1)},z=e=>{if(!v.current)return;let t=v.current.getBoundingClientRect();w("horizontal"===o?(e.clientX-t.left)/t.width:1-(e.clientY-t.top)/t.height)};return(0,i.useEffect)(()=>{if(h)return document.addEventListener("pointermove",j),document.addEventListener("pointerup",k),()=>{document.removeEventListener("pointermove",j),document.removeEventListener("pointerup",k)}},[h]),(0,a.jsxs)("div",{className:(0,s.W)("flex items-center gap-2","vertical"===o&&"flex-col",u),children:[c&&(0,a.jsx)("button",{onClick:()=>{f||(t>0?(b(t),w(0)):w(y>0?y:.7))},disabled:f,className:(0,s.W)("p-1 rounded-md transition-colors","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-amber-500","disabled:opacity-50 disabled:cursor-not-allowed",0===t?"text-red-500":"text-gray-600 dark:text-gray-400"),"aria-label":0===t?"取消静音":"静音",title:0===t?"取消静音":"静音",children:0===t?(0,a.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"})}):t<.3?(0,a.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M7 9v6h4l5 5V4l-5 5H7z"})}):t<.7?(0,a.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"})}):(0,a.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"})})}),(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsxs)("div",{ref:v,className:(0,s.W)("relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer","focus:outline-none focus:ring-2 focus:ring-amber-500",{sm:"horizontal"===o?"h-1":"w-1 h-16",md:"horizontal"===o?"h-2":"w-2 h-20",lg:"horizontal"===o?"h-3":"w-3 h-24"}[l],f&&"opacity-50 cursor-not-allowed"),onPointerDown:e=>{f||(m(!0),x(!0),z(e),e.preventDefault())},onKeyDown:e=>{if(f)return;let n=t;switch(e.key){case"ArrowUp":case"ArrowRight":n=Math.min(1,t+.1);break;case"ArrowDown":case"ArrowLeft":n=Math.max(0,t-.1);break;case"Home":n=1;break;case"End":n=0;break;default:return}e.preventDefault(),w(n)},tabIndex:f?-1:0,role:"slider","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":Math.round(100*t),"aria-label":g("volume"),onMouseEnter:()=>x(!0),onMouseLeave:()=>!h&&x(!1),children:[(0,a.jsx)("div",{className:"absolute bg-amber-500 rounded-full transition-all duration-150",style:{["horizontal"===o?"width":"height"]:"".concat(100*t,"%"),["horizontal"===o?"height":"width"]:"100%",["vertical"===o?"bottom":"left"]:0}}),(0,a.jsx)("div",{className:(0,s.W)("absolute bg-white border-2 border-amber-500 rounded-full shadow-md","transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150","hover:scale-110",h&&"scale-125",{sm:"w-3 h-3",md:"w-4 h-4",lg:"w-5 h-5"}[l]),style:{["horizontal"===o?"left":"bottom"]:"".concat(100*t,"%"),["horizontal"===o?"top":"left"]:"50%"}})]}),(p||d)&&(0,a.jsxs)("div",{className:(0,s.W)("absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg","transform -translate-x-1/2","horizontal"===o?"-top-8 left-1/2":"-right-12 top-1/2 -translate-y-1/2"),children:[Math.round(100*t),"%"]})]}),d&&(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 min-w-[3rem] text-right",children:[Math.round(100*t),"%"]})]})}},2608:function(e,t,n){n.d(t,{MixingChannel:function(){return c}});var a=n(7437),i=n(2265),r=n(2586),s=n(1994),o=n(4805),l=n(8119);function c(e){let t,{channel:n,audio:c,isPlaying:d,isLoading:u,onPlay:f,onPause:g,onStop:h,onVolumeChange:m,onMute:p,onRemove:x,className:v}=e,y=(0,r.useTranslations)("mixing"),b=(0,r.useLocale)(),[w,j]=(0,i.useState)(!1),k=(t=c.title)[b]||t.en||Object.values(t)[0]||"";return(0,a.jsxs)("div",{className:(0,s.W)("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700","shadow-sm hover:shadow-md transition-all duration-200",!n.isActive&&"opacity-60",v),children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-100 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-lg",children:{rain:"\uD83C\uDF27️",nature:"\uD83C\uDF3F",noise:"\uD83D\uDD0A",animals:"\uD83D\uDC3E",things:"\uD83C\uDFE0",transport:"\uD83D\uDE97",urban:"\uD83C\uDFD9️",places:"\uD83D\uDCCD"}[c.category.toLowerCase()]||"\uD83C\uDFB5"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100 truncate",children:k}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:c.category})]}),d&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-1 h-3 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("div",{className:"w-1 h-4 bg-green-500 rounded-full animate-pulse",style:{animationDelay:"0.2s"}}),(0,a.jsx)("div",{className:"w-1 h-3 bg-green-500 rounded-full animate-pulse",style:{animationDelay:"0.4s"}})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[(0,a.jsx)("button",{onClick:()=>j(!w),className:"p-1 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors","aria-label":y(w?"hideDetails":"showDetails"),children:(0,a.jsx)("svg",{className:(0,s.W)("w-4 h-4 transition-transform",w&&"rotate-180"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})}),(0,a.jsx)("button",{onClick:x,className:"p-1 rounded-md text-gray-400 hover:text-red-500 transition-colors","aria-label":y("removeChannel"),children:(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.SmallPlayButton,{isPlaying:d,isLoading:u,onPlay:f,onPause:g,variant:"secondary"}),(0,a.jsx)("button",{onClick:h,disabled:!d,className:(0,s.W)("p-1.5 rounded-md transition-colors","hover:bg-gray-100 dark:hover:bg-gray-700","disabled:opacity-50 disabled:cursor-not-allowed","text-gray-600 dark:text-gray-400"),"aria-label":y("stop"),children:(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M6 6h12v12H6z"})})})]}),(0,a.jsx)("button",{onClick:p,className:(0,s.W)("p-1.5 rounded-md transition-colors","hover:bg-gray-100 dark:hover:bg-gray-700",n.isMuted?"text-red-500":"text-gray-600 dark:text-gray-400"),"aria-label":y(n.isMuted?"unmute":"mute"),children:n.isMuted?(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"})}):(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"})})})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:y("volume")}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(100*n.volume),"%"]})]}),(0,a.jsx)(o.VolumeControl,{volume:n.isMuted?0:n.volume,onVolumeChange:m,showIcon:!1,size:"sm",disabled:n.isMuted})]}),w&&(0,a.jsx)("div",{className:"pt-4 border-t border-gray-100 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-500 dark:text-gray-400",children:[y("category"),":"]}),(0,a.jsx)("span",{className:"text-gray-900 dark:text-gray-100",children:c.category})]}),c.scientificRating&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-500 dark:text-gray-400",children:[y("rating"),":"]}),(0,a.jsxs)("span",{className:"text-gray-900 dark:text-gray-100",children:["⭐ ",c.scientificRating.toFixed(1)]})]}),c.tags&&c.tags.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-gray-500 dark:text-gray-400 block mb-1",children:[y("tags"),":"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:c.tags.map(e=>(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full",children:e},e))})]})]})})]})]})}},9914:function(e,t,n){n.d(t,{MixingPanel:function(){return h}});var a=n(7437),i=n(2265),r=n(2586),s=n(5095),o=n(4820),l=n(1994),c=n(8981),d=n(2608),u=n(4805),f=n(9590);let g=[{id:"rain_heavy_rain",filename:"heavy-rain.mp3",category:"rain",title:{en:"Heavy Rain",zh:"大雨"},description:{en:"Intense rainfall for deep relaxation",zh:"强烈降雨，深度放松"},tags:["intense","powerful","relaxing"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:7.5},{id:"rain_light_rain",filename:"light-rain.mp3",category:"rain",title:{en:"Light Rain",zh:"轻雨"},description:{en:"Gentle light rain for peaceful relaxation",zh:"轻柔细雨，宁静放松"},tags:["gentle","soft","peaceful"],duration:3600,scientificRating:9.2,sleepEffectiveness:9.5,focusEffectiveness:8.8},{id:"rain_on_car_roof",filename:"rain-on-car-roof.mp3",category:"rain",title:{en:"Rain on Car Roof",zh:"雨打车顶"},description:{en:"Rain drumming on car roof for cozy atmosphere",zh:"雨滴敲打车顶，营造温馨氛围"},tags:["cozy","rhythmic","comforting"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8},{id:"rain_on_leaves",filename:"rain-on-leaves.mp3",category:"rain",title:{en:"Rain on Leaves",zh:"雨打树叶"},description:{en:"Rain falling on leaves for natural ambiance",zh:"雨滴落在树叶上，自然环境音"},tags:["natural","organic","soothing"],duration:3600,scientificRating:8.8,sleepEffectiveness:9.1,focusEffectiveness:8.3},{id:"rain_on_tent",filename:"rain-on-tent.mp3",category:"rain",title:{en:"Rain on Tent",zh:"雨打帐篷"},description:{en:"Rain pattering on tent fabric for camping atmosphere",zh:"雨滴敲打帐篷，露营氛围"},tags:["camping","adventure","cozy"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.6,focusEffectiveness:7.8},{id:"rain_on_umbrella",filename:"rain-on-umbrella.mp3",category:"rain",title:{en:"Rain on Umbrella",zh:"雨打雨伞"},description:{en:"Rain hitting umbrella for intimate sound experience",zh:"雨滴敲打雨伞，亲密的声音体验"},tags:["intimate","close","personal"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.5,focusEffectiveness:7.9},{id:"rain_on_window",filename:"rain-on-window.mp3",category:"rain",title:{en:"Rain on Window",zh:"雨打窗户"},description:{en:"Rain streaming down window glass for indoor comfort",zh:"雨水流淌在窗玻璃上，室内舒适感"},tags:["indoor","comfort","peaceful"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.9,focusEffectiveness:8.1},{id:"rain_thunder",filename:"thunder.mp3",category:"rain",title:{en:"Thunder",zh:"雷声"},description:{en:"Thunder sounds for dramatic weather atmosphere",zh:"雷声，戏剧性天气氛围"},tags:["dramatic","powerful","weather"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.5,focusEffectiveness:6.8},{id:"nature_campfire",filename:"campfire.mp3",category:"nature",title:{en:"Campfire",zh:"篝火"},description:{en:"Crackling campfire for cozy outdoor atmosphere",zh:"噼啪作响的篝火，温馨户外氛围"},tags:["cozy","warm","outdoor"],duration:3600,scientificRating:8.6,sleepEffectiveness:8.8,focusEffectiveness:8.2},{id:"nature_droplets",filename:"droplets.mp3",category:"nature",title:{en:"Water Droplets",zh:"水滴"},description:{en:"Gentle water droplets for meditation",zh:"轻柔水滴声，适合冥想"},tags:["gentle","meditation","pure"],duration:3600,scientificRating:9,sleepEffectiveness:9.3,focusEffectiveness:8.7},{id:"nature_howling_wind",filename:"howling-wind.mp3",category:"nature",title:{en:"Howling Wind",zh:"呼啸风声"},description:{en:"Wind howling through landscapes",zh:"风声呼啸穿过大地"},tags:["wind","atmospheric","dramatic"],duration:3600,scientificRating:7.9,sleepEffectiveness:7.8,focusEffectiveness:7.2},{id:"nature_jungle",filename:"jungle.mp3",category:"nature",title:{en:"Jungle",zh:"丛林"},description:{en:"Tropical jungle ambiance with wildlife",zh:"热带丛林环境音，伴有野生动物声"},tags:["tropical","wildlife","exotic"],duration:3600,scientificRating:8.4,sleepEffectiveness:8.2,focusEffectiveness:7.8},{id:"nature_river",filename:"river.mp3",category:"nature",title:{en:"River",zh:"河流"},description:{en:"Flowing river water for natural relaxation",zh:"流淌的河水，自然放松"},tags:["flowing","natural","peaceful"],duration:3600,scientificRating:9.1,sleepEffectiveness:9.4,focusEffectiveness:8.9},{id:"nature_walk_in_snow",filename:"walk-in-snow.mp3",category:"nature",title:{en:"Walk in Snow",zh:"雪中漫步"},description:{en:"Footsteps crunching in fresh snow",zh:"脚步踩在新雪上的声音"},tags:["winter","crisp","peaceful"],duration:3600,scientificRating:8.2,sleepEffectiveness:8.4,focusEffectiveness:8},{id:"nature_walk_on_gravel",filename:"walk-on-gravel.mp3",category:"nature",title:{en:"Walk on Gravel",zh:"砾石路漫步"},description:{en:"Footsteps on gravel path",zh:"脚步声在砾石路上"},tags:["walking","texture","rhythmic"],duration:3600,scientificRating:7.8,sleepEffectiveness:7.9,focusEffectiveness:7.5},{id:"nature_walk_on_leaves",filename:"walk-on-leaves.mp3",category:"nature",title:{en:"Walk on Leaves",zh:"踏叶而行"},description:{en:"Footsteps rustling through autumn leaves",zh:"脚步踩过秋叶的沙沙声"},tags:["autumn","rustling","seasonal"],duration:3600,scientificRating:8.3,sleepEffectiveness:8.5,focusEffectiveness:8.1},{id:"nature_waterfall",filename:"waterfall.mp3",category:"nature",title:{en:"Waterfall",zh:"瀑布"},description:{en:"Powerful waterfall cascading down rocks",zh:"瀑布从岩石上倾泻而下"},tags:["powerful","cascading","majestic"],duration:3600,scientificRating:8.9,sleepEffectiveness:8.7,focusEffectiveness:8.4},{id:"nature_waves",filename:"waves.mp3",category:"nature",title:{en:"Ocean Waves",zh:"海浪"},description:{en:"Gentle ocean waves lapping the shore",zh:"轻柔的海浪拍打海岸"},tags:["ocean","rhythmic","calming"],duration:3600,scientificRating:9.3,sleepEffectiveness:9.6,focusEffectiveness:9},{id:"nature_wind_in_trees",filename:"wind-in-trees.mp3",category:"nature",title:{en:"Wind in Trees",zh:"林间风声"},description:{en:"Wind rustling through tree branches",zh:"风吹过树枝的沙沙声"},tags:["rustling","peaceful","forest"],duration:3600,scientificRating:8.8,sleepEffectiveness:9,focusEffectiveness:8.6},{id:"nature_wind",filename:"wind.mp3",category:"nature",title:{en:"Wind",zh:"风声"},description:{en:"Gentle wind blowing across open spaces",zh:"轻风吹过开阔地带"},tags:["gentle","open","airy"],duration:3600,scientificRating:8.5,sleepEffectiveness:8.7,focusEffectiveness:8.3},{id:"noise_brown_noise",filename:"brown-noise.wav",category:"noise",title:{en:"Brown Noise",zh:"棕色噪音"},description:{en:"Deep brown noise for focus and relaxation",zh:"深沉的棕色噪音，专注与放松"},tags:["deep","focus","masking"],duration:3600,scientificRating:9,sleepEffectiveness:8.8,focusEffectiveness:9.2},{id:"noise_pink_noise",filename:"pink-noise.wav",category:"noise",title:{en:"Pink Noise",zh:"粉色噪音"},description:{en:"Balanced pink noise for sleep and concentration",zh:"平衡的粉色噪音，助眠与专注"},tags:["balanced","sleep","concentration"],duration:3600,scientificRating:9.2,sleepEffectiveness:9.4,focusEffectiveness:9.1},{id:"noise_white_noise",filename:"white-noise.wav",category:"noise",title:{en:"White Noise",zh:"白色噪音"},description:{en:"Classic white noise for sound masking",zh:"经典白色噪音，声音遮蔽"},tags:["classic","masking","consistent"],duration:3600,scientificRating:8.9,sleepEffectiveness:9,focusEffectiveness:9.3},{id:"animals_beehive",filename:"beehive.mp3",category:"animals",title:{en:"Beehive",zh:"蜂巢"},description:{en:"Gentle buzzing of bees in their hive",zh:"蜜蜂在蜂巢中轻柔的嗡嗡声"},tags:["buzzing","gentle","productive"],duration:3600,scientificRating:8.1,sleepEffectiveness:8.3,focusEffectiveness:8.5},{id:"animals_birds",filename:"birds.mp3",category:"animals",title:{en:"Birds",zh:"鸟鸣"},description:{en:"Cheerful bird songs for morning atmosphere",zh:"欢快的鸟鸣声，晨间氛围"},tags:["cheerful","morning","natural"],duration:3600,scientificRating:8.7,sleepEffectiveness:8.2,focusEffectiveness:8.8},{id:"animals_cat_purring",filename:"cat-purring.mp3",category:"animals",title:{en:"Cat Purring",zh:"猫咪呼噜声"},description:{en:"Soothing cat purring for comfort and relaxation",zh:"舒缓的猫咪呼噜声，带来安慰与放松"},tags:["soothing","comfort","cozy"],duration:3600,scientificRating:8.9,sleepEffectiveness:9.1,focusEffectiveness:8.4}];function h(e){let{className:t}=e,n=(0,r.useTranslations)("mixing"),[h,m]=(0,i.useState)(!1),[p,x]=(0,i.useState)("all"),{playerUI:v,mixingChannels:y,maxChannels:b,masterVolume:w,addMixingChannel:j,removeMixingChannel:k,updateChannelVolume:z,setMasterVolume:N,setMixingPanelVisible:C}=(0,c.U)(),{playChannel:E,pauseChannel:M,stopChannel:_,setChannelVolume:R,muteChannel:L,unmuteChannel:D,getChannelState:P,stopAllChannels:W,setMasterVolume:B}=(0,f.o)(),V=g.filter(e=>!y.some(t=>t.soundId===e.id));"all"===p||V.filter(e=>e.category===p),Array.from(new Set(g.map(e=>e.category))).sort(),(0,i.useCallback)(e=>{j(e)&&m(!1)},[j]);let S=(0,i.useCallback)(e=>{k(e)},[k]),H=(0,i.useCallback)(e=>{E(e)},[E]),F=(0,i.useCallback)(e=>{M(e)},[M]),A=(0,i.useCallback)(e=>{_(e)},[_]),T=(0,i.useCallback)((e,t)=>{R(e,t)},[R]),G=(0,i.useCallback)((e,t)=>{t?L(e):D(e)},[L,D]),I=(0,i.useCallback)(e=>{N(e),B(e)},[N,B]),U=e=>g.find(t=>t.id===e),O=()=>{C(!1)};return v.showMixingPanel?(0,a.jsx)(s.M,{children:(0,a.jsx)(o.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",onClick:O,children:(0,a.jsxs)(o.E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:(0,l.W)("absolute bottom-0 left-0 right-0 max-h-[80vh] overflow-y-auto","bg-white dark:bg-gray-900 rounded-t-xl shadow-2xl","border-t border-gray-200 dark:border-gray-700",t),onClick:e=>e.stopPropagation(),children:[(0,a.jsx)("div",{className:"sticky top-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:n("title")}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:n("description",{max:b})})]}),(0,a.jsx)("button",{onClick:O,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}),(0,a.jsxs)("div",{className:"p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:n("masterVolume")}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[Math.round(100*w),"%"]})]}),(0,a.jsx)(u.VolumeControl,{volume:w,onVolumeChange:I,size:"sm"})]}),0===y.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"})})}),(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:n("noChannels")}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:n("addFirstAudio")}),(0,a.jsxs)("button",{onClick:()=>m(!0),className:"inline-flex items-center gap-2 px-4 py-2 bg-amber-500 text-white rounded-lg font-medium hover:bg-amber-600 transition-colors",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),n("addAudio")]})]}):(0,a.jsxs)("div",{className:"space-y-3",children:[y.map(e=>{let t=U(e.soundId);if(!t)return null;let n=P(e.id);return(0,a.jsx)(d.MixingChannel,{channel:e,audio:t,isPlaying:n.isPlaying,isLoading:n.isLoading,onPlay:()=>H(e.id),onPause:()=>F(e.id),onStop:()=>A(e.id),onVolumeChange:t=>T(e.id,t),onMute:()=>G(e.id,!e.isMuted),onRemove:()=>S(e.id)},e.id)}),y.length<b&&(0,a.jsx)("button",{onClick:()=>m(!0),className:"w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-amber-400 hover:text-amber-600 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),n("addAudio")]})})]})]})]})})}):null}},9590:function(e,t,n){n.d(t,{o:function(){return l}});var a=n(2265),i=n(1435),r=n(8981),s=n(9280),o=n(2554);let l=()=>{let e=(0,a.useRef)(new Map),{mixingChannels:t,masterVolume:n,updateChannelVolume:l,setMasterVolume:c}=(0,r.U)(),d=(0,a.useCallback)(e=>(0,o.DA)(e.category,e.filename),[]),u=(0,a.useCallback)((t,a)=>{let r=d(a),s={howl:new i.Howl({src:[r],volume:t.volume*n*(t.isMuted?0:1),loop:!0,onload:()=>{let n=e.current.get(t.id);n&&(n.isLoading=!1,n.error=null)},onplay:()=>{let n=e.current.get(t.id);n&&(n.isPlaying=!0)},onpause:()=>{let n=e.current.get(t.id);n&&(n.isPlaying=!1)},onstop:()=>{let n=e.current.get(t.id);n&&(n.isPlaying=!1)},onloaderror:(n,a)=>{console.error("音频加载失败 (".concat(t.id,"):"),a);let i=e.current.get(t.id);i&&(i.isLoading=!1,i.error="音频加载失败")},onplayerror:(n,a)=>{console.error("音频播放失败 (".concat(t.id,"):"),a);let i=e.current.get(t.id);i&&(i.isPlaying=!1,i.error="音频播放失败")}}),isPlaying:!1,isLoading:!0,error:null};return e.current.set(t.id,s),s},[d,n]),f=(0,a.useCallback)(t=>{let n=e.current.get(t);!n||n.isLoading||n.error||n.howl.play()},[]),g=(0,a.useCallback)(t=>{let n=e.current.get(t);n&&n.howl.pause()},[]),h=(0,a.useCallback)(t=>{let n=e.current.get(t);n&&n.howl.stop()},[]),m=(0,a.useCallback)((a,i)=>{let r=e.current.get(a),s=t.find(e=>e.id===a);if(r&&s){let e=i*n*(s.isMuted?0:1);r.howl.volume(e),l(a,i)}},[t,n,l]),p=(0,a.useCallback)(t=>{let n=e.current.get(t);n&&n.howl.volume(0)},[]),x=(0,a.useCallback)(a=>{let i=e.current.get(a),r=t.find(e=>e.id===a);if(i&&r){let e=r.volume*n;i.howl.volume(e)}},[t,n]),v=(0,a.useCallback)(t=>{let n=e.current.get(t);return n?{isPlaying:n.isPlaying,isLoading:n.isLoading,error:n.error}:{isPlaying:!1,isLoading:!1,error:null}},[]),y=(0,a.useCallback)(()=>{e.current.forEach(e=>{e.howl.stop()})},[]),b=(0,a.useCallback)(n=>{c(n),e.current.forEach((e,a)=>{let i=t.find(e=>e.id===a);if(i){let t=i.volume*n*(i.isMuted?0:1);e.howl.volume(t)}})},[t,c]);return(0,a.useEffect)(()=>{let n=new Set(t.map(e=>e.id));new Set(e.current.keys()).forEach(t=>{if(!n.has(t)){let n=e.current.get(t);n&&(n.howl.unload(),e.current.delete(t))}}),t.forEach(t=>{if(!e.current.has(t.id)){let e=s.N.find(e=>e.id===t.soundId);e&&u(t,e)}})},[t,u]),(0,a.useEffect)(()=>()=>{e.current.forEach(e=>{e.howl.unload()}),e.current.clear()},[]),{playChannel:f,pauseChannel:g,stopChannel:h,setChannelVolume:m,muteChannel:p,unmuteChannel:x,getChannelState:v,stopAllChannels:y,setMasterVolume:b}}}}]);