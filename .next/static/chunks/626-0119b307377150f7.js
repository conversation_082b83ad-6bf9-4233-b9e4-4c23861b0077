"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{1994:function(t,e,i){i.d(e,{W:function(){return n}});function n(){for(var t,e,i=0,n="",r=arguments.length;i<r;i++)(t=arguments[i])&&(e=function t(e){var i,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e){if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(n=t(e[i]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n)}return r}(t))&&(n&&(n+=" "),n+=e);return n}},5095:function(t,e,i){i.d(e,{M:function(){return v}});var n=i(2265),r=i(1534);function s(){let t=(0,n.useRef)(!1);return(0,r.L)(()=>(t.current=!0,()=>{t.current=!1}),[]),t}var o=i(8345),a=i(4252),l=i(3576);class u extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function h(t){let{children:e,isPresent:i}=t,r=(0,n.useId)(),s=(0,n.useRef)(null),o=(0,n.useRef)({width:0,height:0,top:0,left:0});return(0,n.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:a}=o.current;if(i||!s.current||!t||!e)return;s.current.dataset.motionPopId=r;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(r,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            top: ").concat(n,"px !important;\n            left: ").concat(a,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[i]),n.createElement(u,{isPresent:i,childRef:s,sizeRef:o},n.cloneElement(e,{ref:s}))}let c=t=>{let{children:e,initial:i,isPresent:r,onExitComplete:s,custom:o,presenceAffectsLayout:u,mode:c}=t,p=(0,l.h)(d),m=(0,n.useId)(),f=(0,n.useMemo)(()=>({id:m,initial:i,isPresent:r,custom:o,onExitComplete:t=>{for(let e of(p.set(t,!0),p.values()))if(!e)return;s&&s()},register:t=>(p.set(t,!1),()=>p.delete(t))}),u?void 0:[r]);return(0,n.useMemo)(()=>{p.forEach((t,e)=>p.set(e,!1))},[r]),n.useEffect(()=>{r||p.size||!s||s()},[r]),"popLayout"===c&&(e=n.createElement(h,{isPresent:r},e)),n.createElement(a.O.Provider,{value:f},e)};function d(){return new Map}var p=i(8881),m=i(3223);let f=t=>t.key||"",v=t=>{var e;let{children:i,custom:a,initial:l=!0,onExitComplete:u,exitBeforeEnter:h,presenceAffectsLayout:d=!0,mode:v="sync"}=t;(0,m.k)(!h,"Replace exitBeforeEnter with mode='wait'");let g=(0,n.useContext)(p.p).forceRender||function(){let t=s(),[e,i]=(0,n.useState)(0),r=(0,n.useCallback)(()=>{t.current&&i(e+1)},[e]);return[(0,n.useCallback)(()=>o.Wi.postRender(r),[r]),e]}()[0],y=s(),x=function(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}(i),P=x,b=(0,n.useRef)(new Map).current,w=(0,n.useRef)(P),A=(0,n.useRef)(new Map).current,T=(0,n.useRef)(!0);if((0,r.L)(()=>{T.current=!1,function(t,e){t.forEach(t=>{let i=f(t);e.set(i,t)})}(x,A),w.current=P}),e=()=>{T.current=!0,A.clear(),b.clear()},(0,n.useEffect)(()=>()=>e(),[]),T.current)return n.createElement(n.Fragment,null,P.map(t=>n.createElement(c,{key:f(t),isPresent:!0,initial:!!l&&void 0,presenceAffectsLayout:d,mode:v},t)));P=[...P];let S=w.current.map(f),V=x.map(f),C=S.length;for(let t=0;t<C;t++){let e=S[t];-1!==V.indexOf(e)||b.has(e)||b.set(e,void 0)}return"wait"===v&&b.size&&(P=[]),b.forEach((t,e)=>{if(-1!==V.indexOf(e))return;let i=A.get(e);if(!i)return;let r=S.indexOf(e),s=t;s||(s=n.createElement(c,{key:f(i),isPresent:!1,onExitComplete:()=>{b.delete(e);let t=Array.from(A.keys()).filter(t=>!V.includes(t));if(t.forEach(t=>A.delete(t)),w.current=x.filter(i=>{let n=f(i);return n===e||t.includes(n)}),!b.size){if(!1===y.current)return;g(),u&&u()}},custom:a,presenceAffectsLayout:d,mode:v},i),b.set(e,s)),P.splice(r,0,s)}),P=P.map(t=>{let e=t.key;return b.has(e)?t:n.createElement(c,{key:f(t),isPresent:!0,presenceAffectsLayout:d,mode:v},t)}),n.createElement(n.Fragment,null,b.size?P:P.map(t=>(0,n.cloneElement)(t)))}},8881:function(t,e,i){i.d(e,{p:function(){return n}});let n=(0,i(2265).createContext)({})},5750:function(t,e,i){i.d(e,{_:function(){return n}});let n=(0,i(2265).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},4252:function(t,e,i){i.d(e,{O:function(){return n}});let n=(0,i(2265).createContext)(null)},8345:function(t,e,i){i.d(e,{Pn:function(){return a},Wi:function(){return o},frameData:function(){return l},S6:function(){return u}});var n=i(4439);class r{add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}constructor(){this.order=[],this.scheduled=new Set}}let s=["prepare","read","update","preRender","render","postRender"],{schedule:o,cancel:a,state:l,steps:u}=function(t,e){let i=!1,n=!0,o={delta:0,timestamp:0,isProcessing:!1},a=s.reduce((t,e)=>(t[e]=function(t){let e=new r,i=new r,n=0,s=!1,o=!1,a=new WeakSet,l={schedule:function(t){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=o&&s,u=l?e:i;return r&&a.add(t),u.add(t)&&l&&s&&(n=e.order.length),t},cancel:t=>{i.remove(t),a.delete(t)},process:r=>{if(s){o=!0;return}if(s=!0,[e,i]=[i,e],i.clear(),n=e.order.length)for(let i=0;i<n;i++){let n=e.order[i];n(r),a.has(n)&&(l.schedule(n),t())}s=!1,o&&(o=!1,l.process(r))}};return l}(()=>i=!0),t),{}),l=t=>a[t].process(o),u=()=>{let r=performance.now();i=!1,o.delta=n?1e3/60:Math.max(Math.min(r-o.timestamp,40),1),o.timestamp=r,o.isProcessing=!0,s.forEach(l),o.isProcessing=!1,i&&e&&(n=!1,t(u))},h=()=>{i=!0,n=!0,o.isProcessing||t(u)};return{schedule:s.reduce((t,e)=>{let n=a[e];return t[e]=function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return i||h(),n.schedule(t,e,r)},t},{}),cancel:t=>s.forEach(e=>a[e].cancel(t)),state:o,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.Z,!0)},4820:function(t,e,i){let n;i.d(e,{E:function(){return rt}});var r,s,o=i(2265),a=i(5750);let l=(0,o.createContext)({});var u=i(4252),h=i(1534);let c=(0,o.createContext)({strict:!1}),d=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),p="data-"+d("framerAppearId");function m(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function f(t){return"string"==typeof t||Array.isArray(t)}function v(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let g=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],y=["initial",...g];function x(t){return v(t.animate)||y.some(e=>f(t[e]))}function P(t){return!!(x(t)||t.variants)}function b(t){return Array.isArray(t)?t.join(" "):t}let w={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},A={};for(let t in w)A[t]={isEnabled:e=>w[t].some(t=>!!e[t])};var T=i(2915),S=i(8881);let V=(0,o.createContext)({}),C=Symbol.for("motionComponentSymbol"),E=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function M(t){if("string"!=typeof t||t.includes("-"));else if(E.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let D={},R=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],k=new Set(R);function L(t,e){let{layout:i,layoutId:n}=e;return k.has(t)||t.startsWith("origin")||(i||void 0!==n)&&(!!D[t]||"opacity"===t)}let j=t=>!!(t&&t.getVelocity),F={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},B=R.length;var O=i(7249);let W=(t,e)=>e&&"number"==typeof t?e.transform(t):t;var I=i(4305),U=i(7492);let N={...I.Rx,transform:Math.round},z={borderWidth:U.px,borderTopWidth:U.px,borderRightWidth:U.px,borderBottomWidth:U.px,borderLeftWidth:U.px,borderRadius:U.px,radius:U.px,borderTopLeftRadius:U.px,borderTopRightRadius:U.px,borderBottomRightRadius:U.px,borderBottomLeftRadius:U.px,width:U.px,maxWidth:U.px,height:U.px,maxHeight:U.px,size:U.px,top:U.px,right:U.px,bottom:U.px,left:U.px,padding:U.px,paddingTop:U.px,paddingRight:U.px,paddingBottom:U.px,paddingLeft:U.px,margin:U.px,marginTop:U.px,marginRight:U.px,marginBottom:U.px,marginLeft:U.px,rotate:U.RW,rotateX:U.RW,rotateY:U.RW,rotateZ:U.RW,scale:I.bA,scaleX:I.bA,scaleY:I.bA,scaleZ:I.bA,skew:U.RW,skewX:U.RW,skewY:U.RW,distance:U.px,translateX:U.px,translateY:U.px,translateZ:U.px,x:U.px,y:U.px,z:U.px,perspective:U.px,transformPerspective:U.px,opacity:I.Fq,originX:U.$C,originY:U.$C,originZ:U.px,zIndex:N,fillOpacity:I.Fq,strokeOpacity:I.Fq,numOctaves:N};function H(t,e,i,n){let{style:r,vars:s,transform:o,transformOrigin:a}=t,l=!1,u=!1,h=!0;for(let t in e){let i=e[t];if((0,O.f9)(t)){s[t]=i;continue}let n=z[t],c=W(i,n);if(k.has(t)){if(l=!0,o[t]=c,!h)continue;i!==(n.default||0)&&(h=!1)}else t.startsWith("origin")?(u=!0,a[t]=c):r[t]=c}if(!e.transform&&(l||n?r.transform=function(t,e,i,n){let{enableHardwareAcceleration:r=!0,allowTransformNone:s=!0}=e,o="";for(let e=0;e<B;e++){let i=R[e];if(void 0!==t[i]){let e=F[i]||i;o+="".concat(e,"(").concat(t[i],") ")}}return r&&!t.z&&(o+="translateZ(0)"),o=o.trim(),n?o=n(t,i?"":o):s&&i&&(o="none"),o}(t.transform,i,h,n):r.transform&&(r.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;r.transformOrigin="".concat(t," ").concat(e," ").concat(i)}}let Z=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function X(t,e,i){for(let n in e)j(e[n])||L(n,i)||(t[n]=e[n])}let $=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Y(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||$.has(t)}let q=t=>!Y(t);try{(r=require("@emotion/is-prop-valid").default)&&(q=t=>t.startsWith("on")?!Y(t):r(t))}catch(t){}function G(t,e,i){return"string"==typeof t?t:U.px.transform(e+i*t)}let K={offset:"stroke-dashoffset",array:"stroke-dasharray"},_={offset:"strokeDashoffset",array:"strokeDasharray"};function J(t,e,i,n,r){let{attrX:s,attrY:o,attrScale:a,originX:l,originY:u,pathLength:h,pathSpacing:c=1,pathOffset:d=0,...p}=e;if(H(t,p,i,r),n){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:m,style:f,dimensions:v}=t;m.transform&&(v&&(f.transform=m.transform),delete m.transform),v&&(void 0!==l||void 0!==u||f.transform)&&(f.transformOrigin=function(t,e,i){let n=G(e,t.x,t.width),r=G(i,t.y,t.height);return"".concat(n," ").concat(r)}(v,void 0!==l?l:.5,void 0!==u?u:.5)),void 0!==s&&(m.x=s),void 0!==o&&(m.y=o),void 0!==a&&(m.scale=a),void 0!==h&&function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=!(arguments.length>4)||void 0===arguments[4]||arguments[4];t.pathLength=1;let s=r?K:_;t[s.offset]=U.px.transform(-n);let o=U.px.transform(e),a=U.px.transform(i);t[s.array]="".concat(o," ").concat(a)}(m,h,c,d,!1)}let Q=()=>({...Z(),attrs:{}}),tt=t=>"string"==typeof t&&"svg"===t.toLowerCase();function te(t,e,i,n){let{style:r,vars:s}=e;for(let e in Object.assign(t.style,r,n&&n.getProjectionStyles(i)),s)t.style.setProperty(e,s[e])}let ti=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tn(t,e,i,n){for(let i in te(t,e,void 0,n),e.attrs)t.setAttribute(ti.has(i)?i:d(i),e.attrs[i])}function tr(t,e){let{style:i}=t,n={};for(let r in i)(j(i[r])||e.style&&j(e.style[r])||L(r,t))&&(n[r]=i[r]);return n}function ts(t,e){let i=tr(t,e);for(let n in t)(j(t[n])||j(e[n]))&&(i[-1!==R.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return i}function to(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return"function"==typeof e&&(e=e(void 0!==i?i:t.custom,n,r)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==i?i:t.custom,n,r)),e}var ta=i(3576);let tl=t=>Array.isArray(t),tu=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),th=t=>tl(t)?t[t.length-1]||0:t;function tc(t){let e=j(t)?t.get():t;return tu(e)?e.toValue():e}let td=t=>(e,i)=>{let n=(0,o.useContext)(l),r=(0,o.useContext)(u.O),s=()=>(function(t,e,i,n){let{scrapeMotionValuesFromProps:r,createRenderState:s,onMount:o}=t,a={latestValues:function(t,e,i,n){let r={},s=n(t,{});for(let t in s)r[t]=tc(s[t]);let{initial:o,animate:a}=t,l=x(t),u=P(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,c=(h=h||!1===o)?a:o;return c&&"boolean"!=typeof c&&!v(c)&&(Array.isArray(c)?c:[c]).forEach(e=>{let i=to(t,e);if(!i)return;let{transitionEnd:n,transition:s,...o}=i;for(let t in o){let e=o[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let t in n)r[t]=n[t]}),r}(e,i,n,r),renderState:s()};return o&&(a.mount=t=>o(e,t,a)),a})(t,e,n,r);return i?s():(0,ta.h)(s)};var tp=i(8345);let tm={useVisualState:td({scrapeMotionValuesFromProps:ts,createRenderState:Q,onMount:(t,e,i)=>{let{renderState:n,latestValues:r}=i;tp.Wi.read(()=>{try{n.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){n.dimensions={x:0,y:0,width:0,height:0}}}),tp.Wi.render(()=>{J(n,r,{enableHardwareAcceleration:!1},tt(e.tagName),t.transformTemplate),tn(e,n)})}})},tf={useVisualState:td({scrapeMotionValuesFromProps:tr,createRenderState:Z})};function tv(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let tg=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ty(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tx=t=>e=>tg(e)&&t(e,ty(e));function tP(t,e,i,n){return tv(t,e,tx(i),n)}var tb=i(332);function tw(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tA=tw("dragHorizontal"),tT=tw("dragVertical");function tS(t){let e=!1;if("y"===t)e=tT();else if("x"===t)e=tA();else{let t=tA(),i=tT();t&&i?e=()=>{t(),i()}:(t&&t(),i&&i())}return e}function tV(){let t=tS(!0);return!t||(t(),!1)}class tC{update(){}constructor(t){this.isMounted=!1,this.node=t}}function tE(t,e){let i="onHover"+(e?"Start":"End");return tP(t.current,"pointer"+(e?"enter":"leave"),(n,r)=>{if("touch"===n.pointerType||tV())return;let s=t.getProps();t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",e),s[i]&&tp.Wi.update(()=>s[i](n,r))},{passive:!t.getProps()[i]})}class tM extends tC{mount(){this.unmount=(0,tb.z)(tE(this.node,!0),tE(this.node,!1))}unmount(){}}class tD extends tC{onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,tb.z)(tv(this.node.current,"focus",()=>this.onFocus()),tv(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}let tR=(t,e)=>!!e&&(t===e||tR(t,e.parentElement));var tk=i(4439);function tL(t,e){if(!e)return;let i=new PointerEvent("pointer"+t);e(i,ty(i))}class tj extends tC{startPress(t,e){this.isPressing=!0;let{onTapStart:i,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),i&&tp.Wi.update(()=>i(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!tV()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:i}=this.node.getProps();i&&tp.Wi.update(()=>i(t,e))}mount(){let t=this.node.getProps(),e=tP(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),i=tv(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=(0,tb.z)(e,i)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}constructor(){super(...arguments),this.removeStartListeners=tk.Z,this.removeEndListeners=tk.Z,this.removeAccessibleListeners=tk.Z,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let i=this.node.getProps(),n=tP(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:i,onTapCancel:n,globalTapTarget:r}=this.node.getProps();tp.Wi.update(()=>{r||tR(this.node.current,t.target)?i&&i(t,e):n&&n(t,e)})},{passive:!(i.onTap||i.onPointerUp)}),r=tP(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(i.onTapCancel||i.onPointerCancel)});this.removeEndListeners=(0,tb.z)(n,r),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=tv(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=tv(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&tL("up",(t,e)=>{let{onTap:i}=this.node.getProps();i&&tp.Wi.update(()=>i(t,e))})}),tL("down",(t,e)=>{this.startPress(t,e)}))}),e=tv(this.node.current,"blur",()=>{this.isPressing&&tL("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=(0,tb.z)(t,e)}}}let tF=new WeakMap,tB=new WeakMap,tO=t=>{let e=tF.get(t.target);e&&e(t)},tW=t=>{t.forEach(tO)},tI={some:0,all:1};class tU extends tC{startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:tI[n]};return function(t,e,i){let n=function(t){let{root:e,...i}=t,n=e||document;tB.has(n)||tB.set(n,{});let r=tB.get(n),s=JSON.stringify(i);return r[s]||(r[s]=new IntersectionObserver(tW,{root:e,...i})),r[s]}(e);return tF.set(t,i),n.observe(t),()=>{tF.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function(t){let{viewport:e={}}=t,{viewport:i={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=>e[t]!==i[t]}(t,e))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}function tN(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function tz(t,e,i){let n=t.getProps();return to(n,e,void 0!==i?i:n.custom,function(t){let e={};return t.values.forEach((t,i)=>e[i]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,i)=>e[i]=t.getVelocity()),e}(t))}var tH=i(3223);let tZ=t=>1e3*t,tX=t=>t/1e3,t$={current:!1},tY=t=>Array.isArray(t)&&"number"==typeof t[0],tq=t=>{let[e,i,n,r]=t;return"cubic-bezier(".concat(e,", ").concat(i,", ").concat(n,", ").concat(r,")")},tG={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tq([0,.65,.55,1]),circOut:tq([.55,0,1,.45]),backIn:tq([.31,.01,.66,-.59]),backOut:tq([.33,1.53,.69,.99])},tK=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t_(t,e,i,n){if(t===e&&i===n)return tk.Z;let r=e=>(function(t,e,i,n,r){let s,o;let a=0;do(s=tK(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tK(r(t),e,n)}let tJ=t_(.42,0,1,1),tQ=t_(0,0,.58,1),t0=t_(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t3=t=>e=>1-t(1-e),t5=t=>1-Math.sin(Math.acos(t)),t4=t3(t5),t9=t2(t5),t6=t_(.33,1.53,.69,.99),t8=t3(t6),t7=t2(t8),et={linear:tk.Z,easeIn:tJ,easeInOut:t0,easeOut:tQ,circIn:t5,circInOut:t9,circOut:t4,backIn:t8,backInOut:t7,backOut:t6,anticipate:t=>(t*=2)<1?.5*t8(t):.5*(2-Math.pow(2,-10*(t-1)))},ee=t=>{if(Array.isArray(t)){(0,tH.k)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return t_(e,i,n,r)}return"string"==typeof t?((0,tH.k)(void 0!==et[t],"Invalid easing type '".concat(t,"'")),et[t]):t};var ei=i(2888),en=i(8090),er=i(6376);function es(t){let{duration:e=300,keyframes:i,times:n,ease:r="easeInOut"}=t,s=t1(r)?r.map(ee):ee(r),o={done:!1,value:i[0]},a=(n&&n.length===i.length?n:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=(0,er.Y)(0,e,n);t.push((0,en.C)(i,1,r))}}(e,t.length-1),e}(i)).map(t=>t*e),l=(0,ei.s)(a,i,{ease:Array.isArray(s)?s:i.map(()=>s||t0).splice(0,i.length-1)});return{calculatedDuration:e,next:t=>(o.value=l(t),o.done=t>=e,o)}}var eo=i(4438);function ea(t,e,i){let n=Math.max(e-5,0);return(0,eo.R)(i-t(n),e-n)}var el=i(9111);function eu(t,e){return t*Math.sqrt(1-e*e)}let eh=["duration","bounce"],ec=["stiffness","damping","mass"];function ed(t,e){return e.some(e=>void 0!==t[e])}function ep(t){let e,{keyframes:i,restDelta:n,restSpeed:r,...s}=t,o=i[0],a=i[i.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!ed(t,ec)&&ed(t,eh)){let i=function(t){let e,i,{duration:n=800,bounce:r=.25,velocity:s=0,mass:o=1}=t;(0,tH.K)(n<=tZ(10),"Spring duration must be 10 seconds or less");let a=1-r;a=(0,el.u)(.05,1,a),n=(0,el.u)(.01,10,tX(n)),a<1?(e=t=>{let e=t*a,i=e*n;return .001-(e-s)/eu(t,a)*Math.exp(-i)},i=t=>{let i=t*a*n,r=Math.pow(a,2)*Math.pow(t,2)*n,o=eu(Math.pow(t,2),a);return(i*s+s-r)*Math.exp(-i)*(-e(t)+.001>0?-1:1)/o}):(e=t=>-.001+Math.exp(-t*n)*((t-s)*n+1),i=t=>n*n*(s-t)*Math.exp(-t*n));let l=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(e,i,5/n);if(n=tZ(n),isNaN(l))return{stiffness:100,damping:10,duration:n};{let t=Math.pow(l,2)*o;return{stiffness:t,damping:2*a*Math.sqrt(o*t),duration:n}}}(t);(e={...e,...i,mass:1}).isResolvedFromDuration=!0}return e}({...s,velocity:-tX(s.velocity||0)}),f=p||0,v=h/(2*Math.sqrt(u*c)),g=a-o,y=tX(Math.sqrt(u/c)),x=5>Math.abs(g);if(r||(r=x?.01:2),n||(n=x?.005:.5),v<1){let t=eu(y,v);e=e=>a-Math.exp(-v*y*e)*((f+v*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===v)e=t=>a-Math.exp(-y*t)*(g+(f+y*g)*t);else{let t=y*Math.sqrt(v*v-1);e=e=>{let i=Math.exp(-v*y*e),n=Math.min(t*e,300);return a-i*((f+v*y*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}return{calculatedDuration:m&&d||null,next:t=>{let i=e(t);if(m)l.done=t>=d;else{let s=f;0!==t&&(s=v<1?ea(e,t,i):0);let o=Math.abs(s)<=r,u=Math.abs(a-i)<=n;l.done=o&&u}return l.value=l.done?a:i,l}}}function em(t){let e,i,{keyframes:n,velocity:r=0,power:s=.8,timeConstant:o=325,bounceDamping:a=10,bounceStiffness:l=500,modifyTarget:u,min:h,max:c,restDelta:d=.5,restSpeed:p}=t,m=n[0],f={done:!1,value:m},v=t=>void 0!==h&&t<h||void 0!==c&&t>c,g=t=>void 0===h?c:void 0===c?h:Math.abs(h-t)<Math.abs(c-t)?h:c,y=s*r,x=m+y,P=void 0===u?x:u(x);P!==x&&(y=P-m);let b=t=>-y*Math.exp(-t/o),w=t=>P+b(t),A=t=>{let e=b(t),i=w(t);f.done=Math.abs(e)<=d,f.value=f.done?P:i},T=t=>{v(f.value)&&(e=t,i=ep({keyframes:[f.value,g(f.value)],velocity:ea(w,t,f.value),damping:a,stiffness:l,restDelta:d,restSpeed:p}))};return T(0),{calculatedDuration:null,next:t=>{let n=!1;return(i||void 0!==e||(n=!0,A(t),T(t)),void 0!==e&&t>e)?i.next(t-e):(n||A(t),f)}}}let ef=t=>{let e=e=>{let{timestamp:i}=e;return t(i)};return{start:()=>tp.Wi.update(e,!0),stop:()=>(0,tp.Pn)(e),now:()=>tp.frameData.isProcessing?tp.frameData.timestamp:performance.now()}};function ev(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}let eg={decay:em,inertia:em,tween:es,keyframes:es,spring:ep};function ey(t){let e,i,n,r,s,{autoplay:o=!0,delay:a=0,driver:l=ef,keyframes:u,type:h="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:p="loop",onPlay:m,onStop:f,onComplete:v,onUpdate:g,...y}=t,x=1,P=!1,b=()=>{i=new Promise(t=>{e=t})};b();let w=eg[h]||es;w!==es&&"number"!=typeof u[0]&&(r=(0,ei.s)([0,100],u,{clamp:!1}),u=[0,100]);let A=w({...y,keyframes:u});"mirror"===p&&(s=w({...y,keyframes:[...u].reverse(),velocity:-(y.velocity||0)}));let T="idle",S=null,V=null,C=null;null===A.calculatedDuration&&c&&(A.calculatedDuration=ev(A));let{calculatedDuration:E}=A,M=1/0,D=1/0;null!==E&&(D=(M=E+d)*(c+1)-d);let R=0,k=t=>{if(null===V)return;x>0&&(V=Math.min(V,t)),x<0&&(V=Math.min(t-D/x,V));let e=(R=null!==S?S:Math.round(t-V)*x)-a*(x>=0?1:-1),i=x>=0?e<0:e>D;R=Math.max(e,0),"finished"===T&&null===S&&(R=D);let n=R,o=A;if(c){let t=Math.min(R,D)/M,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,d&&(i-=d/M)):"mirror"===p&&(o=s)),n=(0,el.u)(0,1,i)*M}let l=i?{done:!1,value:u[0]}:o.next(n);r&&(l.value=r(l.value));let{done:h}=l;i||null===E||(h=x>=0?R>=D:R<=0);let m=null===S&&("finished"===T||"running"===T&&h);return g&&g(l.value),m&&F(),l},L=()=>{n&&n.stop(),n=void 0},j=()=>{T="idle",L(),e(),b(),V=C=null},F=()=>{T="finished",v&&v(),L(),e()},B=()=>{if(P)return;n||(n=l(k));let t=n.now();m&&m(),null!==S?V=t-S:V&&"finished"!==T||(V=t),"finished"===T&&b(),C=V,S=null,T="running",n.start()};o&&B();let O={then:(t,e)=>i.then(t,e),get time(){return tX(R)},set time(newTime){R=newTime=tZ(newTime),null===S&&n&&0!==x?V=n.now()-newTime/x:S=newTime},get duration(){return tX(null===A.calculatedDuration?ev(A):A.calculatedDuration)},get speed(){return x},set speed(newSpeed){if(newSpeed===x||!n)return;x=newSpeed,O.time=tX(R)},get state(){return T},play:B,pause:()=>{T="paused",S=R},stop:()=>{P=!0,"idle"!==T&&(T="idle",f&&f(),j())},cancel:()=>{null!==C&&k(C),j()},complete:()=>{T="finished"},sample:t=>(V=0,k(t))};return O}let ex=(s=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===n&&(n=s()),n)),eP=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),eb=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&tG[e]||tY(e)||Array.isArray(e)&&e.every(t))}(e.ease),ew={type:"spring",stiffness:500,damping:25,restSpeed:10},eA=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),eT={type:"keyframes",duration:.8},eS={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},eV=(t,e)=>{let{keyframes:i}=e;return i.length>2?eT:k.has(t)?t.startsWith("scale")?eA(i[1]):ew:eS};var eC=i(5636);let eE=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eC.P.test(e)||"0"===e)&&!e.startsWith("url("));var eM=i(796);let eD=new Set(["brightness","contrast","saturate","opacity"]);function eR(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(eM.KP)||[];if(!n)return t;let r=i.replace(n,""),s=eD.has(e)?1:0;return n!==i&&(s*=100),e+"("+s+r+")"}let ek=/([a-z-]*)\(.*?\)/g,eL={...eC.P,getAnimatableNone:t=>{let e=t.match(ek);return e?e.map(eR).join(" "):t}};var ej=i(3964);let eF={...z,color:ej.$,backgroundColor:ej.$,outlineColor:ej.$,fill:ej.$,stroke:ej.$,borderColor:ej.$,borderTopColor:ej.$,borderRightColor:ej.$,borderBottomColor:ej.$,borderLeftColor:ej.$,filter:eL,WebkitFilter:eL},eB=t=>eF[t];function eO(t,e){let i=eB(t);return i!==eL&&(i=eC.P),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let eW=t=>/^0[^.\s]+$/.test(t);function eI(t,e){return t[e]||t.default||t}let eU={skipAnimations:!1},eN=function(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return r=>{let s=eI(n,t)||{},o=s.delay||n.delay||0,{elapsed:a=0}=n;a-=tZ(o);let l=function(t,e,i,n){let r,s;let o=eE(e,i);r=Array.isArray(i)?[...i]:[null,i];let a=void 0!==n.from?n.from:t.get(),l=[];for(let t=0;t<r.length;t++){var u;null===r[t]&&(r[t]=0===t?a:r[t-1]),("number"==typeof(u=r[t])?0===u:null!==u?"none"===u||"0"===u||eW(u):void 0)&&l.push(t),"string"==typeof r[t]&&"none"!==r[t]&&"0"!==r[t]&&(s=r[t])}if(o&&l.length&&s)for(let t=0;t<l.length;t++)r[l[t]]=eO(e,s);return r}(e,t,i,s),u=l[0],h=l[l.length-1],c=eE(t,u),d=eE(t,h);(0,tH.K)(c===d,"You are trying to animate ".concat(t,' from "').concat(u,'" to "').concat(h,'". ').concat(u," is not an animatable value - to enable this animation set ").concat(u," to a value animatable to ").concat(h," via the `style` property."));let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:t=>{e.set(t),s.onUpdate&&s.onUpdate(t)},onComplete:()=>{r(),s.onComplete&&s.onComplete()}};if(!function(t){let{when:e,delay:i,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:o,repeatType:a,repeatDelay:l,from:u,elapsed:h,...c}=t;return!!Object.keys(c).length}(s)&&(p={...p,...eV(t,p)}),p.duration&&(p.duration=tZ(p.duration)),p.repeatDelay&&(p.repeatDelay=tZ(p.repeatDelay)),!c||!d||t$.current||!1===s.type||eU.skipAnimations)return function(t){let{keyframes:e,delay:i,onUpdate:n,onComplete:r}=t,s=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:tk.Z,pause:tk.Z,stop:tk.Z,then:t=>(t(),Promise.resolve()),cancel:tk.Z,complete:tk.Z});return i?ey({keyframes:[0,1],duration:0,delay:i,onComplete:s}):s()}(t$.current?{...p,delay:0}:p);if(!n.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let i=function(t,e,i){let n,r,{onUpdate:s,onComplete:o,...a}=i;if(!(ex()&&eP.has(e)&&!a.repeatDelay&&"mirror"!==a.repeatType&&0!==a.damping&&"inertia"!==a.type))return!1;let l=!1,u=!1,h=()=>{r=new Promise(t=>{n=t})};h();let{keyframes:c,duration:d=300,ease:p,times:m}=a;if(eb(e,a)){let t=ey({...a,repeat:0,delay:0}),e={done:!1,value:c[0]},i=[],n=0;for(;!e.done&&n<2e4;)e=t.sample(n),i.push(e.value),n+=10;m=void 0,c=i,d=n-10,p="linear"}let f=function(t,e,i){let{delay:n=0,duration:r,repeat:s=0,repeatType:o="loop",ease:a,times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u={[e]:i};l&&(u.offset=l);let h=function t(e){if(e)return tY(e)?tq(e):Array.isArray(e)?e.map(t):tG[e]}(a);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:n,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(t.owner.current,e,c,{...a,duration:d,ease:p,times:m}),v=()=>{u=!1,f.cancel()},g=()=>{u=!0,tp.Wi.update(v),n(),h()};return f.onfinish=()=>{u||(t.set(function(t,e){let{repeat:i,repeatType:n="loop"}=e,r=i&&"loop"!==n&&i%2==1?0:t.length-1;return t[r]}(c,a)),o&&o(),g())},{then:(t,e)=>r.then(t,e),attachTimeline:t=>(f.timeline=t,f.onfinish=null,tk.Z),get time(){return tX(f.currentTime||0)},set time(newTime){f.currentTime=tZ(newTime)},get speed(){return f.playbackRate},set speed(newSpeed){f.playbackRate=newSpeed},get duration(){return tX(d)},play:()=>{l||(f.play(),(0,tp.Pn)(v))},pause:()=>f.pause(),stop:()=>{if(l=!0,"idle"===f.playState)return;let{currentTime:e}=f;if(e){let i=ey({...a,autoplay:!1});t.setWithVelocity(i.sample(e-10).value,i.sample(e).value,10)}g()},complete:()=>{u||f.finish()},cancel:g}}(e,t,p);if(i)return i}return ey(p)}};function ez(t){return!!(j(t)&&t.add)}let eH=t=>/^\-?\d*\.?\d+$/.test(t);var eZ=i(3078);let eX=t=>e=>e.test(t),e$=[I.Rx,U.px,U.aQ,U.RW,U.vw,U.vh,{test:t=>"auto"===t,parse:t=>t}],eY=t=>e$.find(eX(t)),eq=[...e$,ej.$,eC.P],eG=t=>eq.find(eX(t));function eK(t,e){let{delay:i=0,transitionOverride:n,type:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");n&&(s=n);let u=[],h=r&&t.animationState&&t.animationState.getState()[r];for(let e in a){let n=t.getValue(e),r=a[e];if(!n||void 0===r||h&&function(t,e){let{protectedKeys:i,needsAnimating:n}=t,r=i.hasOwnProperty(e)&&!0!==n[e];return n[e]=!1,r}(h,e))continue;let o={delay:i,elapsed:0,...eI(s||{},e)};if(window.HandoffAppearAnimations){let i=t.getProps()[p];if(i){let t=window.HandoffAppearAnimations(i,e,n,tp.Wi);null!==t&&(o.elapsed=t,o.isHandoff=!0)}}let c=!o.isHandoff&&!function(t,e){let i=t.get();if(!Array.isArray(e))return i!==e;for(let t=0;t<e.length;t++)if(e[t]!==i)return!0}(n,r);if("spring"===o.type&&(n.getVelocity()||o.velocity)&&(c=!1),n.animation&&(c=!1),c)continue;n.start(eN(e,n,r,t.shouldReduceMotion&&k.has(e)?{type:!1}:o));let d=n.animation;ez(l)&&(l.add(e),d.then(()=>l.remove(e))),u.push(d)}return o&&Promise.all(u).then(()=>{o&&function(t,e){let i=tz(t,e),{transitionEnd:n={},transition:r={},...s}=i?t.makeTargetAnimatable(i,!1):{};for(let e in s={...s,...n}){let i=th(s[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,eZ.BX)(i))}}(t,o)}),u}function e_(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=tz(t,e,i.custom),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(eK(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=arguments.length>5?arguments[5]:void 0,o=[],a=(t.variantChildren.size-1)*n,l=1===r?function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return t*n}:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return a-t*n};return Array.from(t.variantChildren).sort(eJ).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(e_(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:a}=r;if(!a)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function eJ(t,e){return t.sortNodePosition(e)}let eQ=[...g].reverse(),e0=g.length;function e1(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class e2 extends tC{updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),v(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(e=>{let{animation:i,options:n}=e;return function(t,e){let i,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>e_(t,e,n)));else if("string"==typeof e)i=e_(t,e,n);else{let r="function"==typeof e?tz(t,e,n.custom):e;i=Promise.all(eK(t,r,n))}return i.then(()=>t.notify("AnimationComplete",e))}(t,i,n)})),i={animate:e1(!0),whileInView:e1(),whileHover:e1(),whileTap:e1(),whileDrag:e1(),whileFocus:e1(),exit:e1()},n=!0,r=(e,i)=>{let n=tz(t,i);if(n){let{transition:t,transitionEnd:i,...r}=n;e={...e,...r,...i}}return e};function s(s,o){let a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],h=new Set,c={},d=1/0;for(let e=0;e<e0;e++){var p;let m=eQ[e],g=i[m],y=void 0!==a[m]?a[m]:l[m],x=f(y),P=m===o?g.isActive:null;!1===P&&(d=e);let b=y===l[m]&&y!==a[m]&&x;if(b&&n&&t.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...c},!g.isActive&&null===P||!y&&!g.prevProp||v(y)||"boolean"==typeof y)continue;let w=(p=g.prevProp,("string"==typeof y?y!==p:!!Array.isArray(y)&&!tN(y,p))||m===o&&g.isActive&&!b&&x||e>d&&x),A=!1,T=Array.isArray(y)?y:[y],S=T.reduce(r,{});!1===P&&(S={});let{prevResolvedValues:V={}}=g,C={...V,...S},E=t=>{w=!0,h.has(t)&&(A=!0,h.delete(t)),g.needsAnimating[t]=!0};for(let t in C){let e=S[t],i=V[t];if(!c.hasOwnProperty(t))(tl(e)&&tl(i)?tN(e,i):e===i)?void 0!==e&&h.has(t)?E(t):g.protectedKeys[t]=!0:void 0!==e?E(t):h.add(t)}g.prevProp=y,g.prevResolvedValues=S,g.isActive&&(c={...c,...S}),n&&t.blockInitialAnimation&&(w=!1),w&&(!b||A)&&u.push(...T.map(t=>({animation:t,options:{type:m,...s}})))}if(h.size){let e={};h.forEach(i=>{let n=t.getBaseTarget(i);void 0!==n&&(e[i]=n)}),u.push({animation:e})}let m=!!u.length;return n&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),n=!1,m?e(u):Promise.resolve()}return{animateChanges:s,setActive:function(e,n,r){var o;if(i[e].isActive===n)return Promise.resolve();null===(o=t.variantChildren)||void 0===o||o.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,n)}),i[e].isActive=n;let a=s(r,e);for(let t in i)i[t].protectedKeys={};return a},setAnimateFunction:function(i){e=i(t)},getState:()=>i}}(t))}}let e3=0;class e5 extends tC{update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:i}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;let r=this.node.animationState.setActive("exit",!t,{custom:null!=i?i:this.node.getProps().custom});e&&!t&&r.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}constructor(){super(...arguments),this.id=e3++}}let e4=(t,e)=>Math.abs(t-e);class e9{updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,tp.Pn)(this.updatePoint)}constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=e7(this.lastMoveEventInfo,this.history),n=null!==this.startEvent,r=(t=i.offset,e={x:0,y:0},Math.sqrt(e4(t.x,e.x)**2+e4(t.y,e.y)**2)>=3);if(!n&&!r)return;let{point:s}=i,{timestamp:o}=tp.frameData;this.history.push({...s,timestamp:o});let{onStart:a,onMove:l}=this.handlers;n||(a&&a(this.lastMoveEvent,i),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,i)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=e6(e,this.transformPagePoint),tp.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=e7("pointercancel"===t.type?this.lastMoveEventInfo:e6(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!tg(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=e6(ty(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=tp.frameData;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,e7(s,this.history)),this.removeListeners=(0,tb.z)(tP(this.contextWindow,"pointermove",this.handlePointerMove),tP(this.contextWindow,"pointerup",this.handlePointerUp),tP(this.contextWindow,"pointercancel",this.handlePointerUp))}}function e6(t,e){return e?{point:e(t.point)}:t}function e8(t,e){return{x:t.x-e.x,y:t.y-e.y}}function e7(t,e){let{point:i}=t;return{point:i,delta:e8(i,it(e)),offset:e8(i,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=it(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>tZ(.1)));)i--;if(!n)return{x:0,y:0};let s=tX(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function it(t){return t[t.length-1]}function ie(t){return t.max-t.min}function ii(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01;return Math.abs(t-e)<=i}function ir(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;t.origin=n,t.originPoint=(0,en.C)(e.min,e.max,t.origin),t.scale=ie(i)/ie(e),(ii(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=(0,en.C)(i.min,i.max,t.origin)-t.originPoint,(ii(t.translate)||isNaN(t.translate))&&(t.translate=0)}function is(t,e,i,n){ir(t.x,e.x,i.x,n?n.originX:void 0),ir(t.y,e.y,i.y,n?n.originY:void 0)}function io(t,e,i){t.min=i.min+e.min,t.max=t.min+ie(e)}function ia(t,e,i){t.min=e.min-i.min,t.max=t.min+ie(e)}function il(t,e,i){ia(t.x,e.x,i.x),ia(t.y,e.y,i.y)}function iu(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function ih(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function ic(t,e,i){return{min:id(t,e),max:id(t,i)}}function id(t,e){return"number"==typeof t?t:t[e]||0}let ip=()=>({translate:0,scale:1,origin:0,originPoint:0}),im=()=>({x:ip(),y:ip()}),iv=()=>({min:0,max:0}),ig=()=>({x:iv(),y:iv()});function iy(t){return[t("x"),t("y")]}function ix(t){let{top:e,left:i,right:n,bottom:r}=t;return{x:{min:i,max:n},y:{min:e,max:r}}}function iP(t){return void 0===t||1===t}function ib(t){let{scale:e,scaleX:i,scaleY:n}=t;return!iP(e)||!iP(i)||!iP(n)}function iw(t){return ib(t)||iA(t)||t.z||t.rotate||t.rotateX||t.rotateY}function iA(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iT(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iS(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0;t.min=iT(t.min,e,i,n,r),t.max=iT(t.max,e,i,n,r)}function iV(t,e){let{x:i,y:n}=e;iS(t.x,i.translate,i.scale,i.originPoint),iS(t.y,n.translate,n.scale,n.originPoint)}function iC(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function iE(t,e){t.min=t.min+e,t.max=t.max+e}function iM(t,e,i){let[n,r,s]=i,o=void 0!==e[s]?e[s]:.5,a=(0,en.C)(t.min,t.max,o);iS(t,e[n],e[r],a,e.scale)}let iD=["x","scaleX","originX"],iR=["y","scaleY","originY"];function ik(t,e){iM(t.x,e,iD),iM(t.y,e,iR)}function iL(t,e){return ix(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let ij=t=>{let{current:e}=t;return e?e.ownerDocument.defaultView:null},iF=new WeakMap;class iB{start(t){let{snapToCursor:e=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new e9(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ty(t,"page").point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=tS(i),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iy(t=>{let e=this.getAxisMotionValue(t).get()||0;if(U.aQ.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];if(n){let t=ie(n);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),r&&tp.Wi.update(()=>r(t,e),!1,!0);let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openGlobalLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iy(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:ij(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&tp.Wi.update(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!iO(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,e,i){let{min:n,max:r}=e;return void 0!==n&&t<n?t=i?(0,en.C)(n,t,i.min):Math.max(t,n):void 0!==r&&t>r&&(t=i?(0,en.C)(r,t,i.max):Math.min(t,r)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,r=this.constraints;e&&m(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,e){let{top:i,left:n,bottom:r,right:s}=e;return{x:iu(t.x,n,s),y:iu(t.y,i,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===t?t=0:!0===t&&(t=.35),{x:ic(t,"left","right"),y:ic(t,"top","bottom")}}(i),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&iy(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!m(e))return!1;let n=e.current;(0,tH.k)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=iL(t,i),{scroll:r}=e;return r&&(iE(n.x,r.offset.x),iE(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:ih((t=r.layout.layoutBox).x,s.x),y:ih(t.y,s.y)};if(i){let t=i(function(t){let{x:e,y:i}=t;return{top:i.min,right:e.max,bottom:i.max,left:e.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ix(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iy(o=>{if(!iO(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return i.start(eN(t,i,0,e))}stopAnimation(){iy(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iy(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iy(e=>{let{drag:i}=this.getProps();if(!iO(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-(0,en.C)(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!m(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iy(t=>{let e=this.getAxisMotionValue(t);if(e){let i=e.get();n[t]=function(t,e){let i=.5,n=ie(t),r=ie(e);return r>n?i=(0,er.Y)(e.min,e.max-n,t.min):n>r&&(i=(0,er.Y)(t.min,t.max-r,e.min)),(0,el.u)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iy(e=>{if(!iO(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set((0,en.C)(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;iF.set(this.visualElement,this);let t=tP(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();m(t)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),e();let r=tv(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:i}=t;this.isDragging&&i&&(iy(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ig(),this.visualElement=t}}function iO(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class iW extends tC{mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tk.Z}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(t){super(t),this.removeGroupControls=tk.Z,this.removeListeners=tk.Z,this.controls=new iB(t)}}let iI=t=>(e,i)=>{t&&tp.Wi.update(()=>t(e,i))};class iU extends tC{onPointerDown(t){this.session=new e9(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ij(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:iI(t),onStart:iI(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&tp.Wi.update(()=>n(t,e))}}}mount(){this.removePointerDownListener=tP(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=tk.Z}}let iN={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iz(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let iH={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!U.px.test(t))return t;t=parseFloat(t)}let i=iz(t,e.target.x),n=iz(t,e.target.y);return"".concat(i,"% ").concat(n,"%")}};class iZ extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;Object.assign(D,i$),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),iN.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,s=i.projection;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||tp.Wi.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iX(t){let[e,i]=function(){let t=(0,o.useContext)(u.O);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:i,register:n}=t,r=(0,o.useId)();return(0,o.useEffect)(()=>n(r),[]),!e&&i?[!1,()=>i&&i(r)]:[!0]}(),n=(0,o.useContext)(S.p);return o.createElement(iZ,{...t,layoutGroup:n,switchLayoutGroup:(0,o.useContext)(V),isPresent:e,safeToRemove:i})}let i$={borderRadius:{...iH,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:iH,borderTopRightRadius:iH,borderBottomLeftRadius:iH,borderBottomRightRadius:iH,boxShadow:{correct:(t,e)=>{let{treeScale:i,projectionDelta:n}=e,r=eC.P.parse(t);if(r.length>5)return t;let s=eC.P.createTransformer(t),o="number"!=typeof r[0]?1:0,a=n.x.scale*i.x,l=n.y.scale*i.y;r[0+o]/=a,r[1+o]/=l;let u=(0,en.C)(a,l,.5);return"number"==typeof r[2+o]&&(r[2+o]/=u),"number"==typeof r[3+o]&&(r[3+o]/=u),s(r)}}};var iY=i(4081);let iq=["TopLeft","TopRight","BottomLeft","BottomRight"],iG=iq.length,iK=t=>"string"==typeof t?parseFloat(t):t,i_=t=>"number"==typeof t||U.px.test(t);function iJ(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iQ=i1(0,.5,t4),i0=i1(.5,.95,tk.Z);function i1(t,e,i){return n=>n<t?0:n>e?1:i((0,er.Y)(t,e,n))}function i2(t,e){t.min=e.min,t.max=e.max}function i3(t,e){i2(t.x,e.x),i2(t.y,e.y)}function i5(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function i4(t,e,i,n,r){let[s,o,a]=i;!function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,r=arguments.length>4?arguments[4]:void 0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:t,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:t;if(U.aQ.test(e)&&(e=parseFloat(e),e=(0,en.C)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,en.C)(s.min,s.max,n);t===s&&(a-=e),t.min=i5(t.min,e,i,a,r),t.max=i5(t.max,e,i,a,r)}(t,e[s],e[o],e[a],e.scale,n,r)}let i9=["x","scaleX","originX"],i6=["y","scaleY","originY"];function i8(t,e,i,n){i4(t.x,e,i9,i?i.x:void 0,n?n.x:void 0),i4(t.y,e,i6,i?i.y:void 0,n?n.y:void 0)}function i7(t){return 0===t.translate&&1===t.scale}function nt(t){return i7(t.x)&&i7(t.y)}function ne(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function ni(t){return ie(t.x)/ie(t.y)}var nn=i(9013);class nr{add(t){(0,nn.y4)(this.members,t),t.scheduleRender()}remove(t){if((0,nn.cl)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}function ns(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y;if((r||s)&&(n="translate3d(".concat(r,"px, ").concat(s,"px, 0) ")),(1!==e.x||1!==e.y)&&(n+="scale(".concat(1/e.x,", ").concat(1/e.y,") ")),i){let{rotate:t,rotateX:e,rotateY:r}=i;t&&(n+="rotate(".concat(t,"deg) ")),e&&(n+="rotateX(".concat(e,"deg) ")),r&&(n+="rotateY(".concat(r,"deg) "))}let o=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==o||1!==a)&&(n+="scale(".concat(o,", ").concat(a,")")),n||"none"}let no=(t,e)=>t.depth-e.depth;class na{add(t){(0,nn.y4)(this.children,t),this.isDirty=!0}remove(t){(0,nn.cl)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(no),this.isDirty=!1,this.children.forEach(t)}constructor(){this.children=[],this.isDirty=!1}}let nl=["","X","Y","Z"],nu={visibility:"hidden"},nh=0,nc={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function nd(t){let{attachResizeListener:e,defaultParent:i,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}=t;return class{addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new iY.L),this.eventHandlers.get(t).add(e)}notifyListeners(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];let r=this.eventHandlers.get(t);r&&r.notify(...i)}hasListeners(t){return this.eventHandlers.has(t)}mount(t){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.root.hasTreeAnimated;if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||n)&&(this.isLayoutDirty=!0),e){let i;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=performance.now(),n=e=>{let{timestamp:r}=e,s=r-i;s>=250&&((0,tp.Pn)(n),t(s-250))};return tp.Wi.read(n,!0),()=>(0,tp.Pn)(n)}(n,0),iN.hasAnimatedSinceResize&&(iN.hasAnimatedSinceResize=!1,this.nodes.forEach(nb))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||r)&&this.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:i,hasRelativeTargetChanged:n,layout:r}=t;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||s.getDefaultTransition()||nM,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=s.getProps(),u=!this.targetLayout||!ne(this.targetLayout,r)||n,h=!i&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||i&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,h);let t={...eI(o,"layout"),onPlay:a,onComplete:l};(s.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else i||nb(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,tp.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nT),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ny);return}this.isUpdating||this.nodes.forEach(nx),this.isUpdating=!1,this.nodes.forEach(nP),this.nodes.forEach(np),this.nodes.forEach(nm),this.clearAllSnapshots();let t=performance.now();tp.frameData.delta=(0,el.u)(0,1e3/60,t-tp.frameData.timestamp),tp.frameData.timestamp=t,tp.frameData.isProcessing=!0,tp.S6.update.process(tp.frameData),tp.S6.preRender.process(tp.frameData),tp.S6.render.process(tp.frameData),tp.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(ng),this.sharedNodes.forEach(nS)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tp.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tp.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ig(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!nt(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&(e||iw(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=this.measurePageBox(),n=this.removeElementScroll(i);return e&&(n=this.removeTransform(n)),nk((t=n).x),nk(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return ig();let e=t.measureViewportBox(),{scroll:i}=this.root;return i&&(iE(e.x,i.offset.x),iE(e.y,i.offset.y)),e}removeElementScroll(t){let e=ig();i3(e,t);for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;if(n!==this.root&&r&&s.layoutScroll){if(r.isRoot){i3(e,t);let{scroll:i}=this.root;i&&(iE(e.x,-i.offset.x),iE(e.y,-i.offset.y))}iE(e.x,r.offset.x),iE(e.y,r.offset.y)}}return e}applyTransform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=ig();i3(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ik(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iw(n.latestValues)&&ik(i,n.latestValues)}return iw(this.latestValues)&&ik(i,this.latestValues),i}removeTransform(t){let e=ig();i3(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iw(i.latestValues))continue;ib(i.latestValues)&&i.updateSnapshot();let n=ig();i3(n,i.measurePageBox()),i8(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iw(this.latestValues)&&i8(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tp.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var t,e,i,n;let r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(r||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tp.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ig(),this.relativeTargetOrigin=ig(),il(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),i3(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=ig(),this.targetWithTransforms=ig()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),e=this.target,i=this.relativeTarget,n=this.relativeParent.target,io(e.x,i.x,n.x),io(e.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):i3(this.target,this.layout.layoutBox),iV(this.target,this.targetDelta)):i3(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ig(),this.relativeTargetOrigin=ig(),il(this.relativeTargetOrigin,this.target,t.target),i3(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nc.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||ib(this.parent.latestValues)||iA(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(n=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===tp.frameData.timestamp&&(n=!1),n)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;i3(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i){let n,r,s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let o=n.instance;(!o||!o.style||"contents"!==o.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ik(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iV(t,r)),s&&iw(n.latestValues)&&ik(t,n.latestValues))}e.x=iC(e.x),e.y=iC(e.y)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=im(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=im(),this.projectionDeltaWithTransform=im());let u=this.projectionTransform;is(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=ns(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nc.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t){let e,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=im();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!i;let a=ig(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nE));this.animationProgress=0,this.mixTargetDelta=i=>{let n=i/1e3;if(nV(o.x,t.x,n),nV(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m;il(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,nC(p.x,m.x,a.x,n),nC(p.y,m.y,a.y,n),e&&(u=this.relativeTarget,d=e,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),e||(e=ig()),i3(e,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=(0,en.C)(0,void 0!==i.opacity?i.opacity:1,iQ(n)),t.opacityExit=(0,en.C)(void 0!==e.opacity?e.opacity:1,0,i0(n))):s&&(t.opacity=(0,en.C)(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,n));for(let r=0;r<iG;r++){let s="border".concat(iq[r],"Radius"),o=iJ(e,s),a=iJ(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||i_(o)===i_(a)?(t[s]=Math.max((0,en.C)(iK(o),iK(a),n),0),(U.aQ.test(a)||U.aQ.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,en.C)(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,tp.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tp.Wi.update(()=>{iN.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let n=j(0)?0:(0,eZ.BX)(0);return n.start(eN("",n,1e3,i)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&nL(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||ig();let e=ie(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=ie(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}i3(e,i),ik(e,r),is(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nr),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote(){let{needsReset:t,transition:e,preserveFollowOpacity:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.rotate||i.rotateX||i.rotateY||i.rotateZ)&&(e=!0),!e)return;let n={};for(let e=0;e<nl.length;e++){let r="rotate"+nl[e];i[r]&&(n[r]=i[r],t.setStaticValue(r,0))}for(let e in t.render(),n)t.setStaticValue(e,n[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nu;let n={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=tc(null==t?void 0:t.pointerEvents)||"",n.transform=r?r(this.latestValues,""):"none",n;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tc(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!iw(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),n.transform=ns(this.projectionDeltaWithTransform,this.treeScale,o),r&&(n.transform=r(o,n.transform));let{x:a,y:l}=this.projectionDelta;for(let t in n.transformOrigin="".concat(100*a.origin,"% ").concat(100*l.origin,"% 0"),s.animationValues?n.opacity=s===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:n.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,D){if(void 0===o[t])continue;let{correct:e,applyTo:i}=D[t],r="none"===n.transform?o[t]:e(o[t],s);if(i){let t=i.length;for(let e=0;e<t;e++)n[i[e]]=r}else n[t]=r}return this.options.layoutId&&(n.pointerEvents=s===this?tc(null==t?void 0:t.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(ny),this.root.sharedNodes.clear()}constructor(t={},e=null==i?void 0:i()){this.id=nh++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nc.totalNodes=nc.resolvedTargetDeltas=nc.recalculatedProjection=0,this.nodes.forEach(nf),this.nodes.forEach(nw),this.nodes.forEach(nA),this.nodes.forEach(nv),window.MotionDebug&&window.MotionDebug.record(nc)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=e?e.root||e:this,this.path=e?[...e.path,e]:[],this.parent=e,this.depth=e?e.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new na)}}}function np(t){t.updateLayout()}function nm(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:r}=t.options,s=i.source!==t.layout.source;"size"===r?iy(t=>{let n=s?i.measuredBox[t]:i.layoutBox[t],r=ie(n);n.min=e[t].min,n.max=n.min+r}):nL(r,i.layoutBox,e)&&iy(n=>{let r=s?i.measuredBox[n]:i.layoutBox[n],o=ie(e[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=im();is(o,e,i.layoutBox);let a=im();s?is(a,t.applyTransform(n,!0),i.measuredBox):is(a,e,i.layoutBox);let l=!nt(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=ig();il(o,i.layoutBox,r.layoutBox);let a=ig();il(a,e,s.layoutBox),ne(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nf(t){nc.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nv(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function ng(t){t.clearSnapshot()}function ny(t){t.clearMeasurements()}function nx(t){t.isLayoutDirty=!1}function nP(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nb(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nw(t){t.resolveTargetDelta()}function nA(t){t.calcProjection()}function nT(t){t.resetRotation()}function nS(t){t.removeLeadSnapshot()}function nV(t,e,i){t.translate=(0,en.C)(e.translate,0,i),t.scale=(0,en.C)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nC(t,e,i,n){t.min=(0,en.C)(e.min,i.min,n),t.max=(0,en.C)(e.max,i.max,n)}function nE(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nM={duration:.45,ease:[.4,0,.1,1]},nD=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),nR=nD("applewebkit/")&&!nD("chrome/")?Math.round:tk.Z;function nk(t){t.min=nR(t.min),t.max=nR(t.max)}function nL(t,e,i){return"position"===t||"preserve-aspect"===t&&!ii(ni(e),ni(i),.2)}let nj=nd({attachResizeListener:(t,e)=>tv(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nF={current:void 0},nB=nd({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nF.current){let t=new nj({});t.mount(window),t.setOptions({layoutScroll:!0}),nF.current=t}return nF.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),nO=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nW(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;(0,tH.k)(i<=4,'Max CSS variable fallback depth detected in property "'.concat(t,'". This may indicate a circular fallback dependency.'));let[n,r]=function(t){let e=nO.exec(t);if(!e)return[,];let[,i,n]=e;return[i,n]}(t);if(!n)return;let s=window.getComputedStyle(e).getPropertyValue(n);if(s){let t=s.trim();return eH(t)?parseFloat(t):t}return(0,O.tm)(r)?nW(r,e,i+1):r}let nI=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nU=t=>nI.has(t),nN=t=>Object.keys(t).some(nU),nz=t=>t===I.Rx||t===U.px,nH=(t,e)=>parseFloat(t.split(", ")[e]),nZ=(t,e)=>(i,n)=>{let{transform:r}=n;if("none"===r||!r)return 0;let s=r.match(/^matrix3d\((.+)\)$/);if(s)return nH(s[1],e);{let e=r.match(/^matrix\((.+)\)$/);return e?nH(e[1],t):0}},nX=new Set(["x","y","z"]),n$=R.filter(t=>!nX.has(t)),nY={width:(t,e)=>{let{x:i}=t,{paddingLeft:n="0",paddingRight:r="0"}=e;return i.max-i.min-parseFloat(n)-parseFloat(r)},height:(t,e)=>{let{y:i}=t,{paddingTop:n="0",paddingBottom:r="0"}=e;return i.max-i.min-parseFloat(n)-parseFloat(r)},top:(t,e)=>{let{top:i}=e;return parseFloat(i)},left:(t,e)=>{let{left:i}=e;return parseFloat(i)},bottom:(t,e)=>{let{y:i}=t,{top:n}=e;return parseFloat(n)+(i.max-i.min)},right:(t,e)=>{let{x:i}=t,{left:n}=e;return parseFloat(n)+(i.max-i.min)},x:nZ(4,13),y:nZ(5,14)};nY.translateX=nY.x,nY.translateY=nY.y;let nq=(t,e,i)=>{let n=e.measureViewportBox(),r=getComputedStyle(e.current),{display:s}=r,o={};"none"===s&&e.setStaticValue("display",t.display||"block"),i.forEach(t=>{o[t]=nY[t](n,r)}),e.render();let a=e.measureViewportBox();return i.forEach(i=>{let n=e.getValue(i);n&&n.jump(o[i]),t[i]=nY[i](a,r)}),t},nG=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};e={...e},n={...n};let r=Object.keys(e).filter(nU),s=[],o=!1,a=[];if(r.forEach(r=>{let l;let u=t.getValue(r);if(!t.hasValue(r))return;let h=i[r],c=eY(h),d=e[r];if(tl(d)){let t=d.length,e=null===d[0]?1:0;c=eY(h=d[e]);for(let i=e;i<t&&null!==d[i];i++)l?(0,tH.k)(eY(d[i])===l,"All keyframes must be of the same type"):(l=eY(d[i]),(0,tH.k)(l===c||nz(c)&&nz(l),"Keyframes must be of the same dimension as the current value"))}else l=eY(d);if(c!==l){if(nz(c)&&nz(l)){let t=u.get();"string"==typeof t&&u.set(parseFloat(t)),"string"==typeof d?e[r]=parseFloat(d):Array.isArray(d)&&l===U.px&&(e[r]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):e[r]=c.transform(d):(o||(s=function(t){let e=[];return n$.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),o=!0),a.push(r),n[r]=void 0!==n[r]?n[r]:e[r],u.jump(d))}}),!a.length)return{target:e,transitionEnd:n};{let i=a.indexOf("height")>=0?window.pageYOffset:null,r=nq(e,t,a);return s.length&&s.forEach(e=>{let[i,n]=e;t.getValue(i).set(n)}),t.render(),T.j&&null!==i&&window.scrollTo({top:i}),{target:r,transitionEnd:n}}},nK=(t,e,i,n)=>{var r,s;let o=function(t,e,i){let{...n}=e,r=t.current;if(!(r instanceof Element))return{target:n,transitionEnd:i};for(let e in i&&(i={...i}),t.values.forEach(t=>{let e=t.get();if(!(0,O.tm)(e))return;let i=nW(e,r);i&&t.set(i)}),n){let t=n[e];if(!(0,O.tm)(t))continue;let s=nW(t,r);s&&(n[e]=s,i||(i={}),void 0===i[e]&&(i[e]=t))}return{target:n,transitionEnd:i}}(t,e,n);return e=o.target,n=o.transitionEnd,r=e,s=n,nN(r)?nG(t,r,i,s):{target:r,transitionEnd:s}},n_={current:null},nJ={current:!1},nQ=new WeakMap,n0=Object.keys(A),n1=n0.length,n2=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],n3=y.length;class n5{scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,nQ.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nJ.current||function(){if(nJ.current=!0,T.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>n_.current=t.matches;t.addListener(e),e()}else n_.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||n_.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in nQ.delete(this.current),this.projection&&this.projection.unmount(),(0,tp.Pn)(this.notifyUpdate),(0,tp.Pn)(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let i=k.has(t),n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tp.Wi.update(this.notifyUpdate,!1,!0),i&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{n(),r()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures(t,e,i,n){let r,s,{children:o,...a}=t;for(let t=0;t<n1;t++){let e=n0[t],{isEnabled:i,Feature:n,ProjectionNode:o,MeasureLayout:l}=A[e];o&&(r=o),i(a)&&(!this.features[e]&&n&&(this.features[e]=new n(this)),l&&(s=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&r){this.projection=new r(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:e,drag:i,dragConstraints:s,layoutScroll:o,layoutRoot:l}=a;this.projection.setOptions({layoutId:t,layout:e,alwaysMeasureLayout:!!i||s&&m(s),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof e?e:"both",initialPromotionConfig:n,layoutScroll:o,layoutRoot:l})}return s}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ig()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<n2.length;e++){let i=n2[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){let{willChange:n}=e;for(let r in e){let s=e[r],o=i[r];if(j(s))t.addValue(r,s),ez(n)&&n.add(r);else if(j(o))t.addValue(r,(0,eZ.BX)(s,{owner:t})),ez(n)&&n.remove(r);else if(o!==s){if(t.hasValue(r)){let e=t.getValue(r);e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(r);t.addValue(r,(0,eZ.BX)(void 0!==e?e:s,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<n3;t++){let i=y[t],n=this.props[i];(f(n)||!1===n)&&(e[i]=n)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,eZ.BX)(e,{owner:this}),this.addValue(t,i)),i}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:i}=this.props,n="string"==typeof i||"object"==typeof i?null===(e=to(this.props,i))||void 0===e?void 0:e[t]:void 0;if(i&&void 0!==n)return n;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||j(r)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new iY.L),this.events[t].add(e)}notify(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];this.events[t]&&this.events[t].notify(...i)}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,visualState:r},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tp.Wi.render(this.render,!1,!0);let{latestValues:o,renderState:a}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=s,this.isControllingVariants=x(e),this.isVariantNode=P(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(let t in u){let e=u[t];void 0!==o[t]&&j(e)&&(e.set(o[t],!1),ez(l)&&l.add(t))}}}class n4 extends n5{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,e){let{vars:i,style:n}=e;delete i[t],delete n[t]}makeTargetAnimatableFromInstance(t,e,i){let{transition:n,transitionEnd:r,...s}=t,{transformValues:o}=e,a=function(t,e,i){let n={};for(let r in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(r,e);if(void 0!==t)n[r]=t;else{let t=i.getValue(r);t&&(n[r]=t.get())}}return n}(s,n||{},this);if(o&&(r&&(r=o(r)),s&&(s=o(s)),a&&(a=o(a))),i){!function(t,e,i){var n,r;let s=Object.keys(e).filter(e=>!t.hasValue(e)),o=s.length;if(o)for(let a=0;a<o;a++){let o=s[a],l=e[o],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(r=null!==(n=i[o])&&void 0!==n?n:t.readValue(o))&&void 0!==r?r:e[o]),null!=u&&("string"==typeof u&&(eH(u)||eW(u))?u=parseFloat(u):!eG(u)&&eC.P.test(l)&&(u=eO(o,l)),t.addValue(o,(0,eZ.BX)(u,{owner:t})),void 0===i[o]&&(i[o]=u),null!==u&&t.setBaseTarget(o,u))}}(this,s,a);let t=nK(this,s,a,r);r=t.transitionEnd,s=t.target}return{transition:n,transitionEnd:r,...s}}}class n9 extends n4{readValueFromInstance(t,e){if(k.has(e)){let t=eB(e);return t&&t.default||0}{let i=window.getComputedStyle(t),n=((0,O.f9)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,e){let{transformPagePoint:i}=e;return iL(t,i)}build(t,e,i,n){H(t,e,i,n.transformTemplate)}scrapeMotionValuesFromProps(t,e){return tr(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;j(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent="".concat(t))}))}renderInstance(t,e,i,n){te(t,e,i,n)}constructor(){super(...arguments),this.type="html"}}class n6 extends n4{getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(k.has(e)){let t=eB(e);return t&&t.default||0}return e=ti.has(e)?e:d(e),t.getAttribute(e)}measureInstanceViewportBox(){return ig()}scrapeMotionValuesFromProps(t,e){return ts(t,e)}build(t,e,i,n){J(t,e,i,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,i,n){tn(t,e,i,n)}mount(t){this.isSVGTag=tt(t.tagName),super.mount(t)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}}let n8=(t,e)=>M(t)?new n6(e,{enableHardwareAcceleration:!1}):new n9(e,{enableHardwareAcceleration:!0}),n7={animation:{Feature:e2},exit:{Feature:e5},inView:{Feature:tU},tap:{Feature:tj},focus:{Feature:tD},hover:{Feature:tM},pan:{Feature:iU},drag:{Feature:iW,ProjectionNode:nB,MeasureLayout:iX},layout:{ProjectionNode:nB,MeasureLayout:iX}},rt=function(t){function e(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(t){let{preloadedFeatures:e,createVisualElement:i,useRender:n,useVisualState:r,Component:s}=t;e&&function(t){for(let e in t)A[e]={...A[e],...t[e]}}(e);let d=(0,o.forwardRef)(function(t,d){var v;let g;let y={...(0,o.useContext)(a._),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,o.useContext)(S.p).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:P}=y,w=function(t){let{initial:e,animate:i}=function(t,e){if(x(t)){let{initial:e,animate:i}=t;return{initial:!1===e||f(e)?e:void 0,animate:f(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,o.useContext)(l));return(0,o.useMemo)(()=>({initial:e,animate:i}),[b(e),b(i)])}(t),A=r(t,P);if(!P&&T.j){w.visualElement=function(t,e,i,n){let{visualElement:r}=(0,o.useContext)(l),s=(0,o.useContext)(c),d=(0,o.useContext)(u.O),m=(0,o.useContext)(a._).reducedMotion,f=(0,o.useRef)();n=n||s.renderer,!f.current&&n&&(f.current=n(t,{visualState:e,parent:r,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:m}));let v=f.current;(0,o.useInsertionEffect)(()=>{v&&v.update(i,d)});let g=(0,o.useRef)(!!(i[p]&&!window.HandoffComplete));return(0,h.L)(()=>{v&&(v.render(),g.current&&v.animationState&&v.animationState.animateChanges())}),(0,o.useEffect)(()=>{v&&(v.updateFeatures(),!g.current&&v.animationState&&v.animationState.animateChanges(),g.current&&(g.current=!1,window.HandoffComplete=!0))}),v}(s,A,y,i);let t=(0,o.useContext)(V),n=(0,o.useContext)(c).strict;w.visualElement&&(g=w.visualElement.loadFeatures(y,n,e,t))}return o.createElement(l.Provider,{value:w},g&&w.visualElement?o.createElement(g,{visualElement:w.visualElement,...y}):null,n(s,t,(v=w.visualElement,(0,o.useCallback)(t=>{t&&A.mount&&A.mount(t),v&&(t?v.mount(t):v.unmount()),d&&("function"==typeof d?d(t):m(d)&&(d.current=t))},[v])),A,P,w.visualElement))});return d[C]=s,d}(t(e,i))}if("undefined"==typeof Proxy)return e;let i=new Map;return new Proxy(e,{get:(t,n)=>(i.has(n)||i.set(n,e(n)),i.get(n))})}((t,e)=>(function(t,e,i,n){let{forwardMotionProps:r=!1}=e;return{...M(t)?tm:tf,preloadedFeatures:i,useRender:function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(e,i,n,r,s)=>{let{latestValues:a}=r,l=(M(e)?function(t,e,i,n){let r=(0,o.useMemo)(()=>{let i=Q();return J(i,e,{enableHardwareAcceleration:!1},tt(n),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};X(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e,i){let n={},r=function(t,e,i){let n=t.style||{},r={};return X(r,n,t),Object.assign(r,function(t,e,i){let{transformTemplate:n}=t;return(0,o.useMemo)(()=>{let t=Z();return H(t,e,{enableHardwareAcceleration:!i},n),Object.assign({},t.vars,t.style)},[e])}(t,e,i)),t.transformValues?t.transformValues(r):r}(t,e,i);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":"pan-".concat("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=r,n})(i,a,s,e),u={...function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(q(r)||!0===i&&Y(r)||!e&&!Y(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),...l,ref:n},{children:h}=i,c=(0,o.useMemo)(()=>j(h)?h.get():h,[h]);return(0,o.createElement)(e,{...u,children:c})}}(r),createVisualElement:n,Component:t}})(t,e,n7,n8))},7249:function(t,e,i){i.d(e,{Xp:function(){return o},f9:function(){return r},tm:function(){return s}});let n=t=>e=>"string"==typeof e&&e.startsWith(t),r=n("--"),s=n("var(--"),o=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g},9013:function(t,e,i){function n(t,e){-1===t.indexOf(e)&&t.push(e)}function r(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{cl:function(){return r},y4:function(){return n}})},9111:function(t,e,i){i.d(e,{u:function(){return n}});let n=(t,e,i)=>Math.min(Math.max(i,t),e)},3223:function(t,e,i){i.d(e,{K:function(){return r},k:function(){return s}});var n=i(4439);let r=n.Z,s=n.Z},2888:function(t,e,i){i.d(e,{s:function(){return V}});var n=i(3223),r=i(3964),s=i(9111),o=i(8090);function a(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var l=i(7325),u=i(8859),h=i(2943);let c=(t,e,i)=>{let n=t*t;return Math.sqrt(Math.max(0,i*(e*e-n)+n))},d=[l.$,u.m,h.J],p=t=>d.find(e=>e.test(t));function m(t){let e=p(t);(0,n.k)(!!e,"'".concat(t,"' is not an animatable color. Use the equivalent color code instead."));let i=e.parse(t);return e===h.J&&(i=function(t){let{hue:e,saturation:i,lightness:n,alpha:r}=t;e/=360,n/=100;let s=0,o=0,l=0;if(i/=100){let t=n<.5?n*(1+i):n+i-n*i,r=2*n-t;s=a(r,t,e+1/3),o=a(r,t,e),l=a(r,t,e-1/3)}else s=o=l=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*l),alpha:r}}(i)),i}let f=(t,e)=>{let i=m(t),n=m(e),r={...i};return t=>(r.red=c(i.red,n.red,t),r.green=c(i.green,n.green,t),r.blue=c(i.blue,n.blue,t),r.alpha=(0,o.C)(i.alpha,n.alpha,t),u.m.transform(r))};var v=i(332),g=i(5636);let y=(t,e)=>i=>"".concat(i>0?e:t);function x(t,e){return"number"==typeof t?i=>(0,o.C)(t,e,i):r.$.test(t)?f(t,e):t.startsWith("var(")?y(t,e):w(t,e)}let P=(t,e)=>{let i=[...t],n=i.length,r=t.map((t,i)=>x(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}},b=(t,e)=>{let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=x(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}},w=(t,e)=>{let i=g.P.createTransformer(e),r=(0,g.V)(t),s=(0,g.V)(e);return r.numVars===s.numVars&&r.numColors===s.numColors&&r.numNumbers>=s.numNumbers?(0,v.z)(P(r.values,s.values),i):((0,n.K)(!0,"Complex values '".concat(t,"' and '").concat(e,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.")),y(t,e))};var A=i(6376),T=i(4439);let S=(t,e)=>i=>(0,o.C)(t,e,i);function V(t,e){let{clamp:i=!0,ease:o,mixer:a}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=t.length;if((0,n.k)(l===e.length,"Both input and output ranges must be the same length"),1===l)return()=>e[0];t[0]>t[l-1]&&(t=[...t].reverse(),e=[...e].reverse());let u=function(t,e,i){let n=[],s=i||function(t){if("number"==typeof t);else if("string"==typeof t)return r.$.test(t)?f:w;else if(Array.isArray(t))return P;else if("object"==typeof t)return b;return S}(t[0]),o=t.length-1;for(let i=0;i<o;i++){let r=s(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||T.Z:e;r=(0,v.z)(t,r)}n.push(r)}return n}(e,o,a),h=u.length,c=e=>{let i=0;if(h>1)for(;i<t.length-2&&!(e<t[i+1]);i++);let n=(0,A.Y)(t[i],t[i+1],e);return u[i](n)};return i?e=>c((0,s.u)(t[0],t[l-1],e)):c}},2915:function(t,e,i){i.d(e,{j:function(){return n}});let n="undefined"!=typeof document},8090:function(t,e,i){i.d(e,{C:function(){return n}});let n=(t,e,i)=>-i*t+i*e+t},4439:function(t,e,i){i.d(e,{Z:function(){return n}});let n=t=>t},332:function(t,e,i){i.d(e,{z:function(){return r}});let n=(t,e)=>i=>e(t(i)),r=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.reduce(n)}},6376:function(t,e,i){i.d(e,{Y:function(){return n}});let n=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n}},4081:function(t,e,i){i.d(e,{L:function(){return r}});var n=i(9013);class r{add(t){return(0,n.y4)(this.subscriptions,t),()=>(0,n.cl)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}constructor(){this.subscriptions=[]}}},3576:function(t,e,i){i.d(e,{h:function(){return r}});var n=i(2265);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},1534:function(t,e,i){i.d(e,{L:function(){return r}});var n=i(2265);let r=i(2915).j?n.useLayoutEffect:n.useEffect},4438:function(t,e,i){i.d(e,{R:function(){return n}});function n(t,e){return e?1e3/e*t:0}},3078:function(t,e,i){i.d(e,{BX:function(){return u},S1:function(){return a}});var n=i(4081),r=i(4438),s=i(8345);let o=t=>!isNaN(parseFloat(t)),a={current:void 0};class l{onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.L);let i=this.events[t].add(e);return"change"===t?()=>{i(),s.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=t,this.timeDelta=i}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return a.current&&a.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?(0,r.R)(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}constructor(t,e={}){var i=this;this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=function(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];i.prev=i.current,i.current=t;let{delta:n,timestamp:r}=s.frameData;i.lastUpdated!==r&&(i.timeDelta=n,i.lastUpdated=r,s.Wi.postRender(i.scheduleVelocityCheck)),i.prev!==i.current&&i.events.change&&i.events.change.notify(i.current),i.events.velocityChange&&i.events.velocityChange.notify(i.getVelocity()),e&&i.events.renderRequest&&i.events.renderRequest.notify(i.current)},this.scheduleVelocityCheck=()=>s.Wi.postRender(this.velocityCheck),this.velocityCheck=t=>{let{timestamp:e}=t;e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=o(this.current),this.owner=e.owner}}function u(t,e){return new l(t,e)}},7325:function(t,e,i){i.d(e,{$:function(){return r}});var n=i(8859);let r={test:(0,i(2702).i)("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:n.m.transform}},2943:function(t,e,i){i.d(e,{J:function(){return a}});var n=i(4305),r=i(7492),s=i(796),o=i(2702);let a={test:(0,o.i)("hsl","hue"),parse:(0,o.d)("hue","saturation","lightness"),transform:t=>{let{hue:e,saturation:i,lightness:o,alpha:a=1}=t;return"hsla("+Math.round(e)+", "+r.aQ.transform((0,s.Nw)(i))+", "+r.aQ.transform((0,s.Nw)(o))+", "+(0,s.Nw)(n.Fq.transform(a))+")"}}},3964:function(t,e,i){i.d(e,{$:function(){return a}});var n=i(796),r=i(7325),s=i(2943),o=i(8859);let a={test:t=>o.m.test(t)||r.$.test(t)||s.J.test(t),parse:t=>o.m.test(t)?o.m.parse(t):s.J.test(t)?s.J.parse(t):r.$.parse(t),transform:t=>(0,n.HD)(t)?t:t.hasOwnProperty("red")?o.m.transform(t):s.J.transform(t)}},8859:function(t,e,i){i.d(e,{m:function(){return u}});var n=i(9111),r=i(4305),s=i(796),o=i(2702);let a=t=>(0,n.u)(0,255,t),l={...r.Rx,transform:t=>Math.round(a(t))},u={test:(0,o.i)("rgb","red"),parse:(0,o.d)("red","green","blue"),transform:t=>{let{red:e,green:i,blue:n,alpha:o=1}=t;return"rgba("+l.transform(e)+", "+l.transform(i)+", "+l.transform(n)+", "+(0,s.Nw)(r.Fq.transform(o))+")"}}},2702:function(t,e,i){i.d(e,{d:function(){return s},i:function(){return r}});var n=i(796);let r=(t,e)=>i=>!!((0,n.HD)(i)&&n.mj.test(i)&&i.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(i,e)),s=(t,e,i)=>r=>{if(!(0,n.HD)(r))return r;let[s,o,a,l]=r.match(n.KP);return{[t]:parseFloat(s),[e]:parseFloat(o),[i]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},5636:function(t,e,i){i.d(e,{P:function(){return v},V:function(){return d}});var n=i(7249),r=i(4439),s=i(3964),o=i(4305),a=i(796);let l={regex:n.Xp,countKey:"Vars",token:"${v}",parse:r.Z},u={regex:a.dA,countKey:"Colors",token:"${c}",parse:s.$.parse},h={regex:a.KP,countKey:"Numbers",token:"${n}",parse:o.Rx.parse};function c(t,e){let{regex:i,countKey:n,token:r,parse:s}=e,o=t.tokenised.match(i);o&&(t["num"+n]=o.length,t.tokenised=t.tokenised.replace(i,r),t.values.push(...o.map(s)))}function d(t){let e=t.toString(),i={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return i.value.includes("var(--")&&c(i,l),c(i,u),c(i,h),i}function p(t){return d(t).values}function m(t){let{values:e,numColors:i,numVars:n,tokenised:r}=d(t),o=e.length;return t=>{let e=r;for(let r=0;r<o;r++)e=r<n?e.replace(l.token,t[r]):r<n+i?e.replace(u.token,s.$.transform(t[r])):e.replace(h.token,(0,a.Nw)(t[r]));return e}}let f=t=>"number"==typeof t?0:t,v={test:function(t){var e,i;return isNaN(t)&&(0,a.HD)(t)&&((null===(e=t.match(a.KP))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(a.dA))||void 0===i?void 0:i.length)||0)>0},parse:p,createTransformer:m,getAnimatableNone:function(t){let e=p(t);return m(t)(e.map(f))}}},4305:function(t,e,i){i.d(e,{Fq:function(){return s},Rx:function(){return r},bA:function(){return o}});var n=i(9111);let r={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},s={...r,transform:t=>(0,n.u)(0,1,t)},o={...r,default:1}},7492:function(t,e,i){i.d(e,{$C:function(){return h},RW:function(){return s},aQ:function(){return o},px:function(){return a},vh:function(){return l},vw:function(){return u}});var n=i(796);let r=t=>({test:e=>(0,n.HD)(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>"".concat(e).concat(t)}),s=r("deg"),o=r("%"),a=r("px"),l=r("vh"),u=r("vw"),h={...o,parse:t=>o.parse(t)/100,transform:t=>o.transform(100*t)}},796:function(t,e,i){i.d(e,{HD:function(){return a},KP:function(){return r},Nw:function(){return n},dA:function(){return s},mj:function(){return o}});let n=t=>Math.round(1e5*t)/1e5,r=/(-)?([\d]*\.?[\d])+/g,s=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,o=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function a(t){return"string"==typeof t}}}]);